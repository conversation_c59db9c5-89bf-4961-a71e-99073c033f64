{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { SettingsRoutingModule } from './settings-routing.module';\nimport { AdminSettingsComponent } from './settings.component';\nimport * as i0 from \"@angular/core\";\nexport class SettingsModule {\n  static {\n    this.ɵfac = function SettingsModule_Factory(t) {\n      return new (t || SettingsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SettingsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, SettingsRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SettingsModule, {\n    declarations: [AdminSettingsComponent],\n    imports: [CommonModule, FormsModule, SettingsRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "SettingsRoutingModule", "AdminSettingsComponent", "SettingsModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\settings\\settings.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\n\nimport { SettingsRoutingModule } from './settings-routing.module';\nimport { AdminSettingsComponent } from './settings.component';\n\n@NgModule({\n  declarations: [\n    AdminSettingsComponent\n  ],\n  imports: [\n    CommonModule,\n    FormsModule,\n    SettingsRoutingModule\n  ]\n})\nexport class SettingsModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,sBAAsB,QAAQ,sBAAsB;;AAY7D,OAAM,MAAOC,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;gBALvBJ,YAAY,EACZC,WAAW,EACXC,qBAAqB;IAAA;EAAA;;;2EAGZE,cAAc;IAAAC,YAAA,GARvBF,sBAAsB;IAAAG,OAAA,GAGtBN,YAAY,EACZC,WAAW,EACXC,qBAAqB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}