import { Component, OnInit } from '@angular/core';
import { AuthService } from 'src/app/services/auth.service';
import { ToastService } from 'src/app/services/toast.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css'],
})
export class DashboardComponent implements OnInit {
  users: any[] = [];
  error = '';
  message = '';
  roles = ['student', 'teacher', 'admin'];
  loading = true;
  currentUser: any = null;
  searchTerm = '';
  filteredUsers: any[] = [];

  // Create user modal properties
  showCreateUserModal = false;
  creatingUser = false;
  newUser = {
    fullName: '',
    email: '',
    role: 'student'
  };

  // Enhanced dashboard data
  systemStats = {
    totalUsers: 0,
    activeUsers: 0,
    newUsersToday: 0,
    newUsersThisWeek: 0,
    totalProjects: 0,
    activeProjects: 0,
    totalTeams: 0,
    systemUptime: '99.9%'
  };

  // Activity logs
  recentActivities: any[] = [];

  // Filters and sorting
  selectedRole = '';
  selectedStatus = '';
  sortBy = 'fullName';
  sortOrder = 'asc';

  // Bulk operations
  selectedUsers: Set<string> = new Set();
  showBulkActions = false;

  // Advanced search
  showAdvancedSearch = false;
  advancedFilters = {
    dateFrom: '',
    dateTo: '',
    verified: '',
    hasGroup: ''
  };

  constructor(
    private authService: AuthService,
    private router: Router,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    this.loadUserData();
  }

  loadUserData(): void {
    this.loading = true;
    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');

    if (!token || !userStr) {
      this.router.navigate(['/admin/login']);
      return;
    }

    this.currentUser = JSON.parse(userStr);

    // Check if user is admin
    if (this.currentUser.role !== 'admin') {
      this.router.navigate(['/']);
      return;
    }

    this.authService.getAllUsers(token).subscribe({
      next: (res: any) => {
        this.users = res;
        this.filteredUsers = [...this.users];
        this.updateSystemStats();
        this.loadRecentActivities();
        this.loading = false;
      },
      error: (err) => {
        this.error = err.error?.message || 'Failed to fetch users';
        this.loading = false;
      },
    });
  }
  searchUsers(): void {
    this.applyAdvancedFilters();
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.applyAdvancedFilters();
  }

  onRoleChange(userId: string, newRole: string) {
    const token = localStorage.getItem('token');
    this.authService.updateUserRole(userId, newRole, token!).subscribe({
      next: (res: any) => {
        this.message = res.message;
        this.error = '';

        // Update the user in the local arrays
        const userIndex = this.users.findIndex((u) => u._id === userId);
        if (userIndex !== -1) {
          this.users[userIndex].role = newRole;
        }

        const filteredIndex = this.filteredUsers.findIndex(
          (u) => u._id === userId
        );
        if (filteredIndex !== -1) {
          this.filteredUsers[filteredIndex].role = newRole;
        }

        // Auto-hide message after 3 seconds
        setTimeout(() => {
          this.message = '';
        }, 3000);
      },
      error: (err) => {
        this.error = err.error?.message || 'Failed to update role';
        this.message = '';

        // Auto-hide error after 3 seconds
        setTimeout(() => {
          this.error = '';
        }, 3000);
      },
    });
  }
  onDeleteUser(userId: string) {
    const confirmDelete = confirm('Are you sure you want to delete this user?');
    if (!confirmDelete) return;

    const token = localStorage.getItem('token');
    this.authService.deleteUser(userId, token!).subscribe({
      next: (res: any) => {
        this.message = res.message;
        this.error = '';

        // Remove user from both arrays
        this.users = this.users.filter((u) => u._id !== userId);
        this.filteredUsers = this.filteredUsers.filter((u) => u._id !== userId);

        // Auto-hide message after 3 seconds
        setTimeout(() => {
          this.message = '';
        }, 3000);
      },
      error: (err) => {
        this.error = err.error?.message || 'Failed to delete user';
        this.message = '';

        // Auto-hide error after 3 seconds
        setTimeout(() => {
          this.error = '';
        }, 3000);
      },
    });
  }
  toggleUserActivation(userId: string, currentStatus: boolean) {
    const newStatus = !currentStatus;
    const action = newStatus ? 'activate' : 'deactivate';

    // Find the user to get their name for better messaging
    const user = this.users.find(u => u._id === userId);
    const userName = user?.fullName || user?.firstName || 'User';

    const confirmAction = confirm(
      `Are you sure you want to ${action} ${userName}?`
    );
    if (!confirmAction) return;

    const token = localStorage.getItem('token');
    this.authService.toggleUserActivation(userId, newStatus, token!).subscribe({
      next: (res: any) => {
        const statusText = newStatus ? 'activated' : 'deactivated';
        const successMessage = `${userName} has been ${statusText} successfully`;

        // Show success toast
        this.toastService.showSuccess(successMessage);

        // Clear any existing messages
        this.message = '';
        this.error = '';

        // Update user in both arrays
        const userIndex = this.users.findIndex((u) => u._id === userId);
        if (userIndex !== -1) {
          this.users[userIndex].isActive = newStatus;
        }

        const filteredIndex = this.filteredUsers.findIndex(
          (u) => u._id === userId
        );
        if (filteredIndex !== -1) {
          this.filteredUsers[filteredIndex].isActive = newStatus;
        }

        // Apply filters to refresh the view
        this.applyFilters();
      },
      error: (err) => {
        const statusText = newStatus ? 'activate' : 'deactivate';
        const errorMessage = err.error?.message || `Failed to ${statusText} ${userName}`;

        // Show error toast
        this.toastService.showError(errorMessage);

        // Clear any existing messages
        this.message = '';
        this.error = '';
      },
    });
  }
  getStudentCount(): number {
    return this.users.filter((u) => u.role === 'student').length;
  }
  getTeacherCount(): number {
    return this.users.filter((u) => u.role === 'teacher').length;
  }
  getAdminCount(): number {
    return this.users.filter((u) => u.role === 'admin').length;
  }
  getActiveCount(): number {
    return this.users.filter((u) => u.isActive !== false).length;
  }
  getInactiveCount(): number {
    return this.users.filter((u) => u.isActive === false).length;
  }

  logout() {
    this.authService.logout();
    this.router.navigate(['/admin/login']);
  }

  showUserDetails(userId: string) {
    this.router.navigate(['/admin/userdetails', userId]);
  }

  applyFilters() {
    this.applyAdvancedFilters();
  }

  // Create user modal methods
  openCreateUserModal() {
    this.showCreateUserModal = true;
    this.resetNewUserForm();
  }

  closeCreateUserModal() {
    this.showCreateUserModal = false;
    this.resetNewUserForm();
  }

  resetNewUserForm() {
    this.newUser = {
      fullName: '',
      email: '',
      role: 'student'
    };
    this.creatingUser = false;
  }

  onCreateUser(form: any) {
    if (form.invalid || this.creatingUser) {
      return;
    }

    this.creatingUser = true;
    this.error = '';
    this.message = '';

    const token = localStorage.getItem('token');

    this.authService.createUser(this.newUser, token!).subscribe({
      next: (res: any) => {
        // Show success message
        this.message = res.message;
        this.error = '';

        // Add new user to the lists
        this.users.push(res.user);
        this.filteredUsers = [...this.users];
        this.applyFilters();

        // Show success toast with credentials info
        this.toastService.showSuccess(
          `User created successfully! Login credentials sent to ${res.user.email}`
        );

        // Close modal and reset form
        this.closeCreateUserModal();

        // Auto-hide message after 5 seconds
        setTimeout(() => {
          this.message = '';
        }, 5000);
      },
      error: (err) => {
        this.error = err.error?.message || 'Failed to create user';
        this.message = '';
        this.creatingUser = false;

        // Show error toast
        this.toastService.showError(this.error);

        // Auto-hide error after 3 seconds
        setTimeout(() => {
          this.error = '';
        }, 3000);
      },
    });
  }

  // Enhanced dashboard methods
  updateSystemStats() {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    this.systemStats = {
      totalUsers: this.users.length,
      activeUsers: this.users.filter(u => u.isActive !== false).length,
      newUsersToday: this.users.filter(u => {
        const userDate = new Date(u.createdAt);
        return userDate >= today;
      }).length,
      newUsersThisWeek: this.users.filter(u => {
        const userDate = new Date(u.createdAt);
        return userDate >= weekAgo;
      }).length,
      totalProjects: 0, // Will be updated when project data is available
      activeProjects: 0,
      totalTeams: 0,
      systemUptime: '99.9%'
    };
  }

  loadRecentActivities() {
    // Mock recent activities - in real app, this would come from an API
    this.recentActivities = [
      {
        id: 1,
        type: 'user_created',
        message: 'New user registered',
        user: 'John Doe',
        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
        icon: 'fas fa-user-plus',
        color: 'text-green-600'
      },
      {
        id: 2,
        type: 'user_login',
        message: 'User logged in',
        user: 'Jane Smith',
        timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
        icon: 'fas fa-sign-in-alt',
        color: 'text-blue-600'
      },
      {
        id: 3,
        type: 'role_changed',
        message: 'User role updated to Teacher',
        user: 'Mike Johnson',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
        icon: 'fas fa-user-tag',
        color: 'text-purple-600'
      }
    ];
  }

  // Advanced filtering and sorting
  applyAdvancedFilters() {
    let filtered = [...this.users];

    // Apply role filter
    if (this.selectedRole) {
      filtered = filtered.filter(user => user.role === this.selectedRole);
    }

    // Apply status filter
    if (this.selectedStatus) {
      if (this.selectedStatus === 'active') {
        filtered = filtered.filter(user => user.isActive !== false);
      } else if (this.selectedStatus === 'inactive') {
        filtered = filtered.filter(user => user.isActive === false);
      }
    }

    // Apply verification filter
    if (this.advancedFilters.verified) {
      const isVerified = this.advancedFilters.verified === 'true';
      filtered = filtered.filter(user => user.verified === isVerified);
    }

    // Apply date range filter
    if (this.advancedFilters.dateFrom) {
      const fromDate = new Date(this.advancedFilters.dateFrom);
      filtered = filtered.filter(user => new Date(user.createdAt) >= fromDate);
    }

    if (this.advancedFilters.dateTo) {
      const toDate = new Date(this.advancedFilters.dateTo);
      filtered = filtered.filter(user => new Date(user.createdAt) <= toDate);
    }

    // Apply search term
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase().trim();
      filtered = filtered.filter(user =>
        user.fullName.toLowerCase().includes(term) ||
        user.email.toLowerCase().includes(term) ||
        user.role.toLowerCase().includes(term)
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue = a[this.sortBy];
      let bValue = b[this.sortBy];

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (this.sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    this.filteredUsers = filtered;
  }

  // Bulk operations
  toggleUserSelection(userId: string) {
    if (this.selectedUsers.has(userId)) {
      this.selectedUsers.delete(userId);
    } else {
      this.selectedUsers.add(userId);
    }
    this.showBulkActions = this.selectedUsers.size > 0;
  }

  selectAllUsers() {
    if (this.selectedUsers.size === this.filteredUsers.length) {
      this.selectedUsers.clear();
    } else {
      this.filteredUsers.forEach(user => this.selectedUsers.add(user._id));
    }
    this.showBulkActions = this.selectedUsers.size > 0;
  }

  bulkDeleteUsers() {
    if (this.selectedUsers.size === 0) return;

    const confirmDelete = confirm(`Are you sure you want to delete ${this.selectedUsers.size} users?`);
    if (!confirmDelete) return;

    const token = localStorage.getItem('token');
    const userIds = Array.from(this.selectedUsers);

    // For now, delete users one by one (in real app, implement bulk delete API)
    let deletedCount = 0;
    userIds.forEach(userId => {
      this.authService.deleteUser(userId, token!).subscribe({
        next: () => {
          deletedCount++;
          this.users = this.users.filter(u => u._id !== userId);
          if (deletedCount === userIds.length) {
            this.selectedUsers.clear();
            this.showBulkActions = false;
            this.applyAdvancedFilters();
            this.toastService.showSuccess(`${deletedCount} users deleted successfully`);
          }
        },
        error: (err) => {
          this.toastService.showError(`Failed to delete some users: ${err.error?.message}`);
        }
      });
    });
  }

  bulkChangeRole(newRole: string) {
    if (this.selectedUsers.size === 0) return;

    const confirmChange = confirm(`Are you sure you want to change the role of ${this.selectedUsers.size} users to ${newRole}?`);
    if (!confirmChange) return;

    const token = localStorage.getItem('token');
    const userIds = Array.from(this.selectedUsers);

    let updatedCount = 0;
    userIds.forEach(userId => {
      this.authService.updateUserRole(userId, newRole, token!).subscribe({
        next: () => {
          updatedCount++;
          const userIndex = this.users.findIndex(u => u._id === userId);
          if (userIndex !== -1) {
            this.users[userIndex].role = newRole;
          }
          if (updatedCount === userIds.length) {
            this.selectedUsers.clear();
            this.showBulkActions = false;
            this.applyAdvancedFilters();
            this.toastService.showSuccess(`${updatedCount} users updated successfully`);
          }
        },
        error: (err) => {
          this.toastService.showError(`Failed to update some users: ${err.error?.message}`);
        }
      });
    });
  }

  // Export functionality
  exportUsers(format: 'csv' | 'json') {
    const dataToExport = this.filteredUsers.map(user => ({
      fullName: user.fullName,
      email: user.email,
      role: user.role,
      isActive: user.isActive,
      verified: user.verified,
      createdAt: user.createdAt,
      group: user.group?.name || 'No Group'
    }));

    if (format === 'csv') {
      this.exportToCSV(dataToExport);
    } else {
      this.exportToJSON(dataToExport);
    }
  }

  private exportToCSV(data: any[]) {
    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => headers.map(header => `"${row[header]}"`).join(','))
    ].join('\n');

    this.downloadFile(csvContent, 'users.csv', 'text/csv');
  }

  private exportToJSON(data: any[]) {
    const jsonContent = JSON.stringify(data, null, 2);
    this.downloadFile(jsonContent, 'users.json', 'application/json');
  }

  private downloadFile(content: string, filename: string, contentType: string) {
    const blob = new Blob([content], { type: contentType });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  // Utility methods
  toggleAdvancedSearch() {
    this.showAdvancedSearch = !this.showAdvancedSearch;
  }

  clearAdvancedFilters() {
    this.advancedFilters = {
      dateFrom: '',
      dateTo: '',
      verified: '',
      hasGroup: ''
    };
    this.selectedRole = '';
    this.selectedStatus = '';
    this.applyAdvancedFilters();
  }

  setSortBy(field: string) {
    if (this.sortBy === field) {
      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortBy = field;
      this.sortOrder = 'asc';
    }
    this.applyAdvancedFilters();
  }
}
