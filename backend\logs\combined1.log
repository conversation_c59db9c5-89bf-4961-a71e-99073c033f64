{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 10:28:14"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 10:28:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:31:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 25ms","timestamp":"2025-05-30 10:31:02"}
{"level":"http","message":"POST / 200 - 71ms","timestamp":"2025-05-30 10:31:02"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:31:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 10:31:02","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 66ms","timestamp":"2025-05-30 10:31:02"}
{"level":"http","message":"POST / 200 - 81ms","timestamp":"2025-05-30 10:31:02"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 57ms","timestamp":"2025-05-30 10:31:02"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 7ms","timestamp":"2025-05-30 10:31:02"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:31:02"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:31:02"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:31:02"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:31:02"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 16ms","timestamp":"2025-05-30 10:31:21"}
{"level":"http","message":"GET /users 200 - 138ms","timestamp":"2025-05-30 10:31:23"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:31:32","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 2ms","timestamp":"2025-05-30 10:31:32"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 10:31:32"}
{"level":"error","message":"PUT /users/68038b686229308a4e6b3e81/activation 500 - 44ms","timestamp":"2025-05-30 10:31:36"}
{"level":"http","message":"DELETE /users/68038b686229308a4e6b3e81 200 - 24ms","timestamp":"2025-05-30 10:31:39"}
{"level":"http","message":"GET /profile 200 - 23ms","timestamp":"2025-05-30 10:31:57"}
{"level":"http","message":"GET /users 200 - 44ms","timestamp":"2025-05-30 10:31:57"}
{"level":"http","message":"GET /user/68344d3c7230af7cda872c1e 200 - 52ms","timestamp":"2025-05-30 10:31:59"}
{"level":"http","message":"GET /user/68344d3c7230af7cda872c1e 200 - 14ms","timestamp":"2025-05-30 10:32:00"}
{"level":"http","message":"GET / 304 - 14ms","timestamp":"2025-05-30 10:32:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:32:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:32:02"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 10:32:02"}
{"level":"http","message":"GET /users 304 - 54ms","timestamp":"2025-05-30 10:32:06"}
{"level":"http","message":"GET / 304 - 6ms","timestamp":"2025-05-30 10:32:13"}
{"level":"http","message":"GET / 200 - 10ms","timestamp":"2025-05-30 10:32:15"}
{"level":"http","message":"GET / 304 - 16ms","timestamp":"2025-05-30 10:32:15"}
{"level":"http","message":"GET /getev 200 - 25ms","timestamp":"2025-05-30 10:32:17"}
{"level":"http","message":"GET / 200 - 9ms","timestamp":"2025-05-30 10:32:20"}
{"level":"http","message":"GET / 304 - 15ms","timestamp":"2025-05-30 10:32:20"}
{"level":"http","message":"GET / 200 - 19ms","timestamp":"2025-05-30 10:32:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:32:32","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 2ms","timestamp":"2025-05-30 10:32:32"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 10:32:32"}
{"level":"http","message":"GET /users 304 - 59ms","timestamp":"2025-05-30 10:32:33"}
{"level":"http","message":"GET / 304 - 4ms","timestamp":"2025-05-30 10:32:34"}
{"level":"http","message":"GET / 304 - 6ms","timestamp":"2025-05-30 10:32:34"}
{"level":"http","message":"GET / 304 - 9ms","timestamp":"2025-05-30 10:32:34"}
{"level":"http","message":"GET /getev 304 - 7ms","timestamp":"2025-05-30 10:32:34"}
{"level":"http","message":"GET /users 304 - 49ms","timestamp":"2025-05-30 10:32:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:33:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:33:02"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 10:33:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:33:32","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:33:32"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 10:33:32"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:35:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:35:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 10:35:01"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 10:35:53"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 10:35:53"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:35:54"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:35:54"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:35:54"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:35:54"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:35:54"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:36:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 7ms","timestamp":"2025-05-30 10:36:01"}
{"level":"http","message":"POST / 200 - 29ms","timestamp":"2025-05-30 10:36:01"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 10:36:28"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 10:36:28"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:36:29"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:36:29"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:36:29"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:36:29"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:36:29"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 10:36:41"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 10:36:41"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:36:42"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:36:42"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:36:42"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:36:42"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:36:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:37:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 9ms","timestamp":"2025-05-30 10:37:01"}
{"level":"http","message":"POST / 200 - 34ms","timestamp":"2025-05-30 10:37:01"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 10:37:07"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 10:37:07"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:37:08"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:08"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:09"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:09"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:09"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 10:37:19"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 10:37:19"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:37:19"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:19"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:19"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:19"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:19"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 10:37:33"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 10:37:33"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:37:34"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:34"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:34"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:35"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:35"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:37:49"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:37:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 18ms","timestamp":"2025-05-30 10:37:53"}
{"level":"http","message":"POST / 200 - 48ms","timestamp":"2025-05-30 10:37:53"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:37:53"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:53"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:53"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:53"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:53"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 28ms","timestamp":"2025-05-30 10:37:53"}
{"level":"http","message":"GET /users 304 - 49ms","timestamp":"2025-05-30 10:37:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:38:23","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:38:23"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 10:38:23"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:38:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:38:53"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 10:38:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:39:23","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:39:23"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 10:39:23"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:39:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:39:53"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 10:39:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:40:23","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:40:23"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 10:40:23"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:40:33"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:40:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:40:35"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 10:40:35"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:40:35"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:40:35"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:40:35"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:40:35"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:40:35"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 6ms","timestamp":"2025-05-30 10:40:35"}
{"level":"http","message":"GET /users 304 - 23ms","timestamp":"2025-05-30 10:40:35"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:40:47"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:40:49","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:40:49"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 10:40:49"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:40:49"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:40:49"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:40:49"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:40:49"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:40:49"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 10ms","timestamp":"2025-05-30 10:40:49"}
{"level":"http","message":"GET /users 304 - 36ms","timestamp":"2025-05-30 10:40:49"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 10:41:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:41:19","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 2ms","timestamp":"2025-05-30 10:41:19"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 10:41:19"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:41:49","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:41:49"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 10:41:49"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 10:44:42"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 10:44:42"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:44:44"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:44:44"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:44:44"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:44:44"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:44:44"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:44:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:44:47","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 27ms","timestamp":"2025-05-30 10:44:47"}
{"level":"http","message":"POST / 200 - 81ms","timestamp":"2025-05-30 10:44:47"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:44:47"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:44:47"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:44:47"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:44:47"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:44:47"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 125ms","timestamp":"2025-05-30 10:44:47"}
{"level":"http","message":"GET /users 304 - 146ms","timestamp":"2025-05-30 10:44:47"}
{"level":"http","message":"PUT /users/6834468be9e36ff27bd2a6d1/activation 200 - 76ms","timestamp":"2025-05-30 10:44:59"}
{"level":"http","message":"PUT /users/6834468be9e36ff27bd2a6d1/activation 200 - 31ms","timestamp":"2025-05-30 10:45:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:45:17","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 3ms","timestamp":"2025-05-30 10:45:17"}
{"level":"http","message":"POST / 200 - 13ms","timestamp":"2025-05-30 10:45:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:45:47","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:45:47"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 10:45:47"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:46:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:46:16","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:46:16"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 10:46:16"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:46:16"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:16"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:16"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:16"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:16"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 8ms","timestamp":"2025-05-30 10:46:16"}
{"level":"http","message":"GET /users 200 - 28ms","timestamp":"2025-05-30 10:46:16"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:46:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:46:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:46:30"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 10:46:30"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:46:30"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:30"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:30"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:30"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:30"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 14ms","timestamp":"2025-05-30 10:46:30"}
{"level":"http","message":"GET /users 304 - 29ms","timestamp":"2025-05-30 10:46:30"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:46:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:46:47","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:46:47"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 10:46:47"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:46:47"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:47"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:47"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:47"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:47"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 8ms","timestamp":"2025-05-30 10:46:47"}
{"level":"http","message":"GET /users 304 - 27ms","timestamp":"2025-05-30 10:46:47"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:47:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:47:09","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:47:09"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 10:47:09"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:47:09"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:47:09"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:47:09"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:47:09"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:47:09"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 18ms","timestamp":"2025-05-30 10:47:09"}
{"level":"http","message":"GET /users 304 - 56ms","timestamp":"2025-05-30 10:47:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:47:39","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 2ms","timestamp":"2025-05-30 10:47:39"}
{"level":"http","message":"POST / 200 - 17ms","timestamp":"2025-05-30 10:47:39"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:48:04"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:48:06","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:48:06"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 10:48:06"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:48:06"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:06"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:06"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:06"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:06"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 11ms","timestamp":"2025-05-30 10:48:06"}
{"level":"http","message":"GET /users 304 - 31ms","timestamp":"2025-05-30 10:48:06"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:48:24"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:48:26","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:48:26"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 10:48:26"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:48:26"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:26"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:26"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:26"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:26"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 10ms","timestamp":"2025-05-30 10:48:26"}
{"level":"http","message":"GET /users 304 - 32ms","timestamp":"2025-05-30 10:48:26"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:48:38"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:48:39","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:48:39"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 10:48:39"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:48:39"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:39"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:39"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:39"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:39"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 7ms","timestamp":"2025-05-30 10:48:39"}
{"level":"http","message":"GET /users 304 - 33ms","timestamp":"2025-05-30 10:48:39"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:49:09","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:49:09"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 10:49:09"}
{"level":"error","message":"POST /users 500 - 402ms","timestamp":"2025-05-30 10:49:19"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:49:32"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:49:33","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:49:33"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 10:49:33"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:49:33"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:49:33"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:49:33"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:49:33"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:49:33"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 17ms","timestamp":"2025-05-30 10:49:33"}
{"level":"http","message":"GET /users 304 - 57ms","timestamp":"2025-05-30 10:49:33"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:50:03","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:50:03"}
{"level":"http","message":"POST / 200 - 18ms","timestamp":"2025-05-30 10:50:03"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:50:33","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:50:33"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 10:50:33"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:50:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:50:46","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:50:46"}
{"level":"http","message":"POST / 200 - 14ms","timestamp":"2025-05-30 10:50:46"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:50:46"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:50:46"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:50:46"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:50:46"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:50:46"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 15ms","timestamp":"2025-05-30 10:50:46"}
{"level":"http","message":"GET /users 304 - 50ms","timestamp":"2025-05-30 10:50:46"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:50:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:51:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:51:00"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 10:51:00"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:51:00"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:51:00"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:51:00"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:51:00"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:51:00"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 9ms","timestamp":"2025-05-30 10:51:00"}
{"level":"http","message":"GET /users 304 - 28ms","timestamp":"2025-05-30 10:51:00"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:51:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:51:13","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:51:13"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 10:51:13"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:51:13"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:51:13"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:51:13"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:51:13"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:51:13"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 9ms","timestamp":"2025-05-30 10:51:13"}
{"level":"http","message":"GET /users 304 - 18ms","timestamp":"2025-05-30 10:51:13"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:51:43","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:51:43"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 10:51:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:52:13","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:52:13"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 10:52:13"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:52:43","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:52:43"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 10:52:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:53:13","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:53:13"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 10:53:13"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:53:43","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:53:43"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 10:53:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:55:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:55:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 10:55:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:56:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:56:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 10:56:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:57:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:57:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 10:57:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:58:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:58:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 10:58:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:59:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:59:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 10:59:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:00:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:00:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:00:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:01:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:01:01"}
{"level":"http","message":"POST / 200 - 29ms","timestamp":"2025-05-30 11:01:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:02:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:02:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:02:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:03:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:03:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:03:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:04:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:04:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:04:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:05:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:05:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 11:05:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:06:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:06:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:06:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:07:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:07:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 11:07:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:08:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:08:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:08:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:09:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:09:01"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 11:09:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:10:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:10:01"}
{"level":"http","message":"POST / 200 - 13ms","timestamp":"2025-05-30 11:10:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:11:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:11:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 11:11:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:12:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:12:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:12:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:13:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:13:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:13:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:14:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:14:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:14:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:15:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:15:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:15:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:16:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:16:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:16:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:17:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:17:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:17:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:18:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:18:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:18:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:19:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:19:01"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 11:19:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:20:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:20:01"}
{"level":"http","message":"POST / 200 - 14ms","timestamp":"2025-05-30 11:20:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:21:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:21:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:21:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:22:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:22:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:22:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:23:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:23:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:23:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:24:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:24:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:24:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:25:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:25:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 11:25:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:26:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:26:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:26:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:27:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:27:01"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 11:27:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:28:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:28:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:28:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:29:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:29:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:29:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:30:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:30:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:30:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:31:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:31:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 11:31:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:32:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:32:01"}
{"level":"http","message":"POST / 200 - 13ms","timestamp":"2025-05-30 11:32:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:33:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:33:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 11:33:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:34:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:34:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 11:34:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:35:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:35:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:35:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:36:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:36:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:36:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:37:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:37:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 11:37:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:38:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:38:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:38:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:39:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:39:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 11:39:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:40:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:40:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 11:40:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:41:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:41:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 11:41:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:42:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:42:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 11:42:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:43:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:43:01"}
{"level":"http","message":"POST / 200 - 13ms","timestamp":"2025-05-30 11:43:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:44:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:44:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 11:44:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:45:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:45:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 11:45:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:46:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:46:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 11:46:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:47:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:47:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 11:47:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:48:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:48:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 11:48:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:49:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:49:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 11:49:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:50:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:50:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:50:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:51:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:51:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 11:51:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:52:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:52:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 11:52:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:53:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:53:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 11:53:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:54:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:54:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 11:54:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:55:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:55:01"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 11:55:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:56:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:56:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:56:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:57:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:57:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:57:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:58:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:58:01"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 11:58:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:59:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:59:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 11:59:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:00:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:00:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:00:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:01:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:01:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:01:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:02:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:02:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:02:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:03:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:03:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:03:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:04:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:04:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:04:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:05:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:05:01"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 12:05:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:06:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:06:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:06:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:07:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:07:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:07:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:08:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:08:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:08:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:09:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:09:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:09:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:10:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:10:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:10:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:11:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:11:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:11:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:12:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:12:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:12:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:13:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:13:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 12:13:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:14:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:14:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:14:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:15:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:15:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:15:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:16:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:16:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 12:16:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:17:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:17:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 12:17:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:18:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:18:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 12:18:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:19:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:19:01"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 12:19:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:20:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:20:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:20:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:21:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:21:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:21:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:22:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:22:01"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 12:22:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:23:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:23:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 12:23:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:24:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:24:01"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 12:24:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:25:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:25:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:25:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:26:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:26:01"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 12:26:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:27:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:27:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:27:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:28:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:28:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:28:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:29:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:29:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:29:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:30:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:30:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:30:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:31:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:31:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:31:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:32:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:32:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:32:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:33:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:33:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:33:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:34:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:34:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:34:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:35:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:35:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 12:35:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:36:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:36:01"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 12:36:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:37:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:37:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 12:37:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:38:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:38:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:38:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:39:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:39:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:39:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:40:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:40:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:40:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:41:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:41:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:41:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:42:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:42:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 12:42:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:43:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:43:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:43:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:44:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:44:02"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:44:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:45:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:45:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:45:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:46:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:46:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:46:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:47:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:47:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:47:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:48:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:48:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:48:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:49:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:49:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:49:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:50:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:50:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:50:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:51:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:51:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:51:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:52:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:52:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 12:52:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:53:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:53:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:53:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:54:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:54:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:54:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:55:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:55:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:55:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:56:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:56:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:56:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:57:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:57:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:57:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:58:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:58:01"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 12:58:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:59:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:59:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:59:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:00:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:00:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:00:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:01:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:01:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:01:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:02:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:02:01"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 13:02:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:03:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:03:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:03:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:04:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:04:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:04:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:05:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:05:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:05:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:06:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:06:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:06:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:07:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:07:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:07:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:08:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:08:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:08:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:09:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:09:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:09:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:10:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:10:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 13:10:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:11:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:11:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:11:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:12:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:12:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:12:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:13:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:13:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 13:13:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:14:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:14:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:14:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:15:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:15:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:15:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:16:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:16:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:16:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:17:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:17:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:17:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:18:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:18:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:18:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:19:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:19:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:19:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:20:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:20:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:20:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:21:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:21:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:21:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:22:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:22:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:22:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:23:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:23:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:23:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:24:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:24:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:24:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:25:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:25:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:25:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:26:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:26:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:26:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:27:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:27:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:27:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:28:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:28:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 13:28:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:29:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:29:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:29:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:30:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:30:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:30:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:31:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:31:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:31:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:32:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:32:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:32:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:33:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:33:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:33:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:34:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:34:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:34:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:35:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:35:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 13:35:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:36:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:36:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 13:36:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:37:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:37:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:37:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:38:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:38:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:38:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:39:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:39:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:39:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:40:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:40:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:40:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:41:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:41:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:41:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:42:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:42:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:42:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:43:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:43:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:43:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:44:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:44:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:44:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:45:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:45:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:45:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:46:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:46:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 13:46:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:47:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:47:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:47:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:48:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:48:01"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 13:48:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:49:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:49:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:49:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:50:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:50:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:50:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:51:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:51:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:51:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:52:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:52:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:52:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:53:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:53:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:53:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:54:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:54:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:54:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:55:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:55:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:55:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:56:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:56:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:56:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:57:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:57:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:57:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:58:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:58:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:58:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:59:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:59:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:59:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:00:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:00:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 14:00:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:01:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:01:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 14:01:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:02:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:02:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 14:02:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:03:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:03:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 14:03:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:04:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:04:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 14:04:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:05:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 14:05:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 14:05:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:06:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 14:06:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 14:06:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:07:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:07:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 14:07:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:08:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:08:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 14:08:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:09:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 14:09:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 14:09:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:10:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:10:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 14:10:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:11:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:11:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 14:11:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:12:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:12:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 14:12:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:12:49","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:12:49"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 14:12:49"}
{"level":"http","message":"GET /getev 304 - 8ms","timestamp":"2025-05-30 14:12:50"}
{"level":"http","message":"PUT /logout?secret=2cinfo1&client=esprit 200 - 19ms","timestamp":"2025-05-30 14:12:56"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 14:13:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:13:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:13:00"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 14:13:00"}
{"level":"warn","message":"Unauthorized connection attempt - No token provided","timestamp":"2025-05-30 14:13:00"}
{"level":"warn","message":"Unauthorized connection attempt - No token provided","timestamp":"2025-05-30 14:13:04"}
{"level":"warn","message":"Unauthorized connection attempt - No token provided","timestamp":"2025-05-30 14:13:08"}
{"level":"warn","message":"Unauthorized connection attempt - No token provided","timestamp":"2025-05-30 14:13:14"}
{"level":"warn","message":"POST /signup 400 - 7ms","timestamp":"2025-05-30 14:13:14"}
{"level":"warn","message":"Unauthorized connection attempt - No token provided","timestamp":"2025-05-30 14:13:23"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:13:30"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:13:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:13:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 7ms","timestamp":"2025-05-30 14:13:30"}
{"level":"http","message":"POST / 200 - 18ms","timestamp":"2025-05-30 14:13:30"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:15:42"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:15:42"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:26:01"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:26:01"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:26:54"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:26:54"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:27:06"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:27:07"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:27:07"}
{"level":"error","message":"[Database] MongoDB connection error: bad auth : authentication failed","mongoURI":"mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0","stack":"MongoServerError: bad auth : authentication failed\n    at Connection.sendCommand (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connection.js:299:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Connection.command (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connection.js:327:26)\n    at async continueScramConversation (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:131:15)\n    at async executeScram (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:80:5)\n    at async ScramSHA1.auth (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:39:16)\n    at async performInitialHandshake (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connect.js:104:13)\n    at async connect (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connect.js:24:9)","timestamp":"2025-05-30 14:27:07"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:27:18"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:27:18"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:27:18"}
{"level":"error","message":"[Database] MongoDB connection error: bad auth : authentication failed","mongoURI":"mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0","stack":"MongoServerError: bad auth : authentication failed\n    at Connection.sendCommand (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connection.js:299:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Connection.command (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connection.js:327:26)\n    at async continueScramConversation (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:131:15)\n    at async executeScram (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:80:5)\n    at async ScramSHA1.auth (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:39:16)\n    at async performInitialHandshake (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connect.js:104:13)\n    at async connect (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connect.js:24:9)","timestamp":"2025-05-30 14:27:18"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:27:30"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:27:30"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:27:30"}
{"level":"error","message":"[Database] MongoDB connection error: bad auth : authentication failed","mongoURI":"mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0","stack":"MongoServerError: bad auth : authentication failed\n    at Connection.sendCommand (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connection.js:299:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Connection.command (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connection.js:327:26)\n    at async continueScramConversation (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:131:15)\n    at async executeScram (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:80:5)\n    at async ScramSHA1.auth (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:39:16)\n    at async performInitialHandshake (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connect.js:104:13)\n    at async connect (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connect.js:24:9)","timestamp":"2025-05-30 14:27:31"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:27:57"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:27:57"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:27:57"}
{"level":"error","message":"[Database] MongoDB connection error: bad auth : authentication failed","mongoURI":"mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0","stack":"MongoServerError: bad auth : authentication failed\n    at Connection.sendCommand (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connection.js:299:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Connection.command (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connection.js:327:26)\n    at async continueScramConversation (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:131:15)\n    at async executeScram (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:80:5)\n    at async ScramSHA1.auth (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:39:16)\n    at async performInitialHandshake (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connect.js:104:13)\n    at async connect (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connect.js:24:9)","timestamp":"2025-05-30 14:27:57"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:31:36"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:31:37"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:31:37"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-00.ciy4zys.mongodb.net","timestamp":"2025-05-30 14:31:37"}
{"level":"http","message":"POST /signup 201 - 1532ms","timestamp":"2025-05-30 14:32:25"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:34:33"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:34:33"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:34:33"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-02.ciy4zys.mongodb.net","timestamp":"2025-05-30 14:34:34"}
{"level":"http","message":"POST /login 200 - 341ms","timestamp":"2025-05-30 14:39:27"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 60ms","timestamp":"2025-05-30 14:39:32"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:46:12"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:46:12"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:46:12"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-02.ciy4zys.mongodb.net","timestamp":"2025-05-30 14:46:15"}
{"level":"http","message":"POST /login 200 - 283ms","timestamp":"2025-05-30 14:46:21"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 63ms","timestamp":"2025-05-30 14:46:24"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:48:19"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:48:19"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:48:19"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-00.ciy4zys.mongodb.net","timestamp":"2025-05-30 14:48:20"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:48:40"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:48:40"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:48:40"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-00.ciy4zys.mongodb.net","timestamp":"2025-05-30 14:48:41"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:48:55"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:48:55"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:48:55"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-01.ciy4zys.mongodb.net","timestamp":"2025-05-30 14:48:56"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:49:19"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:49:20"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:49:20"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-01.ciy4zys.mongodb.net","timestamp":"2025-05-30 14:49:20"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:49:36"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:49:36"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:49:36"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-00.ciy4zys.mongodb.net","timestamp":"2025-05-30 14:49:37"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:49:48"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:49:48"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:49:48"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-02.ciy4zys.mongodb.net","timestamp":"2025-05-30 14:49:51"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:50:37","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 30ms","timestamp":"2025-05-30 14:50:37"}
{"level":"http","message":"POST / 200 - 65ms","timestamp":"2025-05-30 14:50:37"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b466666b7ee01e8b5bd2","timestamp":"2025-05-30 14:50:37"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:50:37"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:50:37"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:50:37"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:50:37"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 14:50:41","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 113ms","timestamp":"2025-05-30 14:50:41"}
{"level":"http","message":"GraphQL anonymous completed in 70ms","timestamp":"2025-05-30 14:50:41"}
{"level":"http","message":"POST / 200 - 87ms","timestamp":"2025-05-30 14:50:41"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 64ms","timestamp":"2025-05-30 14:50:41"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 49ms","timestamp":"2025-05-30 14:50:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:51:07","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 14:51:07"}
{"level":"http","message":"POST / 200 - 34ms","timestamp":"2025-05-30 14:51:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:51:37","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:51:37"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 14:51:37"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 14:51:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:51:59","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 14:51:59"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 14:51:59"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b466666b7ee01e8b5bd2","timestamp":"2025-05-30 14:51:59"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:51:59"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:51:59"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:51:59"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:51:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 14:51:59","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 62ms","timestamp":"2025-05-30 14:51:59"}
{"level":"http","message":"GraphQL anonymous completed in 49ms","timestamp":"2025-05-30 14:51:59"}
{"level":"http","message":"POST / 200 - 54ms","timestamp":"2025-05-30 14:51:59"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 54ms","timestamp":"2025-05-30 14:51:59"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 66ms","timestamp":"2025-05-30 14:52:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:52:29","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:52:29"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 14:52:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:52:59","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 14:52:59"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 14:52:59"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 14:53:03"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:53:04","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 14:53:04"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 14:53:04"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b466666b7ee01e8b5bd2","timestamp":"2025-05-30 14:53:04"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:53:04"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:53:04"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:53:04"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:53:04"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 14:53:04","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 93ms","timestamp":"2025-05-30 14:53:04"}
{"level":"http","message":"GraphQL anonymous completed in 78ms","timestamp":"2025-05-30 14:53:04"}
{"level":"http","message":"POST / 200 - 82ms","timestamp":"2025-05-30 14:53:04"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 60ms","timestamp":"2025-05-30 14:53:04"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 60ms","timestamp":"2025-05-30 14:53:04"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 14:53:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:53:26","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:53:26"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 14:53:26"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b466666b7ee01e8b5bd2","timestamp":"2025-05-30 14:53:26"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:53:26"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:53:26"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:53:26"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:53:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 14:53:27","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 56ms","timestamp":"2025-05-30 14:53:27"}
{"level":"http","message":"GraphQL anonymous completed in 164ms","timestamp":"2025-05-30 14:53:27"}
{"level":"http","message":"POST / 200 - 168ms","timestamp":"2025-05-30 14:53:27"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 132ms","timestamp":"2025-05-30 14:53:27"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 57ms","timestamp":"2025-05-30 14:53:27"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:53:57","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:53:57"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 14:53:57"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:54:27","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:54:27"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 14:54:27"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:54:41"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:54:41"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:54:41"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-01.ciy4zys.mongodb.net","timestamp":"2025-05-30 14:54:42"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b466666b7ee01e8b5bd2","timestamp":"2025-05-30 14:54:42"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:54:42"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:54:42"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:54:42"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:54:42"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:54:55"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:54:55"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:54:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:54:57","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 14ms","timestamp":"2025-05-30 14:54:57"}
{"level":"http","message":"POST / 200 - 32ms","timestamp":"2025-05-30 14:54:57"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b466666b7ee01e8b5bd2","timestamp":"2025-05-30 14:54:58"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:54:58"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:54:58"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:54:58"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:54:58"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-01.ciy4zys.mongodb.net","timestamp":"2025-05-30 14:54:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:55:27","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:55:27"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 14:55:27"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:55:57","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 14:55:57"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 14:55:57"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:57:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:57:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 14:57:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:58:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 14:58:01"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 14:58:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:59:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:59:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 14:59:01"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 14:59:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:59:19","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 2ms","timestamp":"2025-05-30 14:59:19"}
{"level":"http","message":"POST / 200 - 15ms","timestamp":"2025-05-30 14:59:19"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b466666b7ee01e8b5bd2","timestamp":"2025-05-30 14:59:19"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:59:19"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:59:19"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:59:19"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:59:19"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 14:59:19","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 92ms","timestamp":"2025-05-30 14:59:19"}
{"level":"http","message":"POST / 200 - 104ms","timestamp":"2025-05-30 14:59:19"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 80ms","timestamp":"2025-05-30 14:59:19"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 52ms","timestamp":"2025-05-30 14:59:19"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 50ms","timestamp":"2025-05-30 14:59:19"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:59:49","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 14:59:49"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 14:59:49"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:00:19","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:00:19"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 15:00:19"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:00:49","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:00:49"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 15:00:49"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:01:19","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:01:19"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:01:19"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 15:01:34"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b466666b7ee01e8b5bd2","timestamp":"2025-05-30 15:01:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:01:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 15:01:35"}
{"level":"http","message":"POST / 200 - 23ms","timestamp":"2025-05-30 15:01:35"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 15:01:35"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 15:01:35"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 15:01:35"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 15:01:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 15:01:35","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 51ms","timestamp":"2025-05-30 15:01:35"}
{"level":"http","message":"POST / 200 - 56ms","timestamp":"2025-05-30 15:01:35"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 58ms","timestamp":"2025-05-30 15:01:35"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 66ms","timestamp":"2025-05-30 15:01:35"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 58ms","timestamp":"2025-05-30 15:01:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:02:05","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 15:02:05"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 15:02:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:02:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:02:35"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:02:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:03:05","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:03:05"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:03:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:03:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:03:35"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 15:03:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:04:05","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:04:05"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 15:04:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:05:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 15:05:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 15:05:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:06:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 15:06:01"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 15:06:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:07:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 15:07:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 15:07:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:08:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:08:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:08:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:09:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:09:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:09:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:10:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:10:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:10:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:11:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 15:11:01"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 15:11:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:12:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:12:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:12:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:13:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:13:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:13:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:14:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 15:14:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:14:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:15:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 15:15:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:15:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:16:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 15:16:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:16:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:17:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:17:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:17:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:18:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 15:18:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:18:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:19:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:19:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:19:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:20:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 15:20:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:20:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:21:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:21:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:21:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:22:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:22:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:22:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:23:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:23:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:23:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:24:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:24:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:24:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:25:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 15:25:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:25:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:26:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:26:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:26:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:27:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:27:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:27:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:28:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:28:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:28:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:29:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:29:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:29:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:30:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 15:30:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:30:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:31:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:31:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:31:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:32:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 15:32:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:32:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:33:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:33:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:33:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:34:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:34:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:34:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:35:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:35:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:35:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:36:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:36:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:36:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:37:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:37:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 15:37:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:38:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 15:38:01"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 15:38:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:39:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:39:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 15:39:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:40:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:40:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:40:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:41:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 15:41:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 15:41:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:42:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:42:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 15:42:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:43:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:43:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 15:43:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:44:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 15:44:01"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 15:44:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:45:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:45:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 15:45:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:46:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:46:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:46:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:47:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:47:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:47:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:48:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:48:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:48:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:49:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:49:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:49:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:50:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:50:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 15:50:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:51:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 15:51:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:51:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:52:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:52:02"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 15:52:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:52:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:52:40"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:52:40"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 15:52:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:52:44","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:52:44"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 15:52:44"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b466666b7ee01e8b5bd2","timestamp":"2025-05-30 15:52:44"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 15:52:44"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 15:52:44"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 15:52:44"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 15:52:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 15:52:44","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 68ms","timestamp":"2025-05-30 15:52:44"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 45ms","timestamp":"2025-05-30 15:52:44"}
{"level":"http","message":"GraphQL anonymous completed in 144ms","timestamp":"2025-05-30 15:52:44"}
{"level":"http","message":"POST / 200 - 168ms","timestamp":"2025-05-30 15:52:44"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 64ms","timestamp":"2025-05-30 15:52:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:53:14","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 15:53:14"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 15:53:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:53:44","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:53:44"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 15:53:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:54:14","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:54:14"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:54:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:54:44","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 15:54:44"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:54:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:55:14","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:55:14"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:55:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:56:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:56:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:56:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:56:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:56:41"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 15:56:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:56:44","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:56:44"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:56:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:57:14","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:57:14"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:57:14"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 15:57:24"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:57:25","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:57:25"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:57:25"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b466666b7ee01e8b5bd2","timestamp":"2025-05-30 15:57:25"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 15:57:25"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 15:57:25"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 15:57:25"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 15:57:25"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 64ms","timestamp":"2025-05-30 15:57:27"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 15:57:27","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 72ms","timestamp":"2025-05-30 15:57:27"}
{"level":"http","message":"GraphQL anonymous completed in 107ms","timestamp":"2025-05-30 15:57:28"}
{"level":"http","message":"POST / 200 - 115ms","timestamp":"2025-05-30 15:57:28"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 56ms","timestamp":"2025-05-30 15:57:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:57:29","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:57:29"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 15:57:29"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b466666b7ee01e8b5bd2","timestamp":"2025-05-30 15:57:29"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 15:57:29"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 15:57:29"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 15:57:29"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 15:57:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 15:57:29","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 60ms","timestamp":"2025-05-30 15:57:29"}
{"level":"http","message":"GraphQL anonymous completed in 98ms","timestamp":"2025-05-30 15:57:29"}
{"level":"http","message":"POST / 200 - 103ms","timestamp":"2025-05-30 15:57:29"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 63ms","timestamp":"2025-05-30 15:57:29"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 59ms","timestamp":"2025-05-30 15:57:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 15:57:37","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 117ms","timestamp":"2025-05-30 15:57:37"}
{"level":"http","message":"POST / 200 - 127ms","timestamp":"2025-05-30 15:57:37"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkNotificationsAsRead($notificationIds: [ID!]!) {\n  markNotificationsAsRead(notificationIds: $notificationIds) {\n    success\n    readCount\n    remainingCount\n  }\n}","timestamp":"2025-05-30 15:57:39","variables":{"notificationIds":["6839c3aeb3d00b25cf26afea"]}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-30 15:57:39","variables":{}}
{"level":"warn","message":"[MessageService] getBulkUnreadCounts called with empty conversationIds","timestamp":"2025-05-30 15:57:39"}
{"level":"http","message":"GraphQL anonymous completed in 60ms","timestamp":"2025-05-30 15:57:39"}
{"level":"http","message":"POST / 200 - 64ms","timestamp":"2025-05-30 15:57:39"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation CreateConversation($userId: ID!) {\n  createConversation(userId: $userId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-30 15:57:39","variables":{"userId":"6839c1fcb3d00b25cf26afa7"}}
{"level":"http","message":"GraphQL anonymous completed in 207ms","timestamp":"2025-05-30 15:57:39"}
{"level":"http","message":"POST / 200 - 216ms","timestamp":"2025-05-30 15:57:39"}
{"level":"info","message":"[MessageService] New conversation created: 6839c763f13093fc2d5643c5","timestamp":"2025-05-30 15:57:39"}
{"level":"http","message":"GraphQL anonymous completed in 337ms","timestamp":"2025-05-30 15:57:40"}
{"level":"http","message":"POST / 200 - 346ms","timestamp":"2025-05-30 15:57:40"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 15:57:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:57:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:57:40"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 15:57:40"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b466666b7ee01e8b5bd2","timestamp":"2025-05-30 15:57:40"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 15:57:40"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 15:57:40"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 15:57:40"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 15:57:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 15:57:40","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 51ms","timestamp":"2025-05-30 15:57:40"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 47ms","timestamp":"2025-05-30 15:57:40"}
{"level":"http","message":"GraphQL anonymous completed in 110ms","timestamp":"2025-05-30 15:57:40"}
{"level":"http","message":"POST / 200 - 114ms","timestamp":"2025-05-30 15:57:40"}
{"level":"http","message":"PUT /logout?secret=2cinfo1&client=esprit 200 - 118ms","timestamp":"2025-05-30 15:57:50"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:57:55","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:57:55"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 15:57:55"}
{"level":"http","message":"POST /signup 200 - 1357ms","timestamp":"2025-05-30 15:58:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:58:10","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:58:10"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 15:58:10"}
{"level":"http","message":"POST /signup 200 - 1250ms","timestamp":"2025-05-30 15:58:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:58:25","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 15:58:25"}
{"level":"http","message":"POST / 200 - 1ms","timestamp":"2025-05-30 15:58:25"}
{"level":"http","message":"POST /verify-email 200 - 1905ms","timestamp":"2025-05-30 15:58:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:58:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 2ms","timestamp":"2025-05-30 15:58:40"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 15:58:40"}
{"level":"http","message":"POST /login 200 - 233ms","timestamp":"2025-05-30 15:58:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:58:55","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:58:55"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:58:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:59:10","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:59:10"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:59:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:59:25","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:59:25"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 15:59:25"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:59:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:59:40"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 15:59:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 15:59:55","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 15:59:55"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 15:59:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:00:10","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:00:10"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:00:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:00:19","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 135ms","timestamp":"2025-05-30 16:00:19"}
{"level":"http","message":"POST / 200 - 139ms","timestamp":"2025-05-30 16:00:19"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkNotificationsAsRead($notificationIds: [ID!]!) {\n  markNotificationsAsRead(notificationIds: $notificationIds) {\n    success\n    readCount\n    remainingCount\n  }\n}","timestamp":"2025-05-30 16:00:22","variables":{"notificationIds":["6839c3aeb3d00b25cf26afea"]}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-30 16:00:22","variables":{}}
{"level":"warn","message":"[MessageService] getBulkUnreadCounts called with empty conversationIds","timestamp":"2025-05-30 16:00:22"}
{"level":"http","message":"GraphQL anonymous completed in 52ms","timestamp":"2025-05-30 16:00:22"}
{"level":"http","message":"POST / 200 - 61ms","timestamp":"2025-05-30 16:00:22"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation CreateConversation($userId: ID!) {\n  createConversation(userId: $userId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-30 16:00:22","variables":{"userId":"6839c1fcb3d00b25cf26afa7"}}
{"level":"http","message":"GraphQL anonymous completed in 163ms","timestamp":"2025-05-30 16:00:22"}
{"level":"http","message":"POST / 200 - 167ms","timestamp":"2025-05-30 16:00:22"}
{"level":"info","message":"[MessageService] New conversation created: 6839c806f13093fc2d5643e1","timestamp":"2025-05-30 16:00:22"}
{"level":"http","message":"GraphQL anonymous completed in 346ms","timestamp":"2025-05-30 16:00:23"}
{"level":"http","message":"POST / 200 - 350ms","timestamp":"2025-05-30 16:00:23"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:00:23"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:00:23","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:00:23"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 16:00:23"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:00:23"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:00:23"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:00:23"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:00:23"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:00:23"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:00:23","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 54ms","timestamp":"2025-05-30 16:00:23"}
{"level":"http","message":"GraphQL anonymous completed in 133ms","timestamp":"2025-05-30 16:00:24"}
{"level":"http","message":"POST / 200 - 143ms","timestamp":"2025-05-30 16:00:24"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 71ms","timestamp":"2025-05-30 16:00:24"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:00:46"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:00:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:00:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:00:48"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:00:48"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:00:48"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:00:48"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:00:48"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:00:48"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:00:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:00:48","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:00:48"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 16:00:48"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:00:48"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:00:48"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:00:48"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:00:48"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:00:48"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 48ms","timestamp":"2025-05-30 16:00:50"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:00:50","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 76ms","timestamp":"2025-05-30 16:00:50"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 55ms","timestamp":"2025-05-30 16:00:50"}
{"level":"http","message":"GraphQL anonymous completed in 197ms","timestamp":"2025-05-30 16:00:50"}
{"level":"http","message":"POST / 200 - 206ms","timestamp":"2025-05-30 16:00:50"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 105ms","timestamp":"2025-05-30 16:00:50"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:00:50","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 141ms","timestamp":"2025-05-30 16:00:50"}
{"level":"http","message":"POST / 200 - 158ms","timestamp":"2025-05-30 16:00:50"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 44ms","timestamp":"2025-05-30 16:00:50"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:00:58"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:00:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:01:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:01:00"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 16:01:00"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:01:00"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:01:00"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:01:00"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:01:00"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:01:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:01:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:01:00"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:01:00"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:01:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:01:00","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:01:00"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:01:00"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:01:00"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:01:00"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 78ms","timestamp":"2025-05-30 16:01:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:01:00","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 129ms","timestamp":"2025-05-30 16:01:00"}
{"level":"http","message":"POST / 200 - 136ms","timestamp":"2025-05-30 16:01:00"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 83ms","timestamp":"2025-05-30 16:01:00"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 56ms","timestamp":"2025-05-30 16:01:01"}
{"level":"http","message":"GraphQL anonymous completed in 96ms","timestamp":"2025-05-30 16:01:01"}
{"level":"http","message":"POST / 200 - 101ms","timestamp":"2025-05-30 16:01:01"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 49ms","timestamp":"2025-05-30 16:01:01"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 47ms","timestamp":"2025-05-30 16:01:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:01:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:01:30"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:01:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:01:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:01:30"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:01:30"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:01:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:01:37","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:01:37"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:01:37"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:01:37"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:01:37"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:01:37"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:01:37"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:01:37"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:01:37","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 49ms","timestamp":"2025-05-30 16:01:37"}
{"level":"http","message":"GraphQL anonymous completed in 116ms","timestamp":"2025-05-30 16:01:37"}
{"level":"http","message":"POST / 200 - 121ms","timestamp":"2025-05-30 16:01:37"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 65ms","timestamp":"2025-05-30 16:01:37"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:01:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:01:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:01:41"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:01:41"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:01:41"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:01:41"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:01:41"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:01:41"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:01:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:01:41","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 56ms","timestamp":"2025-05-30 16:01:41"}
{"level":"http","message":"GraphQL anonymous completed in 104ms","timestamp":"2025-05-30 16:01:41"}
{"level":"http","message":"POST / 200 - 111ms","timestamp":"2025-05-30 16:01:41"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 63ms","timestamp":"2025-05-30 16:01:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:02:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:02:00"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 16:02:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:02:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:02:11"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 16:02:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:02:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:02:30"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 16:02:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:02:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:02:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:02:41"}
{"level":"http","message":"PUT /complete-profile 200 - 149ms","timestamp":"2025-05-30 16:02:47"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 64ms","timestamp":"2025-05-30 16:02:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:03:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:03:00"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:03:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:03:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:03:11"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 16:03:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:03:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:03:30"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:03:30"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:03:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:03:36","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:03:36"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 16:03:36"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:03:36"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:03:36"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:03:36"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:03:36"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:03:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:03:36","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 52ms","timestamp":"2025-05-30 16:03:36"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 47ms","timestamp":"2025-05-30 16:03:36"}
{"level":"http","message":"GraphQL anonymous completed in 110ms","timestamp":"2025-05-30 16:03:36"}
{"level":"http","message":"POST / 200 - 118ms","timestamp":"2025-05-30 16:03:36"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 55ms","timestamp":"2025-05-30 16:03:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:03:43","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 104ms","timestamp":"2025-05-30 16:03:43"}
{"level":"http","message":"POST / 200 - 115ms","timestamp":"2025-05-30 16:03:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkNotificationsAsRead($notificationIds: [ID!]!) {\n  markNotificationsAsRead(notificationIds: $notificationIds) {\n    success\n    readCount\n    remainingCount\n  }\n}","timestamp":"2025-05-30 16:03:46","variables":{"notificationIds":["6839c3aeb3d00b25cf26afea"]}}
{"level":"http","message":"GraphQL anonymous completed in 161ms","timestamp":"2025-05-30 16:03:46"}
{"level":"http","message":"POST / 200 - 175ms","timestamp":"2025-05-30 16:03:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkNotificationsAsRead($notificationIds: [ID!]!) {\n  markNotificationsAsRead(notificationIds: $notificationIds) {\n    success\n    readCount\n    remainingCount\n  }\n}","timestamp":"2025-05-30 16:03:49","variables":{"notificationIds":["6839c3aeb3d00b25cf26afea"]}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-30 16:03:49","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 198ms","timestamp":"2025-05-30 16:03:50"}
{"level":"http","message":"POST / 200 - 205ms","timestamp":"2025-05-30 16:03:50"}
{"level":"http","message":"GraphQL anonymous completed in 203ms","timestamp":"2025-05-30 16:03:50"}
{"level":"http","message":"POST / 200 - 210ms","timestamp":"2025-05-30 16:03:50"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation CreateConversation($userId: ID!) {\n  createConversation(userId: $userId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-30 16:03:50","variables":{"userId":"6839c1fcb3d00b25cf26afa7"}}
{"level":"http","message":"GraphQL anonymous completed in 436ms","timestamp":"2025-05-30 16:03:50"}
{"level":"http","message":"POST / 200 - 490ms","timestamp":"2025-05-30 16:03:50"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:03:50"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:03:51","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:03:51"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:03:51"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:03:51"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:03:51"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:03:51"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:03:51"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:03:51"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:03:51","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 69ms","timestamp":"2025-05-30 16:03:51"}
{"level":"http","message":"GraphQL anonymous completed in 128ms","timestamp":"2025-05-30 16:03:51"}
{"level":"http","message":"POST / 200 - 143ms","timestamp":"2025-05-30 16:03:51"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 64ms","timestamp":"2025-05-30 16:03:51"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:04:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:04:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:04:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:04:10","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 126ms","timestamp":"2025-05-30 16:04:10"}
{"level":"http","message":"POST / 200 - 132ms","timestamp":"2025-05-30 16:04:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkNotificationsAsRead($notificationIds: [ID!]!) {\n  markNotificationsAsRead(notificationIds: $notificationIds) {\n    success\n    readCount\n    remainingCount\n  }\n}","timestamp":"2025-05-30 16:04:12","variables":{"notificationIds":["6839c3aeb3d00b25cf26afea"]}}
{"level":"http","message":"GraphQL anonymous completed in 169ms","timestamp":"2025-05-30 16:04:12"}
{"level":"http","message":"POST / 200 - 175ms","timestamp":"2025-05-30 16:04:12"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkNotificationsAsRead($notificationIds: [ID!]!) {\n  markNotificationsAsRead(notificationIds: $notificationIds) {\n    success\n    readCount\n    remainingCount\n  }\n}","timestamp":"2025-05-30 16:04:13","variables":{"notificationIds":["6839c3aeb3d00b25cf26afea"]}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-30 16:04:13","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 156ms","timestamp":"2025-05-30 16:04:13"}
{"level":"http","message":"POST / 200 - 162ms","timestamp":"2025-05-30 16:04:13"}
{"level":"http","message":"GraphQL anonymous completed in 191ms","timestamp":"2025-05-30 16:04:13"}
{"level":"http","message":"POST / 200 - 196ms","timestamp":"2025-05-30 16:04:13"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation CreateConversation($userId: ID!) {\n  createConversation(userId: $userId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-30 16:04:13","variables":{"userId":"6839c1fcb3d00b25cf26afa7"}}
{"level":"http","message":"GraphQL anonymous completed in 266ms","timestamp":"2025-05-30 16:04:14"}
{"level":"http","message":"POST / 200 - 270ms","timestamp":"2025-05-30 16:04:14"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:04:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:04:14","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:04:14"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:04:14"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:04:14"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:04:14"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:04:14"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:04:14"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:04:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:04:14","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 54ms","timestamp":"2025-05-30 16:04:14"}
{"level":"http","message":"GraphQL anonymous completed in 149ms","timestamp":"2025-05-30 16:04:14"}
{"level":"http","message":"POST / 200 - 155ms","timestamp":"2025-05-30 16:04:14"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 75ms","timestamp":"2025-05-30 16:04:14"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 59ms","timestamp":"2025-05-30 16:04:17"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:04:33"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:04:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:04:38","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:04:38"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 16:04:38"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:04:38"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:04:38"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:04:38"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:04:38"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:04:38"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:04:38","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 57ms","timestamp":"2025-05-30 16:04:38"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 58ms","timestamp":"2025-05-30 16:04:38"}
{"level":"http","message":"GraphQL anonymous completed in 124ms","timestamp":"2025-05-30 16:04:38"}
{"level":"http","message":"POST / 200 - 133ms","timestamp":"2025-05-30 16:04:38"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 68ms","timestamp":"2025-05-30 16:04:38"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:04:38","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:04:38"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 16:04:38"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:04:38"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:04:38"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:04:38"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:04:38"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:04:38"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:04:38","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 64ms","timestamp":"2025-05-30 16:04:38"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 57ms","timestamp":"2025-05-30 16:04:38"}
{"level":"http","message":"GraphQL anonymous completed in 127ms","timestamp":"2025-05-30 16:04:38"}
{"level":"http","message":"POST / 200 - 139ms","timestamp":"2025-05-30 16:04:38"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 59ms","timestamp":"2025-05-30 16:04:38"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:04:43"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:04:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:04:45","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:04:45"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:04:45"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:04:45"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:04:45"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:04:45"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:04:45"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:04:45"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:04:45","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:04:45"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 16:04:45"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:04:45"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:04:45","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:04:45"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:04:45"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:04:45"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:04:45"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 77ms","timestamp":"2025-05-30 16:04:45"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:04:45","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 58ms","timestamp":"2025-05-30 16:04:45"}
{"level":"http","message":"GraphQL anonymous completed in 106ms","timestamp":"2025-05-30 16:04:45"}
{"level":"http","message":"POST / 200 - 111ms","timestamp":"2025-05-30 16:04:45"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 72ms","timestamp":"2025-05-30 16:04:45"}
{"level":"http","message":"GraphQL anonymous completed in 113ms","timestamp":"2025-05-30 16:04:45"}
{"level":"http","message":"POST / 200 - 118ms","timestamp":"2025-05-30 16:04:45"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 54ms","timestamp":"2025-05-30 16:04:45"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 56ms","timestamp":"2025-05-30 16:04:45"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 59ms","timestamp":"2025-05-30 16:04:45"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:05:06"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:05:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:05:07","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:05:07"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 16:05:07"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:05:07"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:05:07"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:05:07"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:05:07"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:05:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:05:07","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:05:07"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 16:05:07"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:05:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:05:07","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:05:07"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:05:07"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:05:07"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:05:07"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 60ms","timestamp":"2025-05-30 16:05:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:05:07","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 55ms","timestamp":"2025-05-30 16:05:07"}
{"level":"http","message":"GraphQL anonymous completed in 117ms","timestamp":"2025-05-30 16:05:08"}
{"level":"http","message":"POST / 200 - 121ms","timestamp":"2025-05-30 16:05:08"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 52ms","timestamp":"2025-05-30 16:05:08"}
{"level":"http","message":"GraphQL anonymous completed in 111ms","timestamp":"2025-05-30 16:05:08"}
{"level":"http","message":"POST / 200 - 114ms","timestamp":"2025-05-30 16:05:08"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 68ms","timestamp":"2025-05-30 16:05:08"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 42ms","timestamp":"2025-05-30 16:05:08"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 44ms","timestamp":"2025-05-30 16:05:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:05:37","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:05:37"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:05:37"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:05:37","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:05:37"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:05:37"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:05:39"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:05:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:05:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:05:40"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:05:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:05:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:05:40"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:05:40"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:05:40"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:05:40"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:05:40"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:05:40"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:05:40"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:05:40"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:05:40"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:05:40"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:05:40"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:05:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:05:40","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:05:41","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 50ms","timestamp":"2025-05-30 16:05:41"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 47ms","timestamp":"2025-05-30 16:05:41"}
{"level":"http","message":"GraphQL anonymous completed in 100ms","timestamp":"2025-05-30 16:05:41"}
{"level":"http","message":"POST / 200 - 105ms","timestamp":"2025-05-30 16:05:41"}
{"level":"http","message":"GraphQL anonymous completed in 106ms","timestamp":"2025-05-30 16:05:41"}
{"level":"http","message":"POST / 200 - 111ms","timestamp":"2025-05-30 16:05:41"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 62ms","timestamp":"2025-05-30 16:05:41"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 60ms","timestamp":"2025-05-30 16:05:41"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 49ms","timestamp":"2025-05-30 16:05:41"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 52ms","timestamp":"2025-05-30 16:05:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:06:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:06:11"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 16:06:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:06:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:06:11"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:06:11"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:06:14"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:06:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:06:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:06:15"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:06:15"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:06:15"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:06:15"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:06:15"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:06:15"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:06:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:06:15","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 40ms","timestamp":"2025-05-30 16:06:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:06:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:06:15"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:06:15"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 43ms","timestamp":"2025-05-30 16:06:15"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:06:15"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:06:15"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:06:15"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:06:15"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:06:15"}
{"level":"http","message":"GraphQL anonymous completed in 113ms","timestamp":"2025-05-30 16:06:15"}
{"level":"http","message":"POST / 200 - 118ms","timestamp":"2025-05-30 16:06:15"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 48ms","timestamp":"2025-05-30 16:06:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:06:15","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 49ms","timestamp":"2025-05-30 16:06:15"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 45ms","timestamp":"2025-05-30 16:06:15"}
{"level":"http","message":"GraphQL anonymous completed in 117ms","timestamp":"2025-05-30 16:06:15"}
{"level":"http","message":"POST / 200 - 120ms","timestamp":"2025-05-30 16:06:15"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 55ms","timestamp":"2025-05-30 16:06:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:06:45","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:06:45"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:06:45"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:06:45","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:06:45"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:06:45"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:07:00"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:07:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:07:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:07:02"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:07:02"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:07:02"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:07:02"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:07:02"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:07:02"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:07:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:07:02","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 47ms","timestamp":"2025-05-30 16:07:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:07:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:07:02"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:07:02"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 54ms","timestamp":"2025-05-30 16:07:02"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:07:02"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:07:02"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:07:02"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:07:02"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:07:02"}
{"level":"http","message":"GraphQL anonymous completed in 122ms","timestamp":"2025-05-30 16:07:02"}
{"level":"http","message":"POST / 200 - 127ms","timestamp":"2025-05-30 16:07:02"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 62ms","timestamp":"2025-05-30 16:07:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:07:02","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 51ms","timestamp":"2025-05-30 16:07:02"}
{"level":"http","message":"GraphQL anonymous completed in 126ms","timestamp":"2025-05-30 16:07:02"}
{"level":"http","message":"POST / 200 - 129ms","timestamp":"2025-05-30 16:07:02"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 93ms","timestamp":"2025-05-30 16:07:02"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 78ms","timestamp":"2025-05-30 16:07:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:07:32","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:07:32"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:07:32"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:07:32","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:07:32"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:07:32"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:07:56"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:07:57"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:07:57","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:07:57"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 16:07:57"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:07:58"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:07:58"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:07:58"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:07:58"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:07:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:07:58","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 66ms","timestamp":"2025-05-30 16:07:58"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 63ms","timestamp":"2025-05-30 16:07:58"}
{"level":"http","message":"GraphQL anonymous completed in 124ms","timestamp":"2025-05-30 16:07:58"}
{"level":"http","message":"POST / 200 - 132ms","timestamp":"2025-05-30 16:07:58"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 60ms","timestamp":"2025-05-30 16:07:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:07:58","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:07:58"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:07:58"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:07:58"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:07:58"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:07:58"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:07:58"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:07:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:07:58","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 64ms","timestamp":"2025-05-30 16:07:58"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 51ms","timestamp":"2025-05-30 16:07:58"}
{"level":"http","message":"GraphQL anonymous completed in 113ms","timestamp":"2025-05-30 16:07:58"}
{"level":"http","message":"POST / 200 - 124ms","timestamp":"2025-05-30 16:07:58"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 66ms","timestamp":"2025-05-30 16:07:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:08:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:08:28"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:08:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:08:28","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:08:28"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:08:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:08:58","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:08:58"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:08:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:08:58","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:08:58"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:08:58"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:09:05"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:09:06"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:09:06","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:09:06"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:09:06"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:09:06"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:09:06"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:09:06"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:09:06"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:09:06"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:09:06","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 49ms","timestamp":"2025-05-30 16:09:06"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:09:06","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:09:06"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:09:06"}
{"level":"http","message":"GraphQL anonymous completed in 102ms","timestamp":"2025-05-30 16:09:06"}
{"level":"http","message":"POST / 200 - 107ms","timestamp":"2025-05-30 16:09:06"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 69ms","timestamp":"2025-05-30 16:09:06"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:09:06"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:09:06"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:09:06"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:09:06"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:09:06"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 55ms","timestamp":"2025-05-30 16:09:06"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:09:06","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 44ms","timestamp":"2025-05-30 16:09:06"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 54ms","timestamp":"2025-05-30 16:09:06"}
{"level":"http","message":"GraphQL anonymous completed in 107ms","timestamp":"2025-05-30 16:09:06"}
{"level":"http","message":"POST / 200 - 115ms","timestamp":"2025-05-30 16:09:06"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 60ms","timestamp":"2025-05-30 16:09:06"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:09:36","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:09:36"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:09:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:09:36","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:09:36"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:09:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:10:06","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:10:06"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:10:06"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:10:06","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:10:06"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:10:06"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:10:36","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:10:36"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 16:10:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:10:36","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:10:36"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:10:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:11:06","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:11:06"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:11:06"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:11:06","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:11:06"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:11:06"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:11:36","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:11:36"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:11:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:11:36","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:11:36"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:11:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:13:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:13:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:13:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:13:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:13:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:13:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:14:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:14:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:14:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:14:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:14:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:14:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:15:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:15:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:15:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:15:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:15:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:15:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:16:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:16:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:16:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:16:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:16:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:16:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:17:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:17:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:17:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:17:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:17:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:17:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:18:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:18:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:18:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:18:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:18:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:18:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:19:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:19:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:19:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:19:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:19:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:19:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:20:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:20:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:20:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:20:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:20:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:20:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:21:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:21:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:21:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:21:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:21:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:21:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:22:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:22:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 16:22:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:22:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:22:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:22:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:23:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:23:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:23:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:23:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:23:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:23:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:24:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:24:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:24:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:24:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:24:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:24:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:25:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:25:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:25:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:25:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:25:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:25:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:26:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:26:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:26:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:26:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 3ms","timestamp":"2025-05-30 16:26:01"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 16:26:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:27:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:27:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:27:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:27:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:27:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 16:27:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:28:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:28:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:28:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:28:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:28:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:28:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:29:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:29:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:29:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:29:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:29:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:29:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:30:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:30:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 16:30:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:30:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:30:01"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 16:30:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:31:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:31:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 16:31:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:31:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:31:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 16:31:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:32:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:32:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:32:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:32:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:32:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:32:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:33:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:33:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 16:33:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:33:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:33:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:33:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:34:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:34:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:34:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:34:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:34:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:34:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:35:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:35:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:35:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:35:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:35:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:35:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:36:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:36:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:36:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:36:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:36:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:36:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:37:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:37:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:37:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:37:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:37:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 16:37:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:38:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:38:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:38:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:38:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:38:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:38:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:39:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:39:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:39:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:39:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:39:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:39:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:39:22","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:39:22"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 16:39:22"}
{"level":"http","message":"PUT /complete-profile 200 - 107ms","timestamp":"2025-05-30 16:39:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:39:36","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:39:36"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:39:36"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:39:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:39:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:39:41"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 16:39:41"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:39:41"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:39:41"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:39:41"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:39:41"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:39:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:39:41","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 52ms","timestamp":"2025-05-30 16:39:41"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 58ms","timestamp":"2025-05-30 16:39:42"}
{"level":"http","message":"GraphQL anonymous completed in 139ms","timestamp":"2025-05-30 16:39:42"}
{"level":"http","message":"POST / 200 - 148ms","timestamp":"2025-05-30 16:39:42"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 57ms","timestamp":"2025-05-30 16:39:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:39:52","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 122ms","timestamp":"2025-05-30 16:39:53"}
{"level":"http","message":"POST / 200 - 130ms","timestamp":"2025-05-30 16:39:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkNotificationsAsRead($notificationIds: [ID!]!) {\n  markNotificationsAsRead(notificationIds: $notificationIds) {\n    success\n    readCount\n    remainingCount\n  }\n}","timestamp":"2025-05-30 16:39:57","variables":{"notificationIds":["6839d0cc72291558e41f45e2"]}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-30 16:39:57","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 194ms","timestamp":"2025-05-30 16:39:58"}
{"level":"http","message":"POST / 200 - 201ms","timestamp":"2025-05-30 16:39:58"}
{"level":"http","message":"GraphQL anonymous completed in 216ms","timestamp":"2025-05-30 16:39:58"}
{"level":"http","message":"POST / 200 - 223ms","timestamp":"2025-05-30 16:39:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation CreateConversation($userId: ID!) {\n  createConversation(userId: $userId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-30 16:39:58","variables":{"userId":"6839bf2db3d00b25cf26af7e"}}
{"level":"info","message":"[MessageService] New conversation created: 6839d14ef13093fc2d564499","timestamp":"2025-05-30 16:39:58"}
{"level":"http","message":"GraphQL anonymous completed in 320ms","timestamp":"2025-05-30 16:39:58"}
{"level":"http","message":"POST / 200 - 324ms","timestamp":"2025-05-30 16:39:58"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:39:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:39:58","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:39:58"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:39:58"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:39:58"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:39:58"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:39:58"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:39:58"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:39:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:39:59","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 67ms","timestamp":"2025-05-30 16:39:59"}
{"level":"http","message":"GraphQL anonymous completed in 106ms","timestamp":"2025-05-30 16:39:59"}
{"level":"http","message":"POST / 200 - 112ms","timestamp":"2025-05-30 16:39:59"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 51ms","timestamp":"2025-05-30 16:39:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:40:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:40:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:40:01"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:40:15"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:40:15"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:40:15"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:40:15"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:40:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-30 16:40:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 203ms","timestamp":"2025-05-30 16:40:15"}
{"level":"http","message":"POST / 200 - 214ms","timestamp":"2025-05-30 16:40:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-30 16:40:17","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 16:40:17","variables":{"conversationId":"6839d14ef13093fc2d564499","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839d14ef13093fc2d564499, userId=6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:40:17"}
{"level":"http","message":"GraphQL anonymous completed in 53ms","timestamp":"2025-05-30 16:40:17"}
{"level":"http","message":"POST / 200 - 58ms","timestamp":"2025-05-30 16:40:17"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839d14ef13093fc2d564499, unread: 0, messages: 0","timestamp":"2025-05-30 16:40:17"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839d14ef13093fc2d564499, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839b36810e260b7a7cbdecd, offset=0","timestamp":"2025-05-30 16:40:17"}
{"level":"info","message":"[MessageService] Retrieved 0 messages","timestamp":"2025-05-30 16:40:17"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 16:40:17"}
{"level":"http","message":"GraphQL anonymous completed in 374ms","timestamp":"2025-05-30 16:40:17"}
{"level":"http","message":"POST / 200 - 379ms","timestamp":"2025-05-30 16:40:17"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:40:17"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:40:17"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:40:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-30 16:40:22","variables":{"content":"wa si ayoub","receiverId":"6839bf2db3d00b25cf26af7e","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=6839b36810e260b7a7cbdecd, receiverId=6839bf2db3d00b25cf26af7e, type=TEXT, hasMetadata=false","timestamp":"2025-05-30 16:40:22"}
{"level":"info","message":"[MessageService] Message saved successfully: 6839d166f13093fc2d5644b3","timestamp":"2025-05-30 16:40:22"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=6839d166f13093fc2d5644b3","timestamp":"2025-05-30 16:40:22"}
{"level":"http","message":"GraphQL anonymous completed in 520ms","timestamp":"2025-05-30 16:40:22"}
{"level":"http","message":"POST / 200 - 525ms","timestamp":"2025-05-30 16:40:22"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:40:29","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:40:29"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:40:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation InitiateCall($recipientId: ID!, $callType: CallType!, $callId: String!, $offer: String!, $conversationId: ID, $options: CallOptions) {\n  initiateCall(\n    recipientId: $recipientId\n    callType: $callType\n    callId: $callId\n    offer: $offer\n    conversationId: $conversationId\n    options: $options\n  ) {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    conversationId\n  }\n}","timestamp":"2025-05-30 16:40:55","variables":{"callId":"17486196559365bnnq10","callType":"AUDIO","conversationId":"6839d14ef13093fc2d564499","offer":"{\"sdp\":\"v=0\\r\\no=- 7297333879491692302 2 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\na=group:BUNDLE 0\\r\\na=extmap-allow-mixed\\r\\na=msid-semantic: WMS 13102cc9-b0bb-4530-9573-e7b2a4896449\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 9 0 8 13 110 126\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:dMqD\\r\\na=ice-pwd:7m2CM/k+rvR19MSPlOoeiaRN\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 39:35:72:21:43:B0:02:92:28:34:60:41:FF:8F:45:9D:84:1C:98:7B:F8:BF:5A:39:2E:8B:E6:95:B8:21:9F:D0\\r\\na=setup:actpass\\r\\na=mid:0\\r\\na=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=sendrecv\\r\\na=msid:13102cc9-b0bb-4530-9573-e7b2a4896449 63f43d3a-5d8e-4c72-b713-38330e008390\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:111 opus/48000/2\\r\\na=rtcp-fb:111 transport-cc\\r\\na=fmtp:111 minptime=10;useinbandfec=1\\r\\na=rtpmap:63 red/48000/2\\r\\na=fmtp:63 111/111\\r\\na=rtpmap:9 G722/8000\\r\\na=rtpmap:0 PCMU/8000\\r\\na=rtpmap:8 PCMA/8000\\r\\na=rtpmap:13 CN/8000\\r\\na=rtpmap:110 telephone-event/48000\\r\\na=rtpmap:126 telephone-event/8000\\r\\na=ssrc:3811658460 cname:9Xy0UjgzT+nyrnqF\\r\\na=ssrc:3811658460 msid:13102cc9-b0bb-4530-9573-e7b2a4896449 63f43d3a-5d8e-4c72-b713-38330e008390\\r\\n\",\"type\":\"offer\"}","recipientId":"6839bf2db3d00b25cf26af7e"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 5ms","timestamp":"2025-05-30 16:40:55"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 16:40:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation InitiateCall($recipientId: ID!, $callType: CallType!, $callId: String!, $offer: String!, $conversationId: ID, $options: CallOptions) {\n  initiateCall(\n    recipientId: $recipientId\n    callType: $callType\n    callId: $callId\n    offer: $offer\n    conversationId: $conversationId\n    options: $options\n  ) {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    conversationId\n  }\n}","timestamp":"2025-05-30 16:40:56","variables":{"callId":"17486196559365bnnq10","callType":"AUDIO","conversationId":"6839d14ef13093fc2d564499","offer":"{\"sdp\":\"v=0\\r\\no=- 7297333879491692302 2 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\na=group:BUNDLE 0\\r\\na=extmap-allow-mixed\\r\\na=msid-semantic: WMS 13102cc9-b0bb-4530-9573-e7b2a4896449\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 9 0 8 13 110 126\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:dMqD\\r\\na=ice-pwd:7m2CM/k+rvR19MSPlOoeiaRN\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 39:35:72:21:43:B0:02:92:28:34:60:41:FF:8F:45:9D:84:1C:98:7B:F8:BF:5A:39:2E:8B:E6:95:B8:21:9F:D0\\r\\na=setup:actpass\\r\\na=mid:0\\r\\na=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=sendrecv\\r\\na=msid:13102cc9-b0bb-4530-9573-e7b2a4896449 63f43d3a-5d8e-4c72-b713-38330e008390\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:111 opus/48000/2\\r\\na=rtcp-fb:111 transport-cc\\r\\na=fmtp:111 minptime=10;useinbandfec=1\\r\\na=rtpmap:63 red/48000/2\\r\\na=fmtp:63 111/111\\r\\na=rtpmap:9 G722/8000\\r\\na=rtpmap:0 PCMU/8000\\r\\na=rtpmap:8 PCMA/8000\\r\\na=rtpmap:13 CN/8000\\r\\na=rtpmap:110 telephone-event/48000\\r\\na=rtpmap:126 telephone-event/8000\\r\\na=ssrc:3811658460 cname:9Xy0UjgzT+nyrnqF\\r\\na=ssrc:3811658460 msid:13102cc9-b0bb-4530-9573-e7b2a4896449 63f43d3a-5d8e-4c72-b713-38330e008390\\r\\n\",\"type\":\"offer\"}","recipientId":"6839bf2db3d00b25cf26af7e"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 1ms","timestamp":"2025-05-30 16:40:56"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:40:56"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:40:59","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:40:59"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:40:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:41:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:41:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:41:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation InitiateCall($recipientId: ID!, $callType: CallType!, $callId: String!, $offer: String!, $conversationId: ID, $options: CallOptions) {\n  initiateCall(\n    recipientId: $recipientId\n    callType: $callType\n    callId: $callId\n    offer: $offer\n    conversationId: $conversationId\n    options: $options\n  ) {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    conversationId\n  }\n}","timestamp":"2025-05-30 16:41:02","variables":{"callId":"1748619662202plsmrlg","callType":"AUDIO","conversationId":"6839d14ef13093fc2d564499","offer":"{\"sdp\":\"v=0\\r\\no=- 2078488470652236223 2 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\na=group:BUNDLE 0\\r\\na=extmap-allow-mixed\\r\\na=msid-semantic: WMS a3507c78-8ec1-4d79-827f-d4be02bfa6a7\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 9 0 8 13 110 126\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:43a9\\r\\na=ice-pwd:F0K8IUKsC52U64VjmI5u3v2z\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 A1:D8:34:1E:24:02:82:B5:09:C5:38:2D:C8:CC:CD:56:59:32:38:36:BE:D3:BE:54:BE:91:BE:FB:FF:45:47:42\\r\\na=setup:actpass\\r\\na=mid:0\\r\\na=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=sendrecv\\r\\na=msid:a3507c78-8ec1-4d79-827f-d4be02bfa6a7 acaaddcb-0e6b-45dd-a91a-676e536b2694\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:111 opus/48000/2\\r\\na=rtcp-fb:111 transport-cc\\r\\na=fmtp:111 minptime=10;useinbandfec=1\\r\\na=rtpmap:63 red/48000/2\\r\\na=fmtp:63 111/111\\r\\na=rtpmap:9 G722/8000\\r\\na=rtpmap:0 PCMU/8000\\r\\na=rtpmap:8 PCMA/8000\\r\\na=rtpmap:13 CN/8000\\r\\na=rtpmap:110 telephone-event/48000\\r\\na=rtpmap:126 telephone-event/8000\\r\\na=ssrc:493698574 cname:L1tzic5VOPTFAWAB\\r\\na=ssrc:493698574 msid:a3507c78-8ec1-4d79-827f-d4be02bfa6a7 acaaddcb-0e6b-45dd-a91a-676e536b2694\\r\\n\",\"type\":\"offer\"}","recipientId":"6839bf2db3d00b25cf26af7e"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 1ms","timestamp":"2025-05-30 16:41:02"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:41:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation InitiateCall($recipientId: ID!, $callType: CallType!, $callId: String!, $offer: String!, $conversationId: ID, $options: CallOptions) {\n  initiateCall(\n    recipientId: $recipientId\n    callType: $callType\n    callId: $callId\n    offer: $offer\n    conversationId: $conversationId\n    options: $options\n  ) {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    conversationId\n  }\n}","timestamp":"2025-05-30 16:41:02","variables":{"callId":"1748619662202plsmrlg","callType":"AUDIO","conversationId":"6839d14ef13093fc2d564499","offer":"{\"sdp\":\"v=0\\r\\no=- 2078488470652236223 2 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\na=group:BUNDLE 0\\r\\na=extmap-allow-mixed\\r\\na=msid-semantic: WMS a3507c78-8ec1-4d79-827f-d4be02bfa6a7\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 9 0 8 13 110 126\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:43a9\\r\\na=ice-pwd:F0K8IUKsC52U64VjmI5u3v2z\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 A1:D8:34:1E:24:02:82:B5:09:C5:38:2D:C8:CC:CD:56:59:32:38:36:BE:D3:BE:54:BE:91:BE:FB:FF:45:47:42\\r\\na=setup:actpass\\r\\na=mid:0\\r\\na=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=sendrecv\\r\\na=msid:a3507c78-8ec1-4d79-827f-d4be02bfa6a7 acaaddcb-0e6b-45dd-a91a-676e536b2694\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:111 opus/48000/2\\r\\na=rtcp-fb:111 transport-cc\\r\\na=fmtp:111 minptime=10;useinbandfec=1\\r\\na=rtpmap:63 red/48000/2\\r\\na=fmtp:63 111/111\\r\\na=rtpmap:9 G722/8000\\r\\na=rtpmap:0 PCMU/8000\\r\\na=rtpmap:8 PCMA/8000\\r\\na=rtpmap:13 CN/8000\\r\\na=rtpmap:110 telephone-event/48000\\r\\na=rtpmap:126 telephone-event/8000\\r\\na=ssrc:493698574 cname:L1tzic5VOPTFAWAB\\r\\na=ssrc:493698574 msid:a3507c78-8ec1-4d79-827f-d4be02bfa6a7 acaaddcb-0e6b-45dd-a91a-676e536b2694\\r\\n\",\"type\":\"offer\"}","recipientId":"6839bf2db3d00b25cf26af7e"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 0ms","timestamp":"2025-05-30 16:41:02"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:41:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:41:29","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:41:29"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 16:41:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:41:59","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:41:59"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:41:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:42:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:42:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:42:01"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:42:08"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:42:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:42:09","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:42:09"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:42:09"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:42:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:42:09","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:42:09"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:42:09"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:42:09"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:42:09"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:42:09"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:42:09"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:42:09"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:42:09"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:42:09"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:42:09"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:42:09"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 48ms","timestamp":"2025-05-30 16:42:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:42:10","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:42:10","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 56ms","timestamp":"2025-05-30 16:42:10"}
{"level":"http","message":"GraphQL anonymous completed in 146ms","timestamp":"2025-05-30 16:42:10"}
{"level":"http","message":"POST / 200 - 155ms","timestamp":"2025-05-30 16:42:10"}
{"level":"http","message":"GraphQL anonymous completed in 166ms","timestamp":"2025-05-30 16:42:10"}
{"level":"http","message":"POST / 200 - 172ms","timestamp":"2025-05-30 16:42:10"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 110ms","timestamp":"2025-05-30 16:42:10"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 54ms","timestamp":"2025-05-30 16:42:10"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 52ms","timestamp":"2025-05-30 16:42:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:42:39","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:42:39"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 16:42:39"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:42:39","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:42:39"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:42:39"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:43:09","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:43:09"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:43:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:43:09","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:43:09"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:43:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:43:39","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:43:39"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:43:39"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:43:39","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:43:39"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:43:39"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:44:09","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:44:09"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:44:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:44:09","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:44:09"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 16:44:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:44:39","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:44:39"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:44:39"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:44:39","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:44:39"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 16:44:39"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:46:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:46:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:46:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:46:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:46:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:46:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:47:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:47:02"}
{"level":"http","message":"POST / 200 - 14ms","timestamp":"2025-05-30 16:47:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:47:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:47:02"}
{"level":"http","message":"POST / 200 - 13ms","timestamp":"2025-05-30 16:47:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:48:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:48:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:48:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:48:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:48:01"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 16:48:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:49:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:49:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:49:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:49:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:49:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:49:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:50:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:50:01"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 16:50:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:50:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:50:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:50:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:51:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:51:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:51:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:51:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:51:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:51:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:52:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:52:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:52:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:52:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:52:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:52:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:53:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:53:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:53:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:53:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:53:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 16:53:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:54:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:54:01"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 16:54:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:54:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:54:01"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 16:54:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:55:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:55:01"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 16:55:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:55:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:55:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 16:55:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:56:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:56:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:56:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:56:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:56:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 16:56:01"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:56:34"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 16:56:34"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:56:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:56:35"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:56:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:56:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:56:35"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:56:35"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:56:35"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:56:35"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:56:35"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:56:35"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:56:35"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 16:56:35"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:56:35"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:56:35"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:56:35"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 16:56:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:56:35","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 48ms","timestamp":"2025-05-30 16:56:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 16:56:35","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 54ms","timestamp":"2025-05-30 16:56:35"}
{"level":"http","message":"GraphQL anonymous completed in 115ms","timestamp":"2025-05-30 16:56:35"}
{"level":"http","message":"POST / 200 - 119ms","timestamp":"2025-05-30 16:56:35"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 44ms","timestamp":"2025-05-30 16:56:35"}
{"level":"http","message":"GraphQL anonymous completed in 135ms","timestamp":"2025-05-30 16:56:35"}
{"level":"http","message":"POST / 200 - 140ms","timestamp":"2025-05-30 16:56:35"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 63ms","timestamp":"2025-05-30 16:56:35"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 72ms","timestamp":"2025-05-30 16:56:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:57:05","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:57:05"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:57:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:57:05","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:57:05"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:57:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:57:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:57:35"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:57:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:57:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:57:35"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:57:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:58:05","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:58:05"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:58:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:58:05","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:58:05"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 16:58:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:58:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:58:35"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:58:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:58:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:58:35"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:58:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:59:05","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 16:59:05"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 16:59:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 16:59:05","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 16:59:05"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 16:59:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:00:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:00:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 17:00:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:00:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:00:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 17:00:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:01:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:01:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 17:01:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:01:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:01:01"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 17:01:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:02:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:02:01"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 17:02:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:02:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:02:01"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 17:02:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:02:57","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:02:57"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 17:02:57"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:03:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:03:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 17:03:01"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 66ms","timestamp":"2025-05-30 17:03:03"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:03:05","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:03:05"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 17:03:05"}
{"level":"http","message":"PUT /complete-profile 200 - 121ms","timestamp":"2025-05-30 17:03:34"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:03:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:03:35"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 17:03:35"}
{"level":"http","message":"PUT /complete-profile 200 - 121ms","timestamp":"2025-05-30 17:04:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:04:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:04:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 17:04:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:04:05","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:04:05"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 17:04:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:04:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:04:35"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 17:04:35"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 17:04:53"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 17:04:54"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 17:04:54"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 17:04:54"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:04:54"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:04:54"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:04:54"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:04:54"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 17:04:54"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:04:54"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:04:54"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:04:54"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:04:54"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-02.ciy4zys.mongodb.net","timestamp":"2025-05-30 17:04:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:05:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 13ms","timestamp":"2025-05-30 17:05:01"}
{"level":"http","message":"POST / 200 - 31ms","timestamp":"2025-05-30 17:05:01"}
{"level":"http","message":"PUT /complete-profile 200 - 199ms","timestamp":"2025-05-30 17:05:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:05:05","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:05:05"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 17:05:05"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 17:05:14"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 17:05:14"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 17:05:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 17:05:14","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 17:05:14"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:14"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:14"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:14"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:14"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 17:05:14"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:14"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:14"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:14"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:14"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-02.ciy4zys.mongodb.net","timestamp":"2025-05-30 17:05:16"}
{"level":"http","message":"GraphQL anonymous completed in 3250ms","timestamp":"2025-05-30 17:05:17"}
{"level":"http","message":"POST / 200 - 3269ms","timestamp":"2025-05-30 17:05:17"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:18"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:18"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:18"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-30 17:05:18","variables":{}}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:18"}
{"level":"http","message":"GraphQL anonymous completed in 223ms","timestamp":"2025-05-30 17:05:18"}
{"level":"http","message":"POST / 200 - 231ms","timestamp":"2025-05-30 17:05:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-30 17:05:19","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 17:05:19","variables":{"conversationId":"6839d14ef13093fc2d564499","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839d14ef13093fc2d564499, userId=6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 17:05:19"}
{"level":"http","message":"GraphQL anonymous completed in 64ms","timestamp":"2025-05-30 17:05:19"}
{"level":"http","message":"POST / 200 - 72ms","timestamp":"2025-05-30 17:05:19"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839d14ef13093fc2d564499, unread: 3, messages: 4","timestamp":"2025-05-30 17:05:19"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839d14ef13093fc2d564499, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839b36810e260b7a7cbdecd, offset=0","timestamp":"2025-05-30 17:05:19"}
{"level":"info","message":"[MessageService] Retrieved 4 messages","timestamp":"2025-05-30 17:05:19"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 17:05:19"}
{"level":"http","message":"GraphQL anonymous completed in 429ms","timestamp":"2025-05-30 17:05:19"}
{"level":"http","message":"POST / 200 - 436ms","timestamp":"2025-05-30 17:05:19"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkMessageAsRead($messageId: ID!) {\n  markMessageAsRead(messageId: $messageId) {\n    id\n    isRead\n    readAt\n  }\n}","timestamp":"2025-05-30 17:05:19","variables":{"messageId":"6839d1c872291558e41f462a"}}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkMessageAsRead($messageId: ID!) {\n  markMessageAsRead(messageId: $messageId) {\n    id\n    isRead\n    readAt\n  }\n}","timestamp":"2025-05-30 17:05:19","variables":{"messageId":"6839d1d472291558e41f4644"}}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:19"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:19"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:19"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkMessageAsRead($messageId: ID!) {\n  markMessageAsRead(messageId: $messageId) {\n    id\n    isRead\n    readAt\n  }\n}","timestamp":"2025-05-30 17:05:19","variables":{"messageId":"6839d1e472291558e41f465e"}}
{"level":"http","message":"GraphQL anonymous completed in 208ms","timestamp":"2025-05-30 17:05:20"}
{"level":"http","message":"POST / 200 - 213ms","timestamp":"2025-05-30 17:05:20"}
{"level":"http","message":"GraphQL anonymous completed in 215ms","timestamp":"2025-05-30 17:05:20"}
{"level":"http","message":"POST / 200 - 221ms","timestamp":"2025-05-30 17:05:20"}
{"level":"http","message":"GraphQL anonymous completed in 215ms","timestamp":"2025-05-30 17:05:20"}
{"level":"http","message":"POST / 200 - 234ms","timestamp":"2025-05-30 17:05:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-30 17:05:26","variables":{"content":"hadmoullah hh","receiverId":"6839bf2db3d00b25cf26af7e","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=6839b36810e260b7a7cbdecd, receiverId=6839bf2db3d00b25cf26af7e, type=TEXT, hasMetadata=false","timestamp":"2025-05-30 17:05:26"}
{"level":"info","message":"[MessageService] Message saved successfully: 6839d746d612fb4054a3242c","timestamp":"2025-05-30 17:05:26"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=6839d746d612fb4054a3242c","timestamp":"2025-05-30 17:05:26"}
{"level":"http","message":"GraphQL anonymous completed in 515ms","timestamp":"2025-05-30 17:05:26"}
{"level":"http","message":"POST / 200 - 518ms","timestamp":"2025-05-30 17:05:26"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 17:05:35"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 17:05:35"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 17:05:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-30 17:05:35","variables":{"content":"appel ye5dem ?","receiverId":"6839bf2db3d00b25cf26af7e","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=6839b36810e260b7a7cbdecd, receiverId=6839bf2db3d00b25cf26af7e, type=TEXT, hasMetadata=false","timestamp":"2025-05-30 17:05:35"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-01.ciy4zys.mongodb.net","timestamp":"2025-05-30 17:05:36"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 17:05:36"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:37"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:37"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:37"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:37"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:05:37","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 3ms","timestamp":"2025-05-30 17:05:37"}
{"level":"http","message":"POST / 200 - 18ms","timestamp":"2025-05-30 17:05:37"}
{"level":"info","message":"[MessageService] Message saved successfully: 6839d751778d3d1a2842eb6b","timestamp":"2025-05-30 17:05:37"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=6839d751778d3d1a2842eb6b","timestamp":"2025-05-30 17:05:37"}
{"level":"http","message":"GraphQL anonymous completed in 1840ms","timestamp":"2025-05-30 17:05:37"}
{"level":"http","message":"POST / 200 - 1867ms","timestamp":"2025-05-30 17:05:37"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 17:05:38"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:38"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:38"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:38"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:38"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:38"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:38"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:38"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:38"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:38"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:38"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:05:38"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 17:06:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:06:03","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 3ms","timestamp":"2025-05-30 17:06:03"}
{"level":"http","message":"POST / 200 - 21ms","timestamp":"2025-05-30 17:06:03"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 17:06:03"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:06:03"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:06:03"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:06:03"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:06:03"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 98ms","timestamp":"2025-05-30 17:06:04"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 168ms","timestamp":"2025-05-30 17:06:04"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 17:06:04","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 57ms","timestamp":"2025-05-30 17:06:04"}
{"level":"http","message":"GraphQL anonymous completed in 256ms","timestamp":"2025-05-30 17:06:04"}
{"level":"http","message":"POST / 200 - 279ms","timestamp":"2025-05-30 17:06:04"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 17:06:09"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 17:06:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:06:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:06:15"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 17:06:15"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 17:06:15"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:06:15"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:06:15"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:06:15"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:06:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 17:06:15","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 61ms","timestamp":"2025-05-30 17:06:15"}
{"level":"http","message":"GraphQL anonymous completed in 159ms","timestamp":"2025-05-30 17:06:16"}
{"level":"http","message":"POST / 200 - 166ms","timestamp":"2025-05-30 17:06:16"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 97ms","timestamp":"2025-05-30 17:06:16"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:06:16","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:06:16"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 17:06:16"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 17:06:16"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:06:16"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:06:16"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:06:16"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:06:16"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 17:06:16","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 52ms","timestamp":"2025-05-30 17:06:16"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 49ms","timestamp":"2025-05-30 17:06:16"}
{"level":"http","message":"GraphQL anonymous completed in 133ms","timestamp":"2025-05-30 17:06:16"}
{"level":"http","message":"POST / 200 - 138ms","timestamp":"2025-05-30 17:06:16"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 60ms","timestamp":"2025-05-30 17:06:16"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 17:06:24"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 17:06:24"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-02.ciy4zys.mongodb.net","timestamp":"2025-05-30 17:06:25"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:06:45","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:06:45"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 17:06:45"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:06:46","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:06:46"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 17:06:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:07:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:07:15"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 17:07:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:07:16","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:07:16"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 17:07:16"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 17:07:23"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 17:07:23"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-00.ciy4zys.mongodb.net","timestamp":"2025-05-30 17:07:24"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:07:45","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:07:45"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 17:07:45"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:07:47","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:07:47"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 17:07:47"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:08:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:08:15"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 17:08:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:08:17","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:08:17"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 17:08:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:08:46","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:08:46"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 17:08:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:08:46","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:08:46"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 17:08:46"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 17:08:55"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 17:08:55"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 17:08:55"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 17:08:55"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 17:08:55"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 17:08:55"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:08:55"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:08:55"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:08:55"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:08:55"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-02.ciy4zys.mongodb.net","timestamp":"2025-05-30 17:08:58"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-02.ciy4zys.mongodb.net","timestamp":"2025-05-30 17:08:58"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 17:09:00"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:09:00"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:09:00"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:09:00"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 17:09:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:10:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 7ms","timestamp":"2025-05-30 17:10:01"}
{"level":"http","message":"POST / 200 - 23ms","timestamp":"2025-05-30 17:10:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:10:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:10:01"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 17:10:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:11:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:11:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 17:11:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:11:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:11:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 17:11:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:12:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:12:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 17:12:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:12:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:12:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 17:12:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:13:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:13:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 17:13:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:13:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:13:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 17:13:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:14:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:14:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 17:14:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:14:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:14:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 17:14:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:15:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:15:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 17:15:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:15:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:15:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 17:15:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:16:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:16:02"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 17:16:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:16:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:16:02"}
{"level":"http","message":"POST / 200 - 13ms","timestamp":"2025-05-30 17:16:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:17:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:17:02"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 17:17:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:17:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:17:02"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 17:17:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:18:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:18:02"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 17:18:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:18:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:18:02"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 17:18:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:19:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:19:01"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 17:19:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:19:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:19:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 17:19:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:20:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:20:02"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 17:20:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:20:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:20:02"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 17:20:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:21:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:21:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 17:21:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:21:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:21:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 17:21:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:22:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:22:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 17:22:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:22:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:22:01"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 17:22:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:23:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:23:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 17:23:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:23:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:23:02"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 17:23:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:24:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:24:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 17:24:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:24:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:24:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 17:24:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:25:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:25:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 17:25:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:25:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:25:01"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 17:25:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:26:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:26:01"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 17:26:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:26:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:26:01"}
{"level":"http","message":"POST / 200 - 37ms","timestamp":"2025-05-30 17:26:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:27:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:27:02"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 17:27:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:27:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 8ms","timestamp":"2025-05-30 17:27:02"}
{"level":"http","message":"POST / 200 - 38ms","timestamp":"2025-05-30 17:27:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:28:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:28:01"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 17:28:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:28:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:28:02"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 17:28:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:29:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:29:01"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 17:29:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:29:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:29:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 17:29:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:30:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:30:02"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 17:30:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:30:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:30:02"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 17:30:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:31:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:31:02"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 17:31:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:31:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:31:02"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 17:31:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:32:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 17:32:01"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 17:32:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 17:32:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 17:32:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 17:32:01"}
{"level":"info","message":"Client disconnected (1006): No reason provided","timestamp":"2025-05-30 17:32:40"}
{"level":"info","message":"Client disconnected (1006): No reason provided","timestamp":"2025-05-30 17:32:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 23:00:06","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 23:00:06"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 23:00:06"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 23:00:06"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:00:06"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:00:06"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:00:06"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:00:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 23:00:07","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 23:00:07"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 23:00:07"}
{"level":"warn","message":"[Database] MongoDB disconnected, attempting to reconnect...","timestamp":"2025-05-30 23:00:11"}
{"level":"warn","message":"[Database] MongoDB disconnected, attempting to reconnect...","timestamp":"2025-05-30 23:00:11"}
{"level":"info","message":"[Database] MongoDB reconnected successfully","timestamp":"2025-05-30 23:00:11"}
{"level":"info","message":"[Database] MongoDB reconnected successfully","timestamp":"2025-05-30 23:00:11"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 23:00:16"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-02.ciy4zys.mongodb.net","timestamp":"2025-05-30 23:00:16"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 23:00:16"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-02.ciy4zys.mongodb.net","timestamp":"2025-05-30 23:00:16"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 23:01:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 23:01:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 23:01:01"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 23:01:44"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:01:44"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:01:44"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:01:44"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:01:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 23:02:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 23:02:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 23:02:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 23:02:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 23:02:01"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 23:02:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 23:02:37","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 23:02:37"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 23:02:37"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 561ms","timestamp":"2025-05-30 23:02:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 23:02:45","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 23:02:45"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 23:02:45"}
{"level":"http","message":"DELETE /remove-profile-image?secret=2cinfo1&client=esprit 200 - 1108ms","timestamp":"2025-05-30 23:02:51"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 23:03:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 23:03:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 23:03:01"}
{"level":"http","message":"PUT /complete-profile 200 - 166ms","timestamp":"2025-05-30 23:03:04"}
{"level":"http","message":"PUT /complete-profile 200 - 148ms","timestamp":"2025-05-30 23:03:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 23:03:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 23:03:15"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 23:03:15"}
{"level":"http","message":"POST /upload-profile-image?secret=2cinfo1&client=esprit 200 - 3366ms","timestamp":"2025-05-30 23:03:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 23:03:31","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 345ms","timestamp":"2025-05-30 23:03:31"}
{"level":"http","message":"POST / 200 - 355ms","timestamp":"2025-05-30 23:03:31"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkNotificationsAsRead($notificationIds: [ID!]!) {\n  markNotificationsAsRead(notificationIds: $notificationIds) {\n    success\n    readCount\n    remainingCount\n  }\n}","timestamp":"2025-05-30 23:03:35","variables":{"notificationIds":["6839c3aeb3d00b25cf26afea"]}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-30 23:03:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 247ms","timestamp":"2025-05-30 23:03:36"}
{"level":"http","message":"POST / 200 - 250ms","timestamp":"2025-05-30 23:03:36"}
{"level":"http","message":"GraphQL anonymous completed in 633ms","timestamp":"2025-05-30 23:03:36"}
{"level":"http","message":"POST / 200 - 637ms","timestamp":"2025-05-30 23:03:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation CreateConversation($userId: ID!) {\n  createConversation(userId: $userId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-30 23:03:36","variables":{"userId":"6839c1fcb3d00b25cf26afa7"}}
{"level":"http","message":"GraphQL anonymous completed in 317ms","timestamp":"2025-05-30 23:03:36"}
{"level":"http","message":"POST / 200 - 322ms","timestamp":"2025-05-30 23:03:36"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 23:03:37"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 23:03:37","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 23:03:37"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 23:03:37"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 23:03:37"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:03:37"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:03:37"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:03:37"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:03:37"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 23:03:37","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 75ms","timestamp":"2025-05-30 23:03:38"}
{"level":"http","message":"GraphQL anonymous completed in 217ms","timestamp":"2025-05-30 23:03:38"}
{"level":"http","message":"POST / 200 - 223ms","timestamp":"2025-05-30 23:03:38"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 139ms","timestamp":"2025-05-30 23:03:38"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 23:03:42","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 225ms","timestamp":"2025-05-30 23:03:43"}
{"level":"http","message":"POST / 200 - 232ms","timestamp":"2025-05-30 23:03:43"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:03:55"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:03:55"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:03:55"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:03:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-30 23:03:55","variables":{}}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:03:55"}
{"level":"http","message":"GraphQL anonymous completed in 268ms","timestamp":"2025-05-30 23:03:55"}
{"level":"http","message":"POST / 200 - 274ms","timestamp":"2025-05-30 23:03:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-30 23:03:57","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 23:03:57","variables":{"conversationId":"6839c806f13093fc2d5643e1","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839c806f13093fc2d5643e1, userId=6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 23:03:57"}
{"level":"http","message":"GraphQL anonymous completed in 62ms","timestamp":"2025-05-30 23:03:57"}
{"level":"http","message":"POST / 200 - 67ms","timestamp":"2025-05-30 23:03:57"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839c806f13093fc2d5643e1, unread: 0, messages: 0","timestamp":"2025-05-30 23:03:58"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839c806f13093fc2d5643e1, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839b36810e260b7a7cbdecd, offset=0","timestamp":"2025-05-30 23:03:58"}
{"level":"info","message":"[MessageService] Retrieved 0 messages","timestamp":"2025-05-30 23:03:58"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 23:03:58"}
{"level":"http","message":"GraphQL anonymous completed in 459ms","timestamp":"2025-05-30 23:03:58"}
{"level":"http","message":"POST / 200 - 464ms","timestamp":"2025-05-30 23:03:58"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:03:58"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:03:58"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:03:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-30 23:04:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 259ms","timestamp":"2025-05-30 23:04:00"}
{"level":"http","message":"POST / 200 - 262ms","timestamp":"2025-05-30 23:04:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-30 23:04:01","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-30 23:04:01","variables":{"conversationId":"6839d14ef13093fc2d564499","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6839d14ef13093fc2d564499, userId=6839b36810e260b7a7cbdecd","timestamp":"2025-05-30 23:04:01"}
{"level":"http","message":"GraphQL anonymous completed in 67ms","timestamp":"2025-05-30 23:04:01"}
{"level":"http","message":"POST / 200 - 70ms","timestamp":"2025-05-30 23:04:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 23:04:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 23:04:01"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 23:04:01"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6839d14ef13093fc2d564499, unread: 0, messages: 6","timestamp":"2025-05-30 23:04:01"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6839d14ef13093fc2d564499, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=6839b36810e260b7a7cbdecd, offset=0","timestamp":"2025-05-30 23:04:01"}
{"level":"info","message":"[MessageService] Retrieved 6 messages","timestamp":"2025-05-30 23:04:02"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-30 23:04:02"}
{"level":"http","message":"GraphQL anonymous completed in 467ms","timestamp":"2025-05-30 23:04:02"}
{"level":"http","message":"POST / 200 - 477ms","timestamp":"2025-05-30 23:04:02"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:04:02"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:04:02"}
{"level":"info","message":"WebSocket operation for user 6839b36810e260b7a7cbdecd, operation: subscribe","timestamp":"2025-05-30 23:04:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 23:04:07","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 23:04:07"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 23:04:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 23:04:08","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 218ms","timestamp":"2025-05-30 23:04:08"}
{"level":"http","message":"POST / 200 - 228ms","timestamp":"2025-05-30 23:04:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkNotificationsAsRead($notificationIds: [ID!]!) {\n  markNotificationsAsRead(notificationIds: $notificationIds) {\n    success\n    readCount\n    remainingCount\n  }\n}","timestamp":"2025-05-30 23:04:10","variables":{"notificationIds":["6839c3aeb3d00b25cf26afea","6839d0cc72291558e41f45e2","6839ceed72291558e41f44fd","6839ce7772291558e41f44bb","6839ce3c72291558e41f4485","6839d1c872291558e41f4632","6839d166f13093fc2d5644b7","6839d1e472291558e41f4666","6839d1d472291558e41f464c","6839d751778d3d1a2842eb78","6839d746d612fb4054a32430","683a1e6f4c0aea7e4dd8c247","683a19cf3e1f79a3208738af","683a19463e1f79a32087387f","683a17b43e1f79a320873744","683a171a3e1f79a3208736c9","683a12c5e780b958457257f3","683a12aee780b958457257cd","683a102fe780b958457256e5","683a0ff6e780b958457256af","683a0faee780b95845725675","683a0e39e780b95845725576","683a0d87e780b958457254cc","683a0ce5e780b95845725460","683a0c13e780b95845725406","683a0b38e780b958457253c6","683a0a55e780b95845725381","683a0a2fe780b9584572535b","683a0931e780b958457252e7","683a08a6e780b95845725285","683a084ee780b9584572525c","683a082fe780b95845725236","683a03421dda30ddee6b9d64","683a03371dda30ddee6b9d3e","6839e320c7191ddb9d7ae81d","6839e26dc7191ddb9d7ae7d0","6839e06ec7191ddb9d7ae75c","6839dcf6c7191ddb9d7ae6d8","6839dcebc7191ddb9d7ae6b2","6839dccbc7191ddb9d7ae68c","6839daa4c7191ddb9d7ae5bd","6839d87ac7191ddb9d7ae515"]}}
{"level":"http","message":"GraphQL anonymous completed in 220ms","timestamp":"2025-05-30 23:04:10"}
{"level":"http","message":"POST / 200 - 225ms","timestamp":"2025-05-30 23:04:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation DeleteAllNotifications {\n  deleteAllNotifications {\n    success\n    count\n    message\n  }\n}","timestamp":"2025-05-30 23:04:12","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 139ms","timestamp":"2025-05-30 23:04:13"}
{"level":"http","message":"POST / 200 - 144ms","timestamp":"2025-05-30 23:04:13"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 60ms","timestamp":"2025-05-30 23:04:17"}
{"level":"http","message":"PUT /logout?secret=2cinfo1&client=esprit 200 - 113ms","timestamp":"2025-05-30 23:04:32"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 23:04:37","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 23:04:37"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 23:04:37"}
{"level":"http","message":"POST /login 200 - 248ms","timestamp":"2025-05-30 23:04:45"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 55ms","timestamp":"2025-05-30 23:04:54"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 23:05:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 23:05:01"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 23:05:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 23:05:07","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 23:05:07"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 23:05:07"}
{"level":"http","message":"GET /users 200 - 84ms","timestamp":"2025-05-30 23:05:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 23:05:37","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 23:05:37"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 23:05:37"}
{"level":"http","message":"GET /getone/683a2a37cfb365efa3a80b5e?secret=2cinfo1&client=esprit 200 - 72ms","timestamp":"2025-05-30 23:05:42"}
{"level":"http","message":"GET /users 304 - 73ms","timestamp":"2025-05-30 23:05:46"}
{"level":"http","message":"GET /getone/6839b466666b7ee01e8b5bd2?secret=2cinfo1&client=esprit 200 - 60ms","timestamp":"2025-05-30 23:05:54"}
{"level":"http","message":"GET /users 304 - 77ms","timestamp":"2025-05-30 23:06:00"}
{"level":"http","message":"GET /users 304 - 83ms","timestamp":"2025-05-30 23:06:01"}
{"level":"http","message":"GET /profile 200 - 76ms","timestamp":"2025-05-30 23:06:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 23:06:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 23:06:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 23:06:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 23:06:07","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 23:06:07"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 23:06:07"}
