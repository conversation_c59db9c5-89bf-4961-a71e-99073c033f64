{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, HostListener } from '@angular/core';\nimport { trigger, transition, style, animate } from '@angular/animations';\nexport let AdminLayoutComponent = class AdminLayoutComponent {\n  constructor(location, authAdminService, authService, router, themeService, dataService) {\n    this.location = location;\n    this.authAdminService = authAdminService;\n    this.authService = authService;\n    this.router = router;\n    this.themeService = themeService;\n    this.dataService = dataService;\n    this.username = '';\n    this.imageProfile = '';\n    this.mobileMenuOpen = false;\n    this.userMenuOpen = false;\n    this.showLogoutModal = false;\n    this.showScrollButton = false;\n    this.currentYear = new Date().getFullYear();\n    this.subscriptions = [];\n    this.loadUserProfile();\n    this.isDarkMode$ = this.themeService.darkMode$;\n  }\n  loadUserProfile() {\n    const user = this.authAdminService.getUser();\n    this.username = user?.fullName || user?.username || '';\n    // Toujours utiliser l'image par défaut si l'image de profil est null, 'null' ou vide\n    if (user?.profileImage && user.profileImage !== 'null' && user.profileImage.trim() !== '') {\n      this.imageProfile = user.profileImage;\n    } else if (user?.image && user.image !== 'null' && user.image.trim() !== '') {\n      this.imageProfile = user.image;\n    } else {\n      this.imageProfile = 'assets/images/default-profile.png';\n    }\n    console.log('Admin layout - Image profile loaded:', this.imageProfile);\n  }\n  ngOnInit() {\n    this.checkScrollPosition();\n    // S'abonner aux changements d'image de profil\n    const profileSub = this.dataService.currentUser$.subscribe(user => {\n      if (user) {\n        this.username = user.fullName || user.username || '';\n        // Toujours utiliser l'image par défaut si l'image de profil est null, 'null' ou vide\n        if (user.profileImage && user.profileImage !== 'null' && user.profileImage.trim() !== '') {\n          this.imageProfile = user.profileImage;\n        } else if (user.image && user.image !== 'null' && user.image.trim() !== '') {\n          this.imageProfile = user.image;\n        } else {\n          this.imageProfile = 'assets/images/default-profile.png';\n        }\n        console.log('Admin layout - Image profile updated:', this.imageProfile);\n      }\n    });\n    this.subscriptions.push(profileSub);\n  }\n  ngOnDestroy() {\n    // Désabonner de tous les observables pour éviter les fuites de mémoire\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  checkScrollPosition() {\n    this.showScrollButton = window.pageYOffset > 300;\n  }\n  scrollToTop() {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  }\n  toggleMobileMenu() {\n    this.mobileMenuOpen = !this.mobileMenuOpen;\n  }\n  toggleUserMenu() {\n    this.userMenuOpen = !this.userMenuOpen;\n  }\n  openLogoutModal() {\n    this.showLogoutModal = true;\n    this.userMenuOpen = false;\n  }\n  closeLogoutModal() {\n    this.showLogoutModal = false;\n  }\n  logout() {\n    this.authService.logout().subscribe({\n      next: () => {\n        this.userMenuOpen = false;\n        this.showLogoutModal = false;\n        this.authService.clearAuthData();\n        this.authAdminService.clearAuthData();\n        setTimeout(() => {\n          this.router.navigate(['/admin/login'], {\n            queryParams: {\n              message: 'Déconnexion réussie'\n            },\n            replaceUrl: true\n          });\n        }, 100);\n      },\n      error: err => {\n        console.error('Logout error:', err);\n        this.authService.clearAuthData();\n        this.authAdminService.clearAuthData();\n        setTimeout(() => {\n          this.router.navigate(['/admin/login'], {\n            queryParams: {\n              message: 'Déconnexion effectuée'\n            },\n            replaceUrl: true\n          });\n        }, 100);\n      }\n    });\n  }\n  goBack() {\n    this.location.back();\n  }\n  toggleDarkMode() {\n    this.themeService.toggleDarkMode();\n  }\n};\n__decorate([HostListener('window:scroll')], AdminLayoutComponent.prototype, \"checkScrollPosition\", null);\nAdminLayoutComponent = __decorate([Component({\n  selector: 'app-admin-layout',\n  templateUrl: './admin-layout.component.html',\n  styleUrls: ['./admin-layout.component.css'],\n  animations: [trigger('fadeIn', [transition(':enter', [style({\n    opacity: 0,\n    transform: 'translateY(-10px)'\n  }), animate('150ms ease-out', style({\n    opacity: 1,\n    transform: 'translateY(0)'\n  }))]), transition(':leave', [animate('100ms ease-in', style({\n    opacity: 0,\n    transform: 'translateY(-10px)'\n  }))])])]\n})], AdminLayoutComponent);", "map": {"version": 3, "names": ["Component", "HostListener", "trigger", "transition", "style", "animate", "AdminLayoutComponent", "constructor", "location", "authAdminService", "authService", "router", "themeService", "dataService", "username", "imageProfile", "mobileMenuOpen", "userMenuOpen", "showLogoutModal", "showScrollButton", "currentYear", "Date", "getFullYear", "subscriptions", "loadUserProfile", "isDarkMode$", "darkMode$", "user", "getUser", "fullName", "profileImage", "trim", "image", "console", "log", "ngOnInit", "checkScrollPosition", "profileSub", "currentUser$", "subscribe", "push", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "window", "pageYOffset", "scrollToTop", "scrollTo", "top", "behavior", "toggleMobileMenu", "toggleUserMenu", "openLogoutModal", "closeLogoutModal", "logout", "next", "clearAuthData", "setTimeout", "navigate", "queryParams", "message", "replaceUrl", "error", "err", "goBack", "back", "toggleDarkMode", "__decorate", "selector", "templateUrl", "styleUrls", "animations", "opacity", "transform"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\layouts\\admin-layout\\admin-layout.component.ts"], "sourcesContent": ["import { Location } from '@angular/common';\r\nimport { Component, HostListener, OnInit, OnDestroy } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { AuthadminService } from 'src/app/services/authadmin.service';\r\nimport { trigger, transition, style, animate } from '@angular/animations';\r\nimport { AuthuserService } from 'src/app/services/authuser.service';\r\nimport { ThemeService } from '@app/services/theme.service';\r\nimport { Observable, Subscription } from 'rxjs';\r\nimport { DataService } from 'src/app/services/data.service';\r\nimport { User } from 'src/app/models/user.model';\r\n@Component({\r\n  selector: 'app-admin-layout',\r\n  templateUrl: './admin-layout.component.html',\r\n  styleUrls: ['./admin-layout.component.css'],\r\n  animations: [\r\n    trigger('fadeIn', [\r\n      transition(':enter', [\r\n        style({ opacity: 0, transform: 'translateY(-10px)' }),\r\n        animate(\r\n          '150ms ease-out',\r\n          style({ opacity: 1, transform: 'translateY(0)' })\r\n        ),\r\n      ]),\r\n      transition(':leave', [\r\n        animate(\r\n          '100ms ease-in',\r\n          style({ opacity: 0, transform: 'translateY(-10px)' })\r\n        ),\r\n      ]),\r\n    ]),\r\n  ],\r\n})\r\nexport class AdminLayoutComponent implements OnInit, OnDestroy {\r\n  username: string = '';\r\n  imageProfile: string = '';\r\n  mobileMenuOpen = false;\r\n  userMenuOpen = false;\r\n  showLogoutModal = false;\r\n  showScrollButton = false;\r\n  currentYear = new Date().getFullYear();\r\n  isDarkMode$: Observable<boolean>;\r\n  private subscriptions: Subscription[] = [];\r\n\r\n  constructor(\r\n    private location: Location,\r\n    private authAdminService: AuthadminService,\r\n    private authService: AuthuserService,\r\n    private router: Router,\r\n    private themeService: ThemeService,\r\n    private dataService: DataService\r\n  ) {\r\n    this.loadUserProfile();\r\n    this.isDarkMode$ = this.themeService.darkMode$;\r\n  }\r\n\r\n  private loadUserProfile(): void {\r\n    const user = this.authAdminService.getUser();\r\n    this.username = user?.fullName || user?.username || '';\r\n\r\n    // Toujours utiliser l'image par défaut si l'image de profil est null, 'null' ou vide\r\n    if (\r\n      user?.profileImage &&\r\n      user.profileImage !== 'null' &&\r\n      user.profileImage.trim() !== ''\r\n    ) {\r\n      this.imageProfile = user.profileImage;\r\n    } else if (\r\n      user?.image &&\r\n      user.image !== 'null' &&\r\n      user.image.trim() !== ''\r\n    ) {\r\n      this.imageProfile = user.image;\r\n    } else {\r\n      this.imageProfile = 'assets/images/default-profile.png';\r\n    }\r\n\r\n    console.log('Admin layout - Image profile loaded:', this.imageProfile);\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.checkScrollPosition();\r\n\r\n    // S'abonner aux changements d'image de profil\r\n    const profileSub = this.dataService.currentUser$.subscribe(\r\n      (user: User | null) => {\r\n        if (user) {\r\n          this.username = user.fullName || user.username || '';\r\n\r\n          // Toujours utiliser l'image par défaut si l'image de profil est null, 'null' ou vide\r\n          if (\r\n            user.profileImage &&\r\n            user.profileImage !== 'null' &&\r\n            user.profileImage.trim() !== ''\r\n          ) {\r\n            this.imageProfile = user.profileImage;\r\n          } else if (\r\n            user.image &&\r\n            user.image !== 'null' &&\r\n            user.image.trim() !== ''\r\n          ) {\r\n            this.imageProfile = user.image;\r\n          } else {\r\n            this.imageProfile = 'assets/images/default-profile.png';\r\n          }\r\n\r\n          console.log(\r\n            'Admin layout - Image profile updated:',\r\n            this.imageProfile\r\n          );\r\n        }\r\n      }\r\n    );\r\n\r\n    this.subscriptions.push(profileSub);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Désabonner de tous les observables pour éviter les fuites de mémoire\r\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\r\n  }\r\n\r\n  @HostListener('window:scroll')\r\n  checkScrollPosition(): void {\r\n    this.showScrollButton = window.pageYOffset > 300;\r\n  }\r\n\r\n  scrollToTop(): void {\r\n    window.scrollTo({ top: 0, behavior: 'smooth' });\r\n  }\r\n\r\n  toggleMobileMenu(): void {\r\n    this.mobileMenuOpen = !this.mobileMenuOpen;\r\n  }\r\n\r\n  toggleUserMenu(): void {\r\n    this.userMenuOpen = !this.userMenuOpen;\r\n  }\r\n\r\n  openLogoutModal(): void {\r\n    this.showLogoutModal = true;\r\n    this.userMenuOpen = false;\r\n  }\r\n\r\n  closeLogoutModal(): void {\r\n    this.showLogoutModal = false;\r\n  }\r\n  logout(): void {\r\n    this.authService.logout().subscribe({\r\n      next: () => {\r\n        this.userMenuOpen = false;\r\n        this.showLogoutModal = false;\r\n        this.authService.clearAuthData();\r\n        this.authAdminService.clearAuthData();\r\n        setTimeout(() => {\r\n          this.router.navigate(['/admin/login'], {\r\n            queryParams: { message: 'Déconnexion réussie' },\r\n            replaceUrl: true,\r\n          });\r\n        }, 100);\r\n      },\r\n      error: (err) => {\r\n        console.error('Logout error:', err);\r\n        this.authService.clearAuthData();\r\n        this.authAdminService.clearAuthData();\r\n        setTimeout(() => {\r\n          this.router.navigate(['/admin/login'], {\r\n            queryParams: { message: 'Déconnexion effectuée' },\r\n            replaceUrl: true,\r\n          });\r\n        }, 100);\r\n      },\r\n    });\r\n  }\r\n\r\n  goBack(): void {\r\n    this.location.back();\r\n  }\r\n\r\n  toggleDarkMode(): void {\r\n    this.themeService.toggleDarkMode();\r\n  }\r\n}\r\n"], "mappings": ";AACA,SAASA,SAAS,EAAEC,YAAY,QAA2B,eAAe;AAG1E,SAASC,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,QAAQ,qBAAqB;AA4BlE,WAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAW/BC,YACUC,QAAkB,EAClBC,gBAAkC,EAClCC,WAA4B,EAC5BC,MAAc,EACdC,YAA0B,EAC1BC,WAAwB;IALxB,KAAAL,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IAhBrB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,WAAW,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IAE9B,KAAAC,aAAa,GAAmB,EAAE;IAUxC,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,WAAW,GAAG,IAAI,CAACb,YAAY,CAACc,SAAS;EAChD;EAEQF,eAAeA,CAAA;IACrB,MAAMG,IAAI,GAAG,IAAI,CAAClB,gBAAgB,CAACmB,OAAO,EAAE;IAC5C,IAAI,CAACd,QAAQ,GAAGa,IAAI,EAAEE,QAAQ,IAAIF,IAAI,EAAEb,QAAQ,IAAI,EAAE;IAEtD;IACA,IACEa,IAAI,EAAEG,YAAY,IAClBH,IAAI,CAACG,YAAY,KAAK,MAAM,IAC5BH,IAAI,CAACG,YAAY,CAACC,IAAI,EAAE,KAAK,EAAE,EAC/B;MACA,IAAI,CAAChB,YAAY,GAAGY,IAAI,CAACG,YAAY;KACtC,MAAM,IACLH,IAAI,EAAEK,KAAK,IACXL,IAAI,CAACK,KAAK,KAAK,MAAM,IACrBL,IAAI,CAACK,KAAK,CAACD,IAAI,EAAE,KAAK,EAAE,EACxB;MACA,IAAI,CAAChB,YAAY,GAAGY,IAAI,CAACK,KAAK;KAC/B,MAAM;MACL,IAAI,CAACjB,YAAY,GAAG,mCAAmC;;IAGzDkB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACnB,YAAY,CAAC;EACxE;EAEAoB,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;IAE1B;IACA,MAAMC,UAAU,GAAG,IAAI,CAACxB,WAAW,CAACyB,YAAY,CAACC,SAAS,CACvDZ,IAAiB,IAAI;MACpB,IAAIA,IAAI,EAAE;QACR,IAAI,CAACb,QAAQ,GAAGa,IAAI,CAACE,QAAQ,IAAIF,IAAI,CAACb,QAAQ,IAAI,EAAE;QAEpD;QACA,IACEa,IAAI,CAACG,YAAY,IACjBH,IAAI,CAACG,YAAY,KAAK,MAAM,IAC5BH,IAAI,CAACG,YAAY,CAACC,IAAI,EAAE,KAAK,EAAE,EAC/B;UACA,IAAI,CAAChB,YAAY,GAAGY,IAAI,CAACG,YAAY;SACtC,MAAM,IACLH,IAAI,CAACK,KAAK,IACVL,IAAI,CAACK,KAAK,KAAK,MAAM,IACrBL,IAAI,CAACK,KAAK,CAACD,IAAI,EAAE,KAAK,EAAE,EACxB;UACA,IAAI,CAAChB,YAAY,GAAGY,IAAI,CAACK,KAAK;SAC/B,MAAM;UACL,IAAI,CAACjB,YAAY,GAAG,mCAAmC;;QAGzDkB,OAAO,CAACC,GAAG,CACT,uCAAuC,EACvC,IAAI,CAACnB,YAAY,CAClB;;IAEL,CAAC,CACF;IAED,IAAI,CAACQ,aAAa,CAACiB,IAAI,CAACH,UAAU,CAAC;EACrC;EAEAI,WAAWA,CAAA;IACT;IACA,IAAI,CAAClB,aAAa,CAACmB,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;EACxD;EAGAR,mBAAmBA,CAAA;IACjB,IAAI,CAACjB,gBAAgB,GAAG0B,MAAM,CAACC,WAAW,GAAG,GAAG;EAClD;EAEAC,WAAWA,CAAA;IACTF,MAAM,CAACG,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAQ,CAAE,CAAC;EACjD;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAACnC,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;EAC5C;EAEAoC,cAAcA,CAAA;IACZ,IAAI,CAACnC,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAoC,eAAeA,CAAA;IACb,IAAI,CAACnC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACD,YAAY,GAAG,KAAK;EAC3B;EAEAqC,gBAAgBA,CAAA;IACd,IAAI,CAACpC,eAAe,GAAG,KAAK;EAC9B;EACAqC,MAAMA,CAAA;IACJ,IAAI,CAAC7C,WAAW,CAAC6C,MAAM,EAAE,CAAChB,SAAS,CAAC;MAClCiB,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACvC,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACR,WAAW,CAAC+C,aAAa,EAAE;QAChC,IAAI,CAAChD,gBAAgB,CAACgD,aAAa,EAAE;QACrCC,UAAU,CAAC,MAAK;UACd,IAAI,CAAC/C,MAAM,CAACgD,QAAQ,CAAC,CAAC,cAAc,CAAC,EAAE;YACrCC,WAAW,EAAE;cAAEC,OAAO,EAAE;YAAqB,CAAE;YAC/CC,UAAU,EAAE;WACb,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDC,KAAK,EAAGC,GAAG,IAAI;QACb/B,OAAO,CAAC8B,KAAK,CAAC,eAAe,EAAEC,GAAG,CAAC;QACnC,IAAI,CAACtD,WAAW,CAAC+C,aAAa,EAAE;QAChC,IAAI,CAAChD,gBAAgB,CAACgD,aAAa,EAAE;QACrCC,UAAU,CAAC,MAAK;UACd,IAAI,CAAC/C,MAAM,CAACgD,QAAQ,CAAC,CAAC,cAAc,CAAC,EAAE;YACrCC,WAAW,EAAE;cAAEC,OAAO,EAAE;YAAuB,CAAE;YACjDC,UAAU,EAAE;WACb,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT;KACD,CAAC;EACJ;EAEAG,MAAMA,CAAA;IACJ,IAAI,CAACzD,QAAQ,CAAC0D,IAAI,EAAE;EACtB;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACvD,YAAY,CAACuD,cAAc,EAAE;EACpC;CACD;AA3DCC,UAAA,EADCnE,YAAY,CAAC,eAAe,CAAC,C,8DAG7B;AA5FUK,oBAAoB,GAAA8D,UAAA,EAtBhCpE,SAAS,CAAC;EACTqE,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,+BAA+B;EAC5CC,SAAS,EAAE,CAAC,8BAA8B,CAAC;EAC3CC,UAAU,EAAE,CACVtE,OAAO,CAAC,QAAQ,EAAE,CAChBC,UAAU,CAAC,QAAQ,EAAE,CACnBC,KAAK,CAAC;IAAEqE,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAmB,CAAE,CAAC,EACrDrE,OAAO,CACL,gBAAgB,EAChBD,KAAK,CAAC;IAAEqE,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAe,CAAE,CAAC,CAClD,CACF,CAAC,EACFvE,UAAU,CAAC,QAAQ,EAAE,CACnBE,OAAO,CACL,eAAe,EACfD,KAAK,CAAC;IAAEqE,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAmB,CAAE,CAAC,CACtD,CACF,CAAC,CACH,CAAC;CAEL,CAAC,C,EACWpE,oBAAoB,CAqJhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}