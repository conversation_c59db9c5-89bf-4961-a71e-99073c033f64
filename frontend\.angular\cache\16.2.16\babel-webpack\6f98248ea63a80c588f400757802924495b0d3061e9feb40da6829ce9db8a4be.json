{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/auth.service\";\nimport * as i2 from \"src/app/services/authuser.service\";\nimport * as i3 from \"src/app/services/data.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/common\";\nfunction ProfileComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18);\n    i0.ɵɵelement(2, \"div\", 19)(3, \"div\", 20);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProfileComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"div\", 23);\n    i0.ɵɵelement(3, \"i\", 24)(4, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h3\", 26);\n    i0.ɵɵtext(7, \" Erreur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 27);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction ProfileComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 22)(2, \"div\", 29);\n    i0.ɵɵelement(3, \"i\", 30)(4, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h3\", 31);\n    i0.ɵɵtext(7, \" Succ\\u00E8s \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 27);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.message, \" \");\n  }\n}\nfunction ProfileComponent_div_26_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_26_div_12_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.toggleEditMode());\n    });\n    i0.ɵɵtext(2, \" Complete Profile \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProfileComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 32);\n    i0.ɵɵelement(2, \"div\", 33);\n    i0.ɵɵelementStart(3, \"div\", 34)(4, \"h3\", 35);\n    i0.ɵɵtext(5, \" Profile Completion \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 36);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 37);\n    i0.ɵɵelement(9, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 27);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, ProfileComponent_div_26_div_12_Template, 3, 0, \"div\", 39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵstyleProp(\"color\", ctx_r3.getProgressColor());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.progressPercentage, \"% \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.progressPercentage, \"%\")(\"background-color\", ctx_r3.getProgressColor());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getMotivationalMessage(), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.progressPercentage < 100);\n  }\n}\nfunction ProfileComponent_div_27_button_13_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 75);\n  }\n}\nfunction ProfileComponent_div_27_button_13_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 76);\n  }\n}\nfunction ProfileComponent_div_27_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.onUpload());\n    });\n    i0.ɵɵtemplate(1, ProfileComponent_div_27_button_13_i_1_Template, 1, 0, \"i\", 73);\n    i0.ɵɵtemplate(2, ProfileComponent_div_27_button_13_i_2_Template, 1, 0, \"i\", 74);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r9.uploadLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.uploadLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.uploadLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.uploadLoading ? \"Uploading...\" : \"Upload\", \" \");\n  }\n}\nfunction ProfileComponent_div_27_button_14_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 79);\n  }\n}\nfunction ProfileComponent_div_27_button_14_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 76);\n  }\n}\nfunction ProfileComponent_div_27_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.removeProfileImage());\n    });\n    i0.ɵɵtemplate(1, ProfileComponent_div_27_button_14_i_1_Template, 1, 0, \"i\", 78);\n    i0.ɵɵtemplate(2, ProfileComponent_div_27_button_14_i_2_Template, 1, 0, \"i\", 74);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.removeLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.removeLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.removeLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.removeLoading ? \"Removing...\" : \"Remove\", \" \");\n  }\n}\nfunction ProfileComponent_div_27_span_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.user.department, \" \");\n  }\n}\nfunction ProfileComponent_div_27_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.user.position, \" \");\n  }\n}\nfunction ProfileComponent_div_27_p_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 82);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.user.bio.length > 150 ? i0.ɵɵpipeBind3(2, 1, ctx_r13.user.bio, 0, 150) + \"...\" : ctx_r13.user.bio, \" \");\n  }\n}\nfunction ProfileComponent_div_27_button_36_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 85);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r28.progressPercentage, \"% \");\n  }\n}\nfunction ProfileComponent_div_27_button_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_button_36_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r30);\n      const tab_r27 = restoredCtx.$implicit;\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.activeTab = tab_r27.id);\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵtext(2);\n    i0.ɵɵtemplate(3, ProfileComponent_div_27_button_36_span_3_Template, 2, 1, \"span\", 84);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r27 = ctx.$implicit;\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"bg-gradient-to-r\", ctx_r14.activeTab === tab_r27.id)(\"from-[#4f5fad]\", ctx_r14.activeTab === tab_r27.id)(\"to-[#7826b5]\", ctx_r14.activeTab === tab_r27.id)(\"text-white\", ctx_r14.activeTab === tab_r27.id)(\"text-[#6d6870]\", ctx_r14.activeTab !== tab_r27.id)(\"dark:text-[#a0a0a0]\", ctx_r14.activeTab !== tab_r27.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(tab_r27.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tab_r27.label, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", tab_r27.id === \"completion\" && ctx_r14.progressPercentage < 100);\n  }\n}\nfunction ProfileComponent_div_27_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 86)(2, \"h3\", 87);\n    i0.ɵɵelement(3, \"i\", 88);\n    i0.ɵɵtext(4, \" Personal Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_div_38_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.setEditSection(\"personal\"));\n    });\n    i0.ɵɵelement(6, \"i\", 90);\n    i0.ɵɵtext(7, \" Edit \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 91)(9, \"div\", 92)(10, \"label\", 93);\n    i0.ɵɵtext(11, \"First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 94);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 92)(15, \"label\", 93);\n    i0.ɵɵtext(16, \"Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\", 94);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 92)(20, \"label\", 93);\n    i0.ɵɵtext(21, \"Full Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\", 94);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 92)(25, \"label\", 93);\n    i0.ɵɵtext(26, \"Date of Birth\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\", 94);\n    i0.ɵɵtext(28);\n    i0.ɵɵpipe(29, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 92)(31, \"label\", 93);\n    i0.ɵɵtext(32, \"Phone Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p\", 94);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 92)(36, \"label\", 93);\n    i0.ɵɵtext(37, \"Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"p\", 94);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(40, \"div\", 92)(41, \"label\", 95);\n    i0.ɵɵtext(42, \"Bio\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"p\", 96);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(ctx_r15.user.firstName || \"Not provided\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r15.user.lastName || \"Not provided\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r15.user.fullName || \"Not provided\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.user.dateOfBirth ? i0.ɵɵpipeBind2(29, 7, ctx_r15.user.dateOfBirth, \"mediumDate\") : \"Not provided\", \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r15.user.phoneNumber || \"Not provided\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r15.user.address || \"Not provided\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.user.bio || \"Tell us about yourself, your interests, and goals...\", \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_39_div_34_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 103);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const skill_r37 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", skill_r37, \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_39_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101);\n    i0.ɵɵtemplate(1, ProfileComponent_div_27_div_39_div_34_span_1_Template, 2, 1, \"span\", 102);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r33.user.skills);\n  }\n}\nfunction ProfileComponent_div_27_div_39_ng_template_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 104);\n    i0.ɵɵtext(1, \"No skills added yet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_27_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 86)(2, \"h3\", 87);\n    i0.ɵɵelement(3, \"i\", 97);\n    i0.ɵɵtext(4, \" Professional Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_div_39_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r38 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r38.setEditSection(\"professional\"));\n    });\n    i0.ɵɵelement(6, \"i\", 90);\n    i0.ɵɵtext(7, \" Edit \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 91)(9, \"div\", 92)(10, \"label\", 93);\n    i0.ɵɵtext(11, \"Department\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 94);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 92)(15, \"label\", 93);\n    i0.ɵɵtext(16, \"Position\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\", 94);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 92)(20, \"label\", 93);\n    i0.ɵɵtext(21, \"Role\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\", 94);\n    i0.ɵɵtext(23);\n    i0.ɵɵpipe(24, \"titlecase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 92)(26, \"label\", 93);\n    i0.ɵɵtext(27, \"Member Since\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"p\", 94);\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 92)(32, \"label\", 98);\n    i0.ɵɵtext(33, \"Skills & Expertise\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(34, ProfileComponent_div_27_div_39_div_34_Template, 2, 1, \"div\", 99);\n    i0.ɵɵtemplate(35, ProfileComponent_div_27_div_39_ng_template_35_Template, 2, 0, \"ng-template\", null, 100, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const _r34 = i0.ɵɵreference(36);\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(ctx_r16.user.department || \"Not specified\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r16.user.position || \"Not specified\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(24, 6, ctx_r16.user.role));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(30, 8, ctx_r16.user.createdAt, \"mediumDate\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.user.skills && ctx_r16.user.skills.length > 0)(\"ngIfElse\", _r34);\n  }\n}\nfunction ProfileComponent_div_27_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 86)(2, \"h3\", 87);\n    i0.ɵɵelement(3, \"i\", 105);\n    i0.ɵɵtext(4, \" Account Settings \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 91)(6, \"div\", 92)(7, \"label\", 93);\n    i0.ɵɵtext(8, \"Email Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 94);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\", 106);\n    i0.ɵɵtext(12, \"Primary contact email\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 92)(14, \"label\", 93);\n    i0.ɵɵtext(15, \"Account Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 107)(17, \"span\", 108);\n    i0.ɵɵtext(18, \" Active \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 94);\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"titlecase\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(22, \"div\", 109)(23, \"h4\", 110);\n    i0.ɵɵtext(24, \"Account Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 111)(26, \"a\", 112);\n    i0.ɵɵelement(27, \"i\", 113);\n    i0.ɵɵtext(28, \" Change Password \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_div_40_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r40 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r40.logout());\n    });\n    i0.ɵɵelement(30, \"i\", 115);\n    i0.ɵɵtext(31, \" Logout \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"a\", 116);\n    i0.ɵɵelement(33, \"i\", 67);\n    i0.ɵɵtext(34, \" Go to Dashboard \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r17.user.email);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(21, 3, ctx_r17.user.role));\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"routerLink\", ctx_r17.user.role === \"admin\" ? \"/admin/dashboard\" : \"/home\");\n  }\n}\nfunction ProfileComponent_div_27_div_41_div_37_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 137)(1, \"span\", 138);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 139);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const field_r44 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(field_r44.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", field_r44.required ? \"Required\" : \"Optional\", \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_41_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 131)(1, \"h4\", 132);\n    i0.ɵɵelement(2, \"i\", 133);\n    i0.ɵɵtext(3, \" Missing Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 134);\n    i0.ɵɵtemplate(5, ProfileComponent_div_27_div_41_div_37_div_5_Template, 5, 2, \"div\", 135);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 136);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_div_41_div_37_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r45 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r45.activeTab = \"personal\");\n    });\n    i0.ɵɵtext(7, \" Complete Missing Fields \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r42.getMissingFields());\n  }\n}\nfunction ProfileComponent_div_27_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 86)(2, \"h3\", 87);\n    i0.ɵɵelement(3, \"i\", 117);\n    i0.ɵɵtext(4, \" Profile Completion \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 36);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 109)(8, \"div\", 34)(9, \"h4\", 118);\n    i0.ɵɵtext(10, \"Overall Progress\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 27);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 119);\n    i0.ɵɵelement(14, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 120)(16, \"div\", 121)(17, \"div\", 122);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 27);\n    i0.ɵɵtext(20, \"Required Fields\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 106);\n    i0.ɵɵtext(22, \"(60% weight)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 121)(24, \"div\", 123);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 27);\n    i0.ɵɵtext(27, \"Optional Fields\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 106);\n    i0.ɵɵtext(29, \"(30% weight)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 121)(31, \"div\", 124);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 27);\n    i0.ɵɵtext(34, \"Profile Image\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 106);\n    i0.ɵɵtext(36, \"(10% weight)\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(37, ProfileComponent_div_27_div_41_div_37_Template, 8, 1, \"div\", 125);\n    i0.ɵɵelementStart(38, \"div\", 126)(39, \"h4\", 127);\n    i0.ɵɵelement(40, \"i\", 128);\n    i0.ɵɵtext(41, \" Tips for a Complete Profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"ul\", 129)(43, \"li\", 22);\n    i0.ɵɵelement(44, \"i\", 130);\n    i0.ɵɵtext(45, \" Add a professional profile photo to help others recognize you \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"li\", 22);\n    i0.ɵɵelement(47, \"i\", 130);\n    i0.ɵɵtext(48, \" Write a compelling bio that showcases your interests and goals \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"li\", 22);\n    i0.ɵɵelement(50, \"i\", 130);\n    i0.ɵɵtext(51, \" List your skills to help with project matching and collaboration \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"li\", 22);\n    i0.ɵɵelement(53, \"i\", 130);\n    i0.ɵɵtext(54, \" Keep your contact information up to date \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵstyleProp(\"color\", ctx_r18.getProgressColor());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.progressPercentage, \"% \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r18.getMotivationalMessage());\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r18.progressPercentage, \"%\")(\"background-color\", ctx_r18.getProgressColor());\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r18.getRequiredFieldsCompletion(), \"%\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r18.getOptionalFieldsCompletion(), \"%\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r18.hasProfileImage() ? 100 : 0, \"%\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.getMissingFields().length > 0);\n  }\n}\nfunction ProfileComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 43);\n    i0.ɵɵelement(2, \"div\", 33);\n    i0.ɵɵelementStart(3, \"div\", 44)(4, \"div\", 45)(5, \"div\", 46);\n    i0.ɵɵelement(6, \"img\", 47);\n    i0.ɵɵelementStart(7, \"div\", 48)(8, \"label\", 49);\n    i0.ɵɵelement(9, \"i\", 50);\n    i0.ɵɵtext(10, \" Change Photo \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 51);\n    i0.ɵɵlistener(\"change\", function ProfileComponent_div_27_Template_input_change_11_listener($event) {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r47 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r47.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 52);\n    i0.ɵɵtemplate(13, ProfileComponent_div_27_button_13_Template, 4, 4, \"button\", 53);\n    i0.ɵɵtemplate(14, ProfileComponent_div_27_button_14_Template, 4, 4, \"button\", 54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 55)(16, \"h2\", 56);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 57);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 58)(21, \"span\", 59);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, ProfileComponent_div_27_span_24_Template, 2, 1, \"span\", 60);\n    i0.ɵɵtemplate(25, ProfileComponent_div_27_span_25_Template, 2, 1, \"span\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, ProfileComponent_div_27_p_26_Template, 3, 5, \"p\", 62);\n    i0.ɵɵelementStart(27, \"div\", 63)(28, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r49 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r49.toggleEditMode());\n    });\n    i0.ɵɵelement(29, \"i\", 65);\n    i0.ɵɵtext(30, \" Edit Profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"a\", 66);\n    i0.ɵɵelement(32, \"i\", 67);\n    i0.ɵɵtext(33, \" Dashboard \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(34, \"div\", 68)(35, \"div\", 69);\n    i0.ɵɵtemplate(36, ProfileComponent_div_27_button_36_Template, 4, 16, \"button\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 71);\n    i0.ɵɵtemplate(38, ProfileComponent_div_27_div_38_Template, 45, 10, \"div\", 15);\n    i0.ɵɵtemplate(39, ProfileComponent_div_27_div_39_Template, 37, 11, \"div\", 15);\n    i0.ɵɵtemplate(40, ProfileComponent_div_27_div_40_Template, 35, 5, \"div\", 15);\n    i0.ɵɵtemplate(41, ProfileComponent_div_27_div_41_Template, 55, 12, \"div\", 15);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", ctx_r4.getProfileImageUrl(), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedImage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.profileImage && ctx_r4.user.profileImage !== \"assets/images/default-profile.png\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.user.fullName || ctx_r4.user.firstName + \" \" + ctx_r4.user.lastName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.user.email);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(23, 15, ctx_r4.user.role), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.department);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.position);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.bio);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"routerLink\", ctx_r4.user.role === \"admin\" ? \"/admin/dashboard\" : \"/home\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.profileTabs);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.activeTab === \"personal\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.activeTab === \"professional\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.activeTab === \"account\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.activeTab === \"completion\");\n  }\n}\nfunction ProfileComponent_div_28_p_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 175);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r50 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r50.getFieldError(\"firstName\"), \" \");\n  }\n}\nfunction ProfileComponent_div_28_p_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 175);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r51 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r51.getFieldError(\"lastName\"), \" \");\n  }\n}\nfunction ProfileComponent_div_28_p_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 175);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r52 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r52.getFieldError(\"fullName\"), \" \");\n  }\n}\nfunction ProfileComponent_div_28_p_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 175);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r53 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r53.getFieldError(\"email\"), \" \");\n  }\n}\nfunction ProfileComponent_div_28_p_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 175);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r54 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r54.getFieldError(\"phoneNumber\"), \" \");\n  }\n}\nfunction ProfileComponent_div_28_p_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 175);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r55 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r55.getFieldError(\"bio\"), \" \");\n  }\n}\nfunction ProfileComponent_div_28_span_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Save Changes\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_28_span_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 176);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 177);\n    i0.ɵɵelement(2, \"circle\", 178)(3, \"path\", 179);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Saving... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_28_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 180)(1, \"p\", 181);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r58 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r58.message);\n  }\n}\nfunction ProfileComponent_div_28_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 182)(1, \"p\", 183);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r59 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r59.error);\n  }\n}\nfunction ProfileComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r61 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 140)(1, \"div\", 141)(2, \"div\", 142)(3, \"div\", 143)(4, \"h2\", 144);\n    i0.ɵɵtext(5, \"Edit Profile\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 145);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_28_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r60 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r60.cancelEdit());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(7, \"svg\", 146);\n    i0.ɵɵelement(8, \"path\", 147);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(9, \"form\", 148);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfileComponent_div_28_Template_form_ngSubmit_9_listener() {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r62 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r62.onEditSubmit());\n    });\n    i0.ɵɵelementStart(10, \"div\", 91)(11, \"div\")(12, \"label\", 149);\n    i0.ɵɵtext(13, \" First Name * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 150);\n    i0.ɵɵtemplate(15, ProfileComponent_div_28_p_15_Template, 2, 1, \"p\", 151);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\")(17, \"label\", 149);\n    i0.ɵɵtext(18, \" Last Name * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"input\", 152);\n    i0.ɵɵtemplate(20, ProfileComponent_div_28_p_20_Template, 2, 1, \"p\", 151);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\")(22, \"label\", 149);\n    i0.ɵɵtext(23, \" Full Name * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(24, \"input\", 153);\n    i0.ɵɵtemplate(25, ProfileComponent_div_28_p_25_Template, 2, 1, \"p\", 151);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\")(27, \"label\", 149);\n    i0.ɵɵtext(28, \" Email * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(29, \"input\", 154);\n    i0.ɵɵtemplate(30, ProfileComponent_div_28_p_30_Template, 2, 1, \"p\", 151);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\")(32, \"label\", 149);\n    i0.ɵɵtext(33, \" Date of Birth \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(34, \"input\", 155);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\")(36, \"label\", 149);\n    i0.ɵɵtext(37, \" Phone Number \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(38, \"input\", 156);\n    i0.ɵɵtemplate(39, ProfileComponent_div_28_p_39_Template, 2, 1, \"p\", 151);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\")(41, \"label\", 149);\n    i0.ɵɵtext(42, \" Department \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(43, \"input\", 157);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\")(45, \"label\", 149);\n    i0.ɵɵtext(46, \" Position \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(47, \"input\", 158);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 159)(49, \"label\", 149);\n    i0.ɵɵtext(50, \" Bio \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(51, \"textarea\", 160);\n    i0.ɵɵtemplate(52, ProfileComponent_div_28_p_52_Template, 2, 1, \"p\", 151);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"div\", 159)(54, \"label\", 149);\n    i0.ɵɵtext(55, \" Address \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(56, \"input\", 161);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"div\", 159)(58, \"label\", 149);\n    i0.ɵɵtext(59, \" Skills \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(60, \"input\", 162);\n    i0.ɵɵelementStart(61, \"p\", 106);\n    i0.ɵɵtext(62, \" Separate skills with commas \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"div\", 159)(64, \"label\", 149);\n    i0.ɵɵtext(65, \" Profile Picture \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"div\", 163)(67, \"div\", 164);\n    i0.ɵɵelement(68, \"img\", 165);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"div\", 166)(70, \"input\", 167);\n    i0.ɵɵlistener(\"change\", function ProfileComponent_div_28_Template_input_change_70_listener($event) {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r63 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r63.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(71, \"p\", 106);\n    i0.ɵɵtext(72, \" Upload a new profile picture (optional) \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(73, \"div\", 168)(74, \"button\", 169);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_28_Template_button_click_74_listener() {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r64 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r64.cancelEdit());\n    });\n    i0.ɵɵtext(75, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"button\", 170);\n    i0.ɵɵtemplate(77, ProfileComponent_div_28_span_77_Template, 2, 0, \"span\", 171);\n    i0.ɵɵtemplate(78, ProfileComponent_div_28_span_78_Template, 5, 0, \"span\", 172);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(79, ProfileComponent_div_28_div_79_Template, 3, 1, \"div\", 173);\n    i0.ɵɵtemplate(80, ProfileComponent_div_28_div_80_Template, 3, 1, \"div\", 174);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"formGroup\", ctx_r5.editForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r5.isFieldInvalid(\"firstName\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getFieldError(\"firstName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r5.isFieldInvalid(\"lastName\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getFieldError(\"lastName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r5.isFieldInvalid(\"fullName\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getFieldError(\"fullName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r5.isFieldInvalid(\"email\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getFieldError(\"email\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r5.isFieldInvalid(\"phoneNumber\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getFieldError(\"phoneNumber\"));\n    i0.ɵɵadvance(12);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r5.isFieldInvalid(\"bio\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getFieldError(\"bio\"));\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"src\", ctx_r5.previewUrl || ctx_r5.getProfileImageUrl(), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r5.editLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.editLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.editLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.message);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.error);\n  }\n}\nexport class ProfileComponent {\n  constructor(authService, authuserService, dataService, router, fb) {\n    this.authService = authService;\n    this.authuserService = authuserService;\n    this.dataService = dataService;\n    this.router = router;\n    this.fb = fb;\n    this.selectedImage = null;\n    this.previewUrl = null;\n    this.message = '';\n    this.error = '';\n    this.uploadLoading = false;\n    this.removeLoading = false;\n    // Edit profile functionality\n    this.isEditMode = false;\n    this.editLoading = false;\n    this.progressPercentage = 0;\n    // Tab navigation\n    this.activeTab = 'personal';\n    this.editSection = '';\n    this.profileTabs = [{\n      id: 'personal',\n      label: 'Personal Info',\n      icon: 'fas fa-user'\n    }, {\n      id: 'professional',\n      label: 'Professional',\n      icon: 'fas fa-briefcase'\n    }, {\n      id: 'account',\n      label: 'Account',\n      icon: 'fas fa-cog'\n    }, {\n      id: 'completion',\n      label: 'Completion',\n      icon: 'fas fa-chart-line'\n    }];\n    this.editForm = this.fb.group({\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\n      fullName: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      dateOfBirth: [''],\n      phoneNumber: ['', [Validators.pattern(/^[0-9+\\-\\s()]+$/)]],\n      department: [''],\n      position: [''],\n      bio: ['', [Validators.minLength(10)]],\n      address: [''],\n      skills: ['']\n    });\n  }\n  ngOnInit() {\n    // Load user profile using DataService\n    this.dataService.getProfile().subscribe({\n      next: res => {\n        this.user = res;\n        // Ensure image properties are consistent\n        if (!this.user.profileImage && this.user.image) {\n          this.user.profileImage = this.user.image;\n        } else if (!this.user.image && this.user.profileImage) {\n          this.user.image = this.user.profileImage;\n        }\n        // If no image is available, use default\n        if (!this.user.profileImage || this.user.profileImage === 'null' || this.user.profileImage.trim() === '') {\n          this.user.profileImage = 'assets/images/default-profile.png';\n          this.user.image = 'assets/images/default-profile.png';\n        }\n        // Ensure profileImageURL is also set for backward compatibility\n        if (!this.user.profileImageURL) {\n          this.user.profileImageURL = this.user.profileImage || this.user.image;\n        }\n        // Calculate profile completion percentage\n        this.calculateProfileCompletion();\n        // Populate edit form with current user data\n        this.populateEditForm();\n      },\n      error: () => {\n        this.error = 'Failed to load profile.';\n      }\n    });\n  }\n  calculateProfileCompletion() {\n    if (!this.user) return;\n    const requiredFields = ['firstName', 'lastName', 'fullName', 'email', 'dateOfBirth', 'phoneNumber', 'department', 'bio'];\n    const optionalFields = ['position', 'address', 'skills'];\n    let completedRequired = 0;\n    let completedOptional = 0;\n    // Check required fields\n    requiredFields.forEach(field => {\n      const value = this.user[field];\n      if (value && value.toString().trim() !== '' && value !== 'uploads/default.png') {\n        completedRequired++;\n      }\n    });\n    // Check optional fields\n    optionalFields.forEach(field => {\n      const value = this.user[field];\n      if (value && value.toString().trim() !== '') {\n        completedOptional++;\n      }\n    });\n    // Check profile image\n    let hasProfileImage = 0;\n    if (this.user.profileImage && this.user.profileImage !== 'uploads/default.png' && this.user.profileImage !== 'assets/images/default-profile.png' && this.user.profileImage.trim() !== '') {\n      hasProfileImage = 1;\n    }\n    // Calculate percentage: Required fields (60%) + Optional fields (30%) + Profile Image (10%)\n    const requiredPercentage = completedRequired / requiredFields.length * 60;\n    const optionalPercentage = completedOptional / optionalFields.length * 30;\n    const imagePercentage = hasProfileImage * 10;\n    this.progressPercentage = Math.round(requiredPercentage + optionalPercentage + imagePercentage);\n  }\n  populateEditForm() {\n    if (!this.user) return;\n    this.editForm.patchValue({\n      firstName: this.user.firstName || '',\n      lastName: this.user.lastName || '',\n      fullName: this.user.fullName || '',\n      email: this.user.email || '',\n      dateOfBirth: this.user.dateOfBirth || '',\n      phoneNumber: this.user.phoneNumber || '',\n      department: this.user.department || '',\n      position: this.user.position || '',\n      bio: this.user.bio || '',\n      address: this.user.address || '',\n      skills: Array.isArray(this.user.skills) ? this.user.skills.join(', ') : this.user.skills || ''\n    });\n  }\n  /**\n   * Returns the appropriate profile image URL based on available properties\n   * Uses the same logic as in front-layout component for consistency\n   */\n  getProfileImageUrl() {\n    if (!this.user) return 'assets/images/default-profile.png';\n    // Check profileImage first\n    if (this.user.profileImage && this.user.profileImage !== 'null' && this.user.profileImage.trim() !== '') {\n      return this.user.profileImage;\n    }\n    // Then check image\n    if (this.user.image && this.user.image !== 'null' && this.user.image.trim() !== '') {\n      return this.user.image;\n    }\n    // Then check profileImageURL (for backward compatibility)\n    if (this.user.profileImageURL && this.user.profileImageURL !== 'null' && this.user.profileImageURL.trim() !== '') {\n      return this.user.profileImageURL;\n    }\n    // Default fallback\n    return 'assets/images/default-profile.png';\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files?.length) {\n      const file = input.files[0];\n      const validTypes = ['image/jpeg', 'image/png', 'image/webp'];\n      if (!validTypes.includes(file.type)) {\n        this.error = 'Seuls les JPEG, PNG et WebP sont autorisés';\n        this.resetFileInput();\n        return;\n      }\n      if (file.size > 2 * 1024 * 1024) {\n        this.error = \"L'image ne doit pas dépasser 2MB\";\n        this.resetFileInput();\n        return;\n      }\n      this.selectedImage = file;\n      this.error = '';\n      const reader = new FileReader();\n      reader.onload = e => {\n        this.previewUrl = e.target?.result || null;\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n  onUpload() {\n    if (!this.selectedImage) return;\n    this.uploadLoading = true; // Activer l'état de chargement\n    this.message = '';\n    this.error = '';\n    console.log('Upload started, uploadLoading:', this.uploadLoading);\n    this.dataService.uploadProfileImage(this.selectedImage).pipe(finalize(() => {\n      this.uploadLoading = false;\n      console.log('Upload finished, uploadLoading:', this.uploadLoading);\n    })).subscribe({\n      next: response => {\n        this.message = response.message || 'Profile updated successfully';\n        // Update all image properties to ensure consistency across the application\n        this.user.profileImageURL = response.imageUrl;\n        this.user.profileImage = response.imageUrl;\n        this.user.image = response.imageUrl;\n        // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\n        this.dataService.updateCurrentUser({\n          profileImage: response.imageUrl,\n          image: response.imageUrl\n        });\n        // Also update in AuthUserService to ensure all components are updated\n        this.authuserService.setCurrentUser({\n          ...this.user,\n          profileImage: response.imageUrl,\n          image: response.imageUrl\n        });\n        this.selectedImage = null;\n        this.previewUrl = null;\n        this.resetFileInput();\n        if (response.token) {\n          localStorage.setItem('token', response.token);\n        }\n        // Auto-hide message after 3 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 3000);\n      },\n      error: err => {\n        this.error = err.error?.message || 'Upload failed';\n        // Auto-hide error after 3 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 3000);\n      }\n    });\n  }\n  removeProfileImage() {\n    if (!confirm('Are you sure you want to remove your profile picture?')) return;\n    this.removeLoading = true;\n    this.message = '';\n    this.error = '';\n    this.dataService.removeProfileImage().pipe(finalize(() => this.removeLoading = false)).subscribe({\n      next: response => {\n        this.message = response.message || 'Profile picture removed successfully';\n        // Update all image properties to ensure consistency across the application\n        this.user.profileImageURL = null;\n        this.user.profileImage = null;\n        this.user.image = null;\n        // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\n        this.dataService.updateCurrentUser({\n          profileImage: 'assets/images/default-profile.png',\n          image: 'assets/images/default-profile.png'\n        });\n        // Also update in AuthUserService to ensure all components are updated\n        this.authuserService.setCurrentUser({\n          ...this.user,\n          profileImage: 'assets/images/default-profile.png',\n          image: 'assets/images/default-profile.png'\n        });\n        if (response.token) {\n          localStorage.setItem('token', response.token);\n        }\n        // Auto-hide message after 3 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 3000);\n      },\n      error: err => {\n        this.error = err.error?.message || 'Removal failed';\n        // Auto-hide error after 3 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 3000);\n      }\n    });\n  }\n  resetFileInput() {\n    this.selectedImage = null;\n    this.previewUrl = null;\n    const fileInput = document.getElementById('profile-upload');\n    if (fileInput) fileInput.value = '';\n  }\n  navigateTo(path) {\n    this.router.navigate([path]);\n  }\n  // Edit profile methods\n  toggleEditMode() {\n    this.isEditMode = !this.isEditMode;\n    if (this.isEditMode) {\n      this.populateEditForm();\n    }\n    this.message = '';\n    this.error = '';\n  }\n  onEditSubmit() {\n    if (this.editForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n    this.editLoading = true;\n    this.error = '';\n    this.message = '';\n    const formData = new FormData();\n    // Add form fields\n    Object.keys(this.editForm.value).forEach(key => {\n      const value = this.editForm.value[key];\n      if (key === 'skills' && value) {\n        // Convert skills string to array\n        const skillsArray = value.split(',').map(skill => skill.trim()).filter(skill => skill);\n        formData.append(key, JSON.stringify(skillsArray));\n      } else if (value) {\n        formData.append(key, value);\n      }\n    });\n    // Add profile image if selected\n    if (this.selectedImage) {\n      formData.append('image', this.selectedImage);\n    }\n    this.dataService.completeProfile(formData).subscribe({\n      next: response => {\n        this.editLoading = false;\n        this.message = 'Profile updated successfully!';\n        // Update current user data\n        this.user = {\n          ...this.user,\n          ...response.user\n        };\n        this.authuserService.setCurrentUser(this.user);\n        // Recalculate progress\n        this.calculateProfileCompletion();\n        // Exit edit mode\n        this.isEditMode = false;\n        // Clear selected image\n        this.selectedImage = null;\n        this.previewUrl = null;\n        this.resetFileInput();\n        // Auto-hide message after 3 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 3000);\n      },\n      error: err => {\n        this.editLoading = false;\n        this.error = err.error?.message || 'An error occurred while updating your profile.';\n        // Auto-hide error after 5 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 5000);\n      }\n    });\n  }\n  cancelEdit() {\n    this.isEditMode = false;\n    this.populateEditForm(); // Reset form to original values\n    this.selectedImage = null;\n    this.previewUrl = null;\n    this.resetFileInput();\n    this.message = '';\n    this.error = '';\n  }\n  markFormGroupTouched() {\n    Object.keys(this.editForm.controls).forEach(key => {\n      this.editForm.get(key)?.markAsTouched();\n    });\n  }\n  // Helper methods for template\n  getFieldError(fieldName) {\n    const field = this.editForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['minlength']) return `${fieldName} is too short`;\n      if (field.errors['email']) return `Invalid email format`;\n      if (field.errors['pattern']) return `${fieldName} format is invalid`;\n    }\n    return '';\n  }\n  isFieldInvalid(fieldName) {\n    const field = this.editForm.get(fieldName);\n    return !!(field?.invalid && field.touched);\n  }\n  getProgressColor() {\n    if (this.progressPercentage < 25) return '#ef4444'; // red\n    if (this.progressPercentage < 50) return '#f97316'; // orange\n    if (this.progressPercentage < 75) return '#eab308'; // yellow\n    if (this.progressPercentage < 100) return '#22c55e'; // green\n    return '#10b981'; // emerald\n  }\n\n  getMotivationalMessage() {\n    if (this.progressPercentage < 25) {\n      return \"Let's complete your profile to unlock all features! 🚀\";\n    } else if (this.progressPercentage < 50) {\n      return \"You're making great progress! Keep going! 💪\";\n    } else if (this.progressPercentage < 75) {\n      return \"Excellent! You're more than halfway there! 🌟\";\n    } else if (this.progressPercentage < 100) {\n      return \"Almost perfect! Just a few more details! 🎯\";\n    } else {\n      return \"Perfect! Your profile is complete! ✨\";\n    }\n  }\n  logout() {\n    this.authuserService.logout().subscribe({\n      next: () => {\n        this.authuserService.clearAuthData();\n        setTimeout(() => {\n          this.router.navigate(['/login'], {\n            queryParams: {\n              message: 'Déconnexion réussie'\n            },\n            replaceUrl: true\n          });\n        }, 100);\n      },\n      error: err => {\n        console.error('Logout error:', err);\n        this.authuserService.clearAuthData();\n        setTimeout(() => {\n          this.router.navigate(['/login'], {});\n        }, 100);\n      }\n    });\n  }\n  // New methods for enhanced profile functionality\n  setEditSection(section) {\n    this.editSection = section;\n    this.isEditMode = true;\n    this.populateEditForm();\n  }\n  getRequiredFieldsCompletion() {\n    if (!this.user) return 0;\n    const requiredFields = ['firstName', 'lastName', 'fullName', 'email', 'dateOfBirth', 'phoneNumber', 'department', 'bio'];\n    let completed = 0;\n    requiredFields.forEach(field => {\n      const value = this.user[field];\n      if (value && value.toString().trim() !== '' && value !== 'uploads/default.png') {\n        completed++;\n      }\n    });\n    return Math.round(completed / requiredFields.length * 100);\n  }\n  getOptionalFieldsCompletion() {\n    if (!this.user) return 0;\n    const optionalFields = ['position', 'address', 'skills'];\n    let completed = 0;\n    optionalFields.forEach(field => {\n      const value = this.user[field];\n      if (field === 'skills') {\n        // Special handling for skills array\n        if (Array.isArray(value) && value.length > 0) {\n          completed++;\n        }\n      } else if (value && value.toString().trim() !== '') {\n        completed++;\n      }\n    });\n    return Math.round(completed / optionalFields.length * 100);\n  }\n  hasProfileImage() {\n    if (!this.user) return false;\n    return !!(this.user.profileImage && this.user.profileImage !== 'uploads/default.png' && this.user.profileImage !== 'assets/images/default-profile.png' && this.user.profileImage.trim() !== '');\n  }\n  getMissingFields() {\n    if (!this.user) return [];\n    const fields = [{\n      field: 'firstName',\n      label: 'First Name',\n      required: true\n    }, {\n      field: 'lastName',\n      label: 'Last Name',\n      required: true\n    }, {\n      field: 'fullName',\n      label: 'Full Name',\n      required: true\n    }, {\n      field: 'dateOfBirth',\n      label: 'Date of Birth',\n      required: true\n    }, {\n      field: 'phoneNumber',\n      label: 'Phone Number',\n      required: true\n    }, {\n      field: 'department',\n      label: 'Department',\n      required: true\n    }, {\n      field: 'bio',\n      label: 'Bio',\n      required: true\n    }, {\n      field: 'position',\n      label: 'Position',\n      required: false\n    }, {\n      field: 'address',\n      label: 'Address',\n      required: false\n    }, {\n      field: 'skills',\n      label: 'Skills',\n      required: false\n    }];\n    return fields.filter(field => {\n      const value = this.user[field.field];\n      return !value || value.toString().trim() === '';\n    });\n  }\n  static {\n    this.ɵfac = function ProfileComponent_Factory(t) {\n      return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.AuthuserService), i0.ɵɵdirectiveInject(i3.DataService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileComponent,\n      selectors: [[\"app-profile\"]],\n      decls: 29,\n      vars: 6,\n      consts: [[1, \"container-fluid\", \"p-4\", \"md:p-6\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"min-h-screen\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-6\", \"relative\", \"z-10\"], [1, \"mb-8\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-1\"], [\"class\", \"flex justify-center items-center py-20\", 4, \"ngIf\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-3xl my-4 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-4 mx-auto max-w-3xl my-4 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"mb-8\", 4, \"ngIf\"], [\"class\", \"space-y-6\", 4, \"ngIf\"], [\"class\", \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"items-center\", \"py-20\"], [1, \"relative\"], [1, \"w-14\", \"h-14\", \"border-4\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#6d78c9]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"rounded-lg\", \"p-4\", \"mx-auto\", \"max-w-3xl\", \"my-4\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-3\", \"text-xl\", \"relative\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"font-medium\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/5\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]/30\", \"rounded-lg\", \"p-4\", \"mx-auto\", \"max-w-3xl\", \"my-4\", \"backdrop-blur-sm\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-3\", \"text-xl\", \"relative\"], [1, \"fas\", \"fa-check-circle\"], [1, \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-1\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"p-6\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-4\"], [1, \"text-lg\", \"font-semibold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-2xl\", \"font-bold\"], [1, \"w-full\", \"bg-[#e2e8f0]\", \"dark:bg-[#2a2a2a]\", \"rounded-full\", \"h-4\", \"overflow-hidden\", \"mb-3\"], [1, \"h-full\", \"rounded-full\", \"transition-all\", \"duration-500\", \"ease-out\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [1, \"mt-4\"], [1, \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"rounded-lg\", \"hover:from-[#3d4a85]\", \"hover:to-[#6a1b9a]\", \"transition-all\", \"text-sm\", \"font-medium\", 3, \"click\"], [1, \"space-y-6\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"p-6\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\", \"overflow-hidden\", \"group\"], [1, \"flex\", \"flex-col\", \"md:flex-row\", \"items-center\", \"md:items-start\", \"gap-6\"], [1, \"flex-shrink-0\", \"text-center\"], [1, \"relative\", \"group/avatar\"], [\"alt\", \"Profile Image\", 1, \"w-32\", \"h-32\", \"rounded-full\", \"object-cover\", \"border-4\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", \"shadow-lg\", \"group-hover/avatar:scale-105\", \"transition-transform\", \"duration-300\", 3, \"src\"], [1, \"absolute\", \"inset-0\", \"bg-black\", \"bg-opacity-50\", \"rounded-full\", \"opacity-0\", \"group-hover/avatar:opacity-100\", \"transition-opacity\", \"duration-300\", \"flex\", \"items-center\", \"justify-center\"], [\"for\", \"profile-upload\", 1, \"cursor-pointer\", \"text-white\", \"text-sm\", \"font-medium\"], [1, \"fas\", \"fa-camera\", \"text-xl\", \"mb-1\", \"block\"], [\"type\", \"file\", \"id\", \"profile-upload\", \"accept\", \"image/*\", 1, \"hidden\", 3, \"change\"], [1, \"flex\", \"gap-2\", \"mt-4\"], [\"class\", \"px-3 py-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white text-xs rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all disabled:opacity-50\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"px-3 py-1 bg-red-500 text-white text-xs rounded-lg hover:bg-red-600 transition-all disabled:opacity-50\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"flex-1\", \"text-center\", \"md:text-left\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"mb-2\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-2\"], [1, \"flex\", \"flex-wrap\", \"gap-2\", \"justify-center\", \"md:justify-start\", \"mb-4\"], [1, \"px-3\", \"py-1\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"text-sm\", \"rounded-full\"], [\"class\", \"px-3 py-1 bg-[#3d4a85]/10 dark:bg-[#4f5fad]/10 text-[#3d4a85] dark:text-[#4f5fad] text-sm rounded-full\", 4, \"ngIf\"], [\"class\", \"px-3 py-1 bg-[#7826b5]/10 dark:bg-[#6d78c9]/10 text-[#7826b5] dark:text-[#6d78c9] text-sm rounded-full\", 4, \"ngIf\"], [\"class\", \"text-[#6d6870] dark:text-[#a0a0a0] text-sm leading-relaxed mb-4\", 4, \"ngIf\"], [1, \"flex\", \"flex-wrap\", \"gap-2\", \"justify-center\", \"md:justify-start\"], [1, \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"text-sm\", \"rounded-lg\", \"hover:from-[#3d4a85]\", \"hover:to-[#6a1b9a]\", \"transition-all\", \"flex\", \"items-center\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-2\"], [1, \"px-4\", \"py-2\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"text-sm\", \"rounded-lg\", \"hover:bg-[#4f5fad]\", \"hover:text-white\", \"dark:hover:bg-[#6d78c9]\", \"dark:hover:text-white\", \"transition-all\", \"flex\", \"items-center\", 3, \"routerLink\"], [1, \"fas\", \"fa-home\", \"mr-2\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"overflow-hidden\"], [1, \"flex\", \"flex-wrap\", \"border-b\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\"], [\"class\", \"flex-1 px-4 py-3 text-sm font-medium transition-all hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 flex items-center justify-center gap-2\", 3, \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-6\"], [1, \"px-3\", \"py-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"text-xs\", \"rounded-lg\", \"hover:from-[#3d4a85]\", \"hover:to-[#6a1b9a]\", \"transition-all\", \"disabled:opacity-50\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-upload mr-1\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin mr-1\", 4, \"ngIf\"], [1, \"fas\", \"fa-upload\", \"mr-1\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-1\"], [1, \"px-3\", \"py-1\", \"bg-red-500\", \"text-white\", \"text-xs\", \"rounded-lg\", \"hover:bg-red-600\", \"transition-all\", \"disabled:opacity-50\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-trash mr-1\", 4, \"ngIf\"], [1, \"fas\", \"fa-trash\", \"mr-1\"], [1, \"px-3\", \"py-1\", \"bg-[#3d4a85]/10\", \"dark:bg-[#4f5fad]/10\", \"text-[#3d4a85]\", \"dark:text-[#4f5fad]\", \"text-sm\", \"rounded-full\"], [1, \"px-3\", \"py-1\", \"bg-[#7826b5]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#7826b5]\", \"dark:text-[#6d78c9]\", \"text-sm\", \"rounded-full\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-sm\", \"leading-relaxed\", \"mb-4\"], [1, \"flex-1\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"transition-all\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"flex\", \"items-center\", \"justify-center\", \"gap-2\", 3, \"click\"], [\"class\", \"px-2 py-1 bg-orange-500 text-white text-xs rounded-full\", 4, \"ngIf\"], [1, \"px-2\", \"py-1\", \"bg-orange-500\", \"text-white\", \"text-xs\", \"rounded-full\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-6\"], [1, \"text-xl\", \"font-semibold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-user\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"px-3\", \"py-1\", \"text-sm\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"rounded-lg\", \"hover:bg-[#4f5fad]/20\", \"dark:hover:bg-[#6d78c9]/20\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [1, \"bg-[#f8fafc]\", \"dark:bg-[#2a2a2a]\", \"p-4\", \"rounded-lg\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-1\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"font-medium\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-2\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"leading-relaxed\"], [1, \"fas\", \"fa-briefcase\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-3\"], [\"class\", \"flex flex-wrap gap-2\", 4, \"ngIf\", \"ngIfElse\"], [\"noSkills\", \"\"], [1, \"flex\", \"flex-wrap\", \"gap-2\"], [\"class\", \"px-3 py-1 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] text-sm rounded-full border border-[#4f5fad]/20 dark:border-[#6d78c9]/20\", 4, \"ngFor\", \"ngForOf\"], [1, \"px-3\", \"py-1\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"text-sm\", \"rounded-full\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"italic\"], [1, \"fas\", \"fa-cog\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-1\"], [1, \"flex\", \"items-center\", \"gap-2\"], [1, \"px-2\", \"py-1\", \"bg-green-100\", \"dark:bg-green-900/20\", \"text-green-800\", \"dark:text-green-200\", \"text-xs\", \"rounded-full\"], [1, \"bg-[#f8fafc]\", \"dark:bg-[#2a2a2a]\", \"p-6\", \"rounded-lg\"], [1, \"text-lg\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-4\"], [1, \"flex\", \"flex-wrap\", \"gap-3\"], [\"routerLink\", \"/change-password\", 1, \"px-4\", \"py-2\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"rounded-lg\", \"hover:bg-[#4f5fad]/20\", \"dark:hover:bg-[#6d78c9]/20\", \"transition-all\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-key\", \"mr-2\"], [1, \"px-4\", \"py-2\", \"bg-red-100\", \"dark:bg-red-900/20\", \"text-red-600\", \"dark:text-red-400\", \"rounded-lg\", \"hover:bg-red-200\", \"dark:hover:bg-red-900/40\", \"transition-all\", \"flex\", \"items-center\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\", \"mr-2\"], [1, \"px-4\", \"py-2\", \"bg-[#6d6870]/10\", \"dark:bg-[#a0a0a0]/10\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"rounded-lg\", \"hover:bg-[#6d6870]/20\", \"dark:hover:bg-[#a0a0a0]/20\", \"transition-all\", \"flex\", \"items-center\", 3, \"routerLink\"], [1, \"fas\", \"fa-chart-line\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"text-lg\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"w-full\", \"bg-[#e2e8f0]\", \"dark:bg-[#2a2a2a]\", \"rounded-full\", \"h-4\", \"overflow-hidden\", \"mb-4\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-4\", \"mt-6\"], [1, \"text-center\", \"p-4\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-lg\"], [1, \"text-2xl\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"text-2xl\", \"font-bold\", \"text-[#7826b5]\", \"dark:text-[#6d78c9]\"], [1, \"text-2xl\", \"font-bold\", \"text-[#3d4a85]\", \"dark:text-[#4f5fad]\"], [\"class\", \"bg-orange-50 dark:bg-orange-900/20 p-6 rounded-lg border border-orange-200 dark:border-orange-800\", 4, \"ngIf\"], [1, \"bg-blue-50\", \"dark:bg-blue-900/20\", \"p-6\", \"rounded-lg\", \"border\", \"border-blue-200\", \"dark:border-blue-800\"], [1, \"text-lg\", \"font-medium\", \"text-blue-800\", \"dark:text-blue-200\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-lightbulb\", \"mr-2\"], [1, \"space-y-2\", \"text-blue-700\", \"dark:text-blue-300\"], [1, \"fas\", \"fa-check-circle\", \"mr-2\", \"mt-1\", \"text-green-500\"], [1, \"bg-orange-50\", \"dark:bg-orange-900/20\", \"p-6\", \"rounded-lg\", \"border\", \"border-orange-200\", \"dark:border-orange-800\"], [1, \"text-lg\", \"font-medium\", \"text-orange-800\", \"dark:text-orange-200\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-2\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-3\"], [\"class\", \"flex items-center justify-between p-3 bg-white dark:bg-[#1e1e1e] rounded-lg\", 4, \"ngFor\", \"ngForOf\"], [1, \"mt-4\", \"px-4\", \"py-2\", \"bg-orange-500\", \"text-white\", \"rounded-lg\", \"hover:bg-orange-600\", \"transition-all\", 3, \"click\"], [1, \"flex\", \"items-center\", \"justify-between\", \"p-3\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-lg\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"px-2\", \"py-1\", \"bg-orange-100\", \"dark:bg-orange-900/40\", \"text-orange-800\", \"dark:text-orange-200\", \"text-xs\", \"rounded-full\"], [1, \"fixed\", \"inset-0\", \"bg-black\", \"bg-opacity-50\", \"flex\", \"items-center\", \"justify-center\", \"z-50\", \"p-4\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-2xl\", \"shadow-2xl\", \"max-w-4xl\", \"w-full\", \"max-h-[90vh]\", \"overflow-y-auto\"], [1, \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"p-6\", \"rounded-t-2xl\"], [1, \"flex\", \"justify-between\", \"items-center\"], [1, \"text-2xl\", \"font-bold\", \"text-white\"], [1, \"text-white\", \"hover:text-gray-200\", \"transition-colors\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"p-6\", 3, \"formGroup\", \"ngSubmit\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [\"type\", \"text\", \"formControlName\", \"firstName\", \"placeholder\", \"Enter your first name\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"class\", \"text-red-500 text-sm mt-1\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"lastName\", \"placeholder\", \"Enter your last name\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"text\", \"formControlName\", \"fullName\", \"placeholder\", \"Enter your full name\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"date\", \"formControlName\", \"dateOfBirth\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"tel\", \"formControlName\", \"phoneNumber\", \"placeholder\", \"Enter your phone number\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"text\", \"formControlName\", \"department\", \"placeholder\", \"e.g., Computer Science, Marketing\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"text\", \"formControlName\", \"position\", \"placeholder\", \"e.g., Student, Professor, Developer\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [1, \"mt-6\"], [\"formControlName\", \"bio\", \"rows\", \"4\", \"placeholder\", \"Tell us about yourself, your interests, and goals...\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", \"resize-none\"], [\"type\", \"text\", \"formControlName\", \"address\", \"placeholder\", \"Enter your address\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"text\", \"formControlName\", \"skills\", \"placeholder\", \"e.g., JavaScript, Python, Project Management (comma separated)\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"flex-shrink-0\"], [\"alt\", \"Profile preview\", 1, \"w-20\", \"h-20\", \"rounded-full\", \"object-cover\", \"border-2\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", 3, \"src\"], [1, \"flex-1\"], [\"type\", \"file\", \"accept\", \"image/*\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"change\"], [1, \"flex\", \"justify-end\", \"space-x-4\", \"mt-8\", \"pt-6\", \"border-t\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\"], [\"type\", \"button\", 1, \"px-6\", \"py-3\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"border\", \"border-[#6d6870]\", \"dark:border-[#a0a0a0]\", \"rounded-lg\", \"hover:bg-[#6d6870]\", \"hover:text-white\", \"dark:hover:bg-[#a0a0a0]\", \"dark:hover:text-black\", \"transition-all\", 3, \"click\"], [\"type\", \"submit\", 1, \"px-8\", \"py-3\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"rounded-lg\", \"hover:from-[#3d4a85]\", \"hover:to-[#6a1b9a]\", \"transition-all\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", 3, \"disabled\"], [4, \"ngIf\"], [\"class\", \"flex items-center\", 4, \"ngIf\"], [\"class\", \"mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\", 4, \"ngIf\"], [\"class\", \"mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\", 4, \"ngIf\"], [1, \"text-red-500\", \"text-sm\", \"mt-1\"], [1, \"flex\", \"items-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"animate-spin\", \"-ml-1\", \"mr-3\", \"h-5\", \"w-5\", \"text-white\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", 1, \"opacity-25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", 1, \"opacity-75\"], [1, \"mt-4\", \"p-4\", \"bg-green-50\", \"dark:bg-green-900/20\", \"border\", \"border-green-200\", \"dark:border-green-800\", \"rounded-lg\"], [1, \"text-green-800\", \"dark:text-green-200\"], [1, \"mt-4\", \"p-4\", \"bg-red-50\", \"dark:bg-red-900/20\", \"border\", \"border-red-200\", \"dark:border-red-800\", \"rounded-lg\"], [1, \"text-red-800\", \"dark:text-red-200\"]],\n      template: function ProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8)(19, \"h1\", 9);\n          i0.ɵɵtext(20, \" Mon Profil \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"p\", 10);\n          i0.ɵɵtext(22, \" G\\u00E9rez vos informations personnelles et vos pr\\u00E9f\\u00E9rences \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(23, ProfileComponent_div_23_Template, 4, 0, \"div\", 11);\n          i0.ɵɵtemplate(24, ProfileComponent_div_24_Template, 10, 1, \"div\", 12);\n          i0.ɵɵtemplate(25, ProfileComponent_div_25_Template, 10, 1, \"div\", 13);\n          i0.ɵɵtemplate(26, ProfileComponent_div_26_Template, 13, 9, \"div\", 14);\n          i0.ɵɵtemplate(27, ProfileComponent_div_27_Template, 42, 17, \"div\", 15);\n          i0.ɵɵtemplate(28, ProfileComponent_div_28_Template, 81, 25, \"div\", 16);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(23);\n          i0.ɵɵproperty(\"ngIf\", !ctx.user);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.message);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.user);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.user);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditMode);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i4.RouterLink, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.FormGroupDirective, i5.FormControlName, i6.SlicePipe, i6.TitleCasePipe, i6.DatePipe],\n      styles: [\"\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(255, 255, 255, 0.8);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  border: 4px solid rgba(0, 0, 0, 0.1);\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  border-left-color: #09f;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.form-loading[_ngcontent-%COMP%] {\\n  min-height: 300px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInByb2ZpbGUuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSw4QkFBOEI7QUFDOUI7RUFDRSxlQUFlO0VBQ2YsTUFBTTtFQUNOLE9BQU87RUFDUCxRQUFRO0VBQ1IsU0FBUztFQUNULG9DQUFvQztFQUNwQyxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLG1CQUFtQjtFQUNuQix1QkFBdUI7RUFDdkIsYUFBYTtBQUNmOztBQUVBO0VBQ0Usb0NBQW9DO0VBQ3BDLFdBQVc7RUFDWCxZQUFZO0VBQ1osa0JBQWtCO0VBQ2xCLHVCQUF1QjtFQUN2QixrQ0FBa0M7QUFDcEM7O0FBRUE7RUFDRTtJQUNFLHVCQUF1QjtFQUN6QjtFQUNBO0lBQ0UseUJBQXlCO0VBQzNCO0FBQ0Y7QUFDQTtFQUNFLGlCQUFpQjtFQUNqQixhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLHVCQUF1QjtBQUN6QiIsImZpbGUiOiJwcm9maWxlLmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBBZGQgdG8geW91ciBjb21wb25lbnQgQ1NTICovXHJcbi5sb2FkaW5nLW92ZXJsYXkge1xyXG4gIHBvc2l0aW9uOiBmaXhlZDtcclxuICB0b3A6IDA7XHJcbiAgbGVmdDogMDtcclxuICByaWdodDogMDtcclxuICBib3R0b206IDA7XHJcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIHotaW5kZXg6IDEwMDA7XHJcbn1cclxuXHJcbi5zcGlubmVyIHtcclxuICBib3JkZXI6IDRweCBzb2xpZCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgd2lkdGg6IDM2cHg7XHJcbiAgaGVpZ2h0OiAzNnB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICBib3JkZXItbGVmdC1jb2xvcjogIzA5ZjtcclxuICBhbmltYXRpb246IHNwaW4gMXMgbGluZWFyIGluZmluaXRlO1xyXG59XHJcblxyXG5Aa2V5ZnJhbWVzIHNwaW4ge1xyXG4gIDAlIHtcclxuICAgIHRyYW5zZm9ybTogcm90YXRlKDBkZWcpO1xyXG4gIH1cclxuICAxMDAlIHtcclxuICAgIHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7XHJcbiAgfVxyXG59XHJcbi5mb3JtLWxvYWRpbmcge1xyXG4gIG1pbi1oZWlnaHQ6IDMwMHB4O1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxufVxyXG4iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcHJvZmlsZS9wcm9maWxlLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsOEJBQThCO0FBQzlCO0VBQ0UsZUFBZTtFQUNmLE1BQU07RUFDTixPQUFPO0VBQ1AsUUFBUTtFQUNSLFNBQVM7RUFDVCxvQ0FBb0M7RUFDcEMsYUFBYTtFQUNiLHNCQUFzQjtFQUN0QixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLGFBQWE7QUFDZjs7QUFFQTtFQUNFLG9DQUFvQztFQUNwQyxXQUFXO0VBQ1gsWUFBWTtFQUNaLGtCQUFrQjtFQUNsQix1QkFBdUI7RUFDdkIsa0NBQWtDO0FBQ3BDOztBQUVBO0VBQ0U7SUFDRSx1QkFBdUI7RUFDekI7RUFDQTtJQUNFLHlCQUF5QjtFQUMzQjtBQUNGO0FBQ0E7RUFDRSxpQkFBaUI7RUFDakIsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQix1QkFBdUI7QUFDekI7O0FBRUEsb3FEQUFvcUQiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBBZGQgdG8geW91ciBjb21wb25lbnQgQ1NTICovXHJcbi5sb2FkaW5nLW92ZXJsYXkge1xyXG4gIHBvc2l0aW9uOiBmaXhlZDtcclxuICB0b3A6IDA7XHJcbiAgbGVmdDogMDtcclxuICByaWdodDogMDtcclxuICBib3R0b206IDA7XHJcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIHotaW5kZXg6IDEwMDA7XHJcbn1cclxuXHJcbi5zcGlubmVyIHtcclxuICBib3JkZXI6IDRweCBzb2xpZCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgd2lkdGg6IDM2cHg7XHJcbiAgaGVpZ2h0OiAzNnB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICBib3JkZXItbGVmdC1jb2xvcjogIzA5ZjtcclxuICBhbmltYXRpb246IHNwaW4gMXMgbGluZWFyIGluZmluaXRlO1xyXG59XHJcblxyXG5Aa2V5ZnJhbWVzIHNwaW4ge1xyXG4gIDAlIHtcclxuICAgIHRyYW5zZm9ybTogcm90YXRlKDBkZWcpO1xyXG4gIH1cclxuICAxMDAlIHtcclxuICAgIHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7XHJcbiAgfVxyXG59XHJcbi5mb3JtLWxvYWRpbmcge1xyXG4gIG1pbi1oZWlnaHQ6IDMwMHB4O1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "finalize", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "ɵɵtextInterpolate1", "ctx_r2", "message", "ɵɵlistener", "ProfileComponent_div_26_div_12_Template_button_click_1_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "toggleEditMode", "ɵɵtemplate", "ProfileComponent_div_26_div_12_Template", "ɵɵstyleProp", "ctx_r3", "getProgressColor", "progressPercentage", "getMotivationalMessage", "ɵɵproperty", "ProfileComponent_div_27_button_13_Template_button_click_0_listener", "_r22", "ctx_r21", "onUpload", "ProfileComponent_div_27_button_13_i_1_Template", "ProfileComponent_div_27_button_13_i_2_Template", "ctx_r9", "uploadLoading", "ProfileComponent_div_27_button_14_Template_button_click_0_listener", "_r26", "ctx_r25", "removeProfileImage", "ProfileComponent_div_27_button_14_i_1_Template", "ProfileComponent_div_27_button_14_i_2_Template", "ctx_r10", "removeLoading", "ctx_r11", "user", "department", "ctx_r12", "position", "ctx_r13", "bio", "length", "ɵɵpipeBind3", "ctx_r28", "ProfileComponent_div_27_button_36_Template_button_click_0_listener", "restoredCtx", "_r30", "tab_r27", "$implicit", "ctx_r29", "activeTab", "id", "ProfileComponent_div_27_button_36_span_3_Template", "ɵɵclassProp", "ctx_r14", "ɵɵclassMap", "icon", "label", "ProfileComponent_div_27_div_38_Template_button_click_5_listener", "_r32", "ctx_r31", "setEditSection", "ctx_r15", "firstName", "lastName", "fullName", "dateOfBirth", "ɵɵpipeBind2", "phoneNumber", "address", "skill_r37", "ProfileComponent_div_27_div_39_div_34_span_1_Template", "ctx_r33", "skills", "ProfileComponent_div_27_div_39_Template_button_click_5_listener", "_r39", "ctx_r38", "ProfileComponent_div_27_div_39_div_34_Template", "ProfileComponent_div_27_div_39_ng_template_35_Template", "ɵɵtemplateRefExtractor", "ctx_r16", "ɵɵpipeBind1", "role", "createdAt", "_r34", "ProfileComponent_div_27_div_40_Template_button_click_29_listener", "_r41", "ctx_r40", "logout", "ctx_r17", "email", "field_r44", "required", "ProfileComponent_div_27_div_41_div_37_div_5_Template", "ProfileComponent_div_27_div_41_div_37_Template_button_click_6_listener", "_r46", "ctx_r45", "ctx_r42", "get<PERSON><PERSON>ing<PERSON>ields", "ProfileComponent_div_27_div_41_div_37_Template", "ctx_r18", "getRequiredFieldsCompletion", "getOptionalFieldsCompletion", "hasProfileImage", "ProfileComponent_div_27_Template_input_change_11_listener", "$event", "_r48", "ctx_r47", "onFileSelected", "ProfileComponent_div_27_button_13_Template", "ProfileComponent_div_27_button_14_Template", "ProfileComponent_div_27_span_24_Template", "ProfileComponent_div_27_span_25_Template", "ProfileComponent_div_27_p_26_Template", "ProfileComponent_div_27_Template_button_click_28_listener", "ctx_r49", "ProfileComponent_div_27_button_36_Template", "ProfileComponent_div_27_div_38_Template", "ProfileComponent_div_27_div_39_Template", "ProfileComponent_div_27_div_40_Template", "ProfileComponent_div_27_div_41_Template", "ctx_r4", "getProfileImageUrl", "ɵɵsanitizeUrl", "selectedImage", "profileImage", "profileTabs", "ctx_r50", "getFieldError", "ctx_r51", "ctx_r52", "ctx_r53", "ctx_r54", "ctx_r55", "ɵɵnamespaceSVG", "ctx_r58", "ctx_r59", "ProfileComponent_div_28_Template_button_click_6_listener", "_r61", "ctx_r60", "cancelEdit", "ɵɵnamespaceHTML", "ProfileComponent_div_28_Template_form_ngSubmit_9_listener", "ctx_r62", "onEditSubmit", "ProfileComponent_div_28_p_15_Template", "ProfileComponent_div_28_p_20_Template", "ProfileComponent_div_28_p_25_Template", "ProfileComponent_div_28_p_30_Template", "ProfileComponent_div_28_p_39_Template", "ProfileComponent_div_28_p_52_Template", "ProfileComponent_div_28_Template_input_change_70_listener", "ctx_r63", "ProfileComponent_div_28_Template_button_click_74_listener", "ctx_r64", "ProfileComponent_div_28_span_77_Template", "ProfileComponent_div_28_span_78_Template", "ProfileComponent_div_28_div_79_Template", "ProfileComponent_div_28_div_80_Template", "ctx_r5", "editForm", "isFieldInvalid", "previewUrl", "editLoading", "ProfileComponent", "constructor", "authService", "authuserService", "dataService", "router", "fb", "isEditMode", "editSection", "group", "<PERSON><PERSON><PERSON><PERSON>", "pattern", "ngOnInit", "getProfile", "subscribe", "next", "res", "image", "trim", "profileImageURL", "calculateProfileCompletion", "populateEditForm", "requiredFields", "optionalFields", "completedRequired", "completedOptional", "for<PERSON>ach", "field", "value", "toString", "requiredPercentage", "optionalPercentage", "imagePercentage", "Math", "round", "patchValue", "Array", "isArray", "join", "event", "input", "target", "files", "file", "validTypes", "includes", "type", "resetFileInput", "size", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "console", "log", "uploadProfileImage", "pipe", "response", "imageUrl", "updateCurrentUser", "setCurrentUser", "token", "localStorage", "setItem", "setTimeout", "err", "confirm", "fileInput", "document", "getElementById", "navigateTo", "path", "navigate", "invalid", "markFormGroupTouched", "formData", "FormData", "Object", "keys", "key", "skillsArray", "split", "map", "skill", "filter", "append", "JSON", "stringify", "completeProfile", "controls", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "fieldName", "errors", "touched", "clearAuthData", "queryParams", "replaceUrl", "section", "completed", "fields", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "AuthuserService", "i3", "DataService", "i4", "Router", "i5", "FormBuilder", "selectors", "decls", "vars", "consts", "template", "ProfileComponent_Template", "rf", "ctx", "ProfileComponent_div_23_Template", "ProfileComponent_div_24_Template", "ProfileComponent_div_25_Template", "ProfileComponent_div_26_Template", "ProfileComponent_div_27_Template", "ProfileComponent_div_28_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\profile\\profile.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\profile\\profile.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { AuthService } from 'src/app/services/auth.service';\r\nimport { finalize } from 'rxjs/operators';\r\nimport { AuthuserService } from 'src/app/services/authuser.service';\r\nimport { Router } from '@angular/router';\r\nimport { DataService } from 'src/app/services/data.service';\r\nimport { User } from 'src/app/models/user.model';\r\n\r\n@Component({\r\n  selector: 'app-profile',\r\n  templateUrl: './profile.component.html',\r\n  styleUrls: ['./profile.component.css'],\r\n})\r\nexport class ProfileComponent implements OnInit {\r\n  user: any;\r\n  selectedImage: File | null = null;\r\n  previewUrl: string | null = null;\r\n  message = '';\r\n  error = '';\r\n  uploadLoading = false;\r\n  removeLoading = false;\r\n\r\n  // Edit profile functionality\r\n  isEditMode = false;\r\n  editForm: FormGroup;\r\n  editLoading = false;\r\n  progressPercentage = 0;\r\n\r\n  // Tab navigation\r\n  activeTab = 'personal';\r\n  editSection = '';\r\n\r\n  profileTabs = [\r\n    { id: 'personal', label: 'Personal Info', icon: 'fas fa-user' },\r\n    { id: 'professional', label: 'Professional', icon: 'fas fa-briefcase' },\r\n    { id: 'account', label: 'Account', icon: 'fas fa-cog' },\r\n    { id: 'completion', label: 'Completion', icon: 'fas fa-chart-line' }\r\n  ];\r\n\r\n  constructor(\r\n    private authService: AuthService,\r\n    private authuserService: AuthuserService,\r\n    private dataService: DataService,\r\n    private router: Router,\r\n    private fb: FormBuilder\r\n  ) {\r\n    this.editForm = this.fb.group({\r\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\r\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\r\n      fullName: ['', [Validators.required, Validators.minLength(2)]],\r\n      email: ['', [Validators.required, Validators.email]],\r\n      dateOfBirth: [''],\r\n      phoneNumber: ['', [Validators.pattern(/^[0-9+\\-\\s()]+$/)]],\r\n      department: [''],\r\n      position: [''],\r\n      bio: ['', [Validators.minLength(10)]],\r\n      address: [''],\r\n      skills: ['']\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // Load user profile using DataService\r\n    this.dataService.getProfile().subscribe({\r\n      next: (res) => {\r\n        this.user = res;\r\n\r\n        // Ensure image properties are consistent\r\n        if (!this.user.profileImage && this.user.image) {\r\n          this.user.profileImage = this.user.image;\r\n        } else if (!this.user.image && this.user.profileImage) {\r\n          this.user.image = this.user.profileImage;\r\n        }\r\n\r\n        // If no image is available, use default\r\n        if (\r\n          !this.user.profileImage ||\r\n          this.user.profileImage === 'null' ||\r\n          this.user.profileImage.trim() === ''\r\n        ) {\r\n          this.user.profileImage = 'assets/images/default-profile.png';\r\n          this.user.image = 'assets/images/default-profile.png';\r\n        }\r\n\r\n        // Ensure profileImageURL is also set for backward compatibility\r\n        if (!this.user.profileImageURL) {\r\n          this.user.profileImageURL = this.user.profileImage || this.user.image;\r\n        }\r\n\r\n        // Calculate profile completion percentage\r\n        this.calculateProfileCompletion();\r\n\r\n        // Populate edit form with current user data\r\n        this.populateEditForm();\r\n      },\r\n      error: () => {\r\n        this.error = 'Failed to load profile.';\r\n      },\r\n    });\r\n  }\r\n\r\n  calculateProfileCompletion(): void {\r\n    if (!this.user) return;\r\n\r\n    const requiredFields = ['firstName', 'lastName', 'fullName', 'email', 'dateOfBirth', 'phoneNumber', 'department', 'bio'];\r\n    const optionalFields = ['position', 'address', 'skills'];\r\n\r\n    let completedRequired = 0;\r\n    let completedOptional = 0;\r\n\r\n    // Check required fields\r\n    requiredFields.forEach(field => {\r\n      const value = this.user[field];\r\n      if (value && value.toString().trim() !== '' && value !== 'uploads/default.png') {\r\n        completedRequired++;\r\n      }\r\n    });\r\n\r\n    // Check optional fields\r\n    optionalFields.forEach(field => {\r\n      const value = this.user[field];\r\n      if (value && value.toString().trim() !== '') {\r\n        completedOptional++;\r\n      }\r\n    });\r\n\r\n    // Check profile image\r\n    let hasProfileImage = 0;\r\n    if (this.user.profileImage &&\r\n        this.user.profileImage !== 'uploads/default.png' &&\r\n        this.user.profileImage !== 'assets/images/default-profile.png' &&\r\n        this.user.profileImage.trim() !== '') {\r\n      hasProfileImage = 1;\r\n    }\r\n\r\n    // Calculate percentage: Required fields (60%) + Optional fields (30%) + Profile Image (10%)\r\n    const requiredPercentage = (completedRequired / requiredFields.length) * 60;\r\n    const optionalPercentage = (completedOptional / optionalFields.length) * 30;\r\n    const imagePercentage = hasProfileImage * 10;\r\n\r\n    this.progressPercentage = Math.round(requiredPercentage + optionalPercentage + imagePercentage);\r\n  }\r\n\r\n  populateEditForm(): void {\r\n    if (!this.user) return;\r\n\r\n    this.editForm.patchValue({\r\n      firstName: this.user.firstName || '',\r\n      lastName: this.user.lastName || '',\r\n      fullName: this.user.fullName || '',\r\n      email: this.user.email || '',\r\n      dateOfBirth: this.user.dateOfBirth || '',\r\n      phoneNumber: this.user.phoneNumber || '',\r\n      department: this.user.department || '',\r\n      position: this.user.position || '',\r\n      bio: this.user.bio || '',\r\n      address: this.user.address || '',\r\n      skills: Array.isArray(this.user.skills) ? this.user.skills.join(', ') : (this.user.skills || '')\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Returns the appropriate profile image URL based on available properties\r\n   * Uses the same logic as in front-layout component for consistency\r\n   */\r\n  getProfileImageUrl(): string {\r\n    if (!this.user) return 'assets/images/default-profile.png';\r\n\r\n    // Check profileImage first\r\n    if (\r\n      this.user.profileImage &&\r\n      this.user.profileImage !== 'null' &&\r\n      this.user.profileImage.trim() !== ''\r\n    ) {\r\n      return this.user.profileImage;\r\n    }\r\n\r\n    // Then check image\r\n    if (\r\n      this.user.image &&\r\n      this.user.image !== 'null' &&\r\n      this.user.image.trim() !== ''\r\n    ) {\r\n      return this.user.image;\r\n    }\r\n\r\n    // Then check profileImageURL (for backward compatibility)\r\n    if (\r\n      this.user.profileImageURL &&\r\n      this.user.profileImageURL !== 'null' &&\r\n      this.user.profileImageURL.trim() !== ''\r\n    ) {\r\n      return this.user.profileImageURL;\r\n    }\r\n\r\n    // Default fallback\r\n    return 'assets/images/default-profile.png';\r\n  }\r\n\r\n  onFileSelected(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files?.length) {\r\n      const file = input.files[0];\r\n\r\n      const validTypes = ['image/jpeg', 'image/png', 'image/webp'];\r\n      if (!validTypes.includes(file.type)) {\r\n        this.error = 'Seuls les JPEG, PNG et WebP sont autorisés';\r\n        this.resetFileInput();\r\n        return;\r\n      }\r\n\r\n      if (file.size > 2 * 1024 * 1024) {\r\n        this.error = \"L'image ne doit pas dépasser 2MB\";\r\n        this.resetFileInput();\r\n        return;\r\n      }\r\n\r\n      this.selectedImage = file;\r\n      this.error = '';\r\n\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => {\r\n        this.previewUrl = (e.target?.result as string) || null;\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  }\r\n\r\n  onUpload(): void {\r\n    if (!this.selectedImage) return;\r\n\r\n    this.uploadLoading = true; // Activer l'état de chargement\r\n    this.message = '';\r\n    this.error = '';\r\n\r\n    console.log('Upload started, uploadLoading:', this.uploadLoading);\r\n\r\n    this.dataService\r\n      .uploadProfileImage(this.selectedImage)\r\n      .pipe(\r\n        finalize(() => {\r\n          this.uploadLoading = false;\r\n          console.log('Upload finished, uploadLoading:', this.uploadLoading);\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.message = response.message || 'Profile updated successfully';\r\n\r\n          // Update all image properties to ensure consistency across the application\r\n          this.user.profileImageURL = response.imageUrl;\r\n          this.user.profileImage = response.imageUrl;\r\n          this.user.image = response.imageUrl;\r\n\r\n          // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\r\n          this.dataService.updateCurrentUser({\r\n            profileImage: response.imageUrl,\r\n            image: response.imageUrl,\r\n          });\r\n\r\n          // Also update in AuthUserService to ensure all components are updated\r\n          this.authuserService.setCurrentUser({\r\n            ...this.user,\r\n            profileImage: response.imageUrl,\r\n            image: response.imageUrl,\r\n          });\r\n\r\n          this.selectedImage = null;\r\n          this.previewUrl = null;\r\n          this.resetFileInput();\r\n\r\n          if (response.token) {\r\n            localStorage.setItem('token', response.token);\r\n          }\r\n\r\n          // Auto-hide message after 3 seconds\r\n          setTimeout(() => {\r\n            this.message = '';\r\n          }, 3000);\r\n        },\r\n        error: (err: { error: { message: string } }) => {\r\n          this.error = err.error?.message || 'Upload failed';\r\n          // Auto-hide error after 3 seconds\r\n          setTimeout(() => {\r\n            this.error = '';\r\n          }, 3000);\r\n        },\r\n      });\r\n  }\r\n\r\n  removeProfileImage(): void {\r\n    if (!confirm('Are you sure you want to remove your profile picture?'))\r\n      return;\r\n\r\n    this.removeLoading = true;\r\n    this.message = '';\r\n    this.error = '';\r\n\r\n    this.dataService\r\n      .removeProfileImage()\r\n      .pipe(finalize(() => (this.removeLoading = false)))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.message =\r\n            response.message || 'Profile picture removed successfully';\r\n\r\n          // Update all image properties to ensure consistency across the application\r\n          this.user.profileImageURL = null;\r\n          this.user.profileImage = null;\r\n          this.user.image = null;\r\n\r\n          // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\r\n          this.dataService.updateCurrentUser({\r\n            profileImage: 'assets/images/default-profile.png',\r\n            image: 'assets/images/default-profile.png',\r\n          });\r\n\r\n          // Also update in AuthUserService to ensure all components are updated\r\n          this.authuserService.setCurrentUser({\r\n            ...this.user,\r\n            profileImage: 'assets/images/default-profile.png',\r\n            image: 'assets/images/default-profile.png',\r\n          });\r\n\r\n          if (response.token) {\r\n            localStorage.setItem('token', response.token);\r\n          }\r\n\r\n          // Auto-hide message after 3 seconds\r\n          setTimeout(() => {\r\n            this.message = '';\r\n          }, 3000);\r\n        },\r\n        error: (err: { error: { message: string } }) => {\r\n          this.error = err.error?.message || 'Removal failed';\r\n          // Auto-hide error after 3 seconds\r\n          setTimeout(() => {\r\n            this.error = '';\r\n          }, 3000);\r\n        },\r\n      });\r\n  }\r\n\r\n  private resetFileInput(): void {\r\n    this.selectedImage = null;\r\n    this.previewUrl = null;\r\n    const fileInput = document.getElementById(\r\n      'profile-upload'\r\n    ) as HTMLInputElement;\r\n    if (fileInput) fileInput.value = '';\r\n  }\r\n\r\n  navigateTo(path: string): void {\r\n    this.router.navigate([path]);\r\n  }\r\n\r\n  // Edit profile methods\r\n  toggleEditMode(): void {\r\n    this.isEditMode = !this.isEditMode;\r\n    if (this.isEditMode) {\r\n      this.populateEditForm();\r\n    }\r\n    this.message = '';\r\n    this.error = '';\r\n  }\r\n\r\n  onEditSubmit(): void {\r\n    if (this.editForm.invalid) {\r\n      this.markFormGroupTouched();\r\n      return;\r\n    }\r\n\r\n    this.editLoading = true;\r\n    this.error = '';\r\n    this.message = '';\r\n\r\n    const formData = new FormData();\r\n\r\n    // Add form fields\r\n    Object.keys(this.editForm.value).forEach(key => {\r\n      const value = this.editForm.value[key];\r\n      if (key === 'skills' && value) {\r\n        // Convert skills string to array\r\n        const skillsArray = value.split(',').map((skill: string) => skill.trim()).filter((skill: string) => skill);\r\n        formData.append(key, JSON.stringify(skillsArray));\r\n      } else if (value) {\r\n        formData.append(key, value);\r\n      }\r\n    });\r\n\r\n    // Add profile image if selected\r\n    if (this.selectedImage) {\r\n      formData.append('image', this.selectedImage);\r\n    }\r\n\r\n    this.dataService.completeProfile(formData).subscribe({\r\n      next: (response: any) => {\r\n        this.editLoading = false;\r\n        this.message = 'Profile updated successfully!';\r\n\r\n        // Update current user data\r\n        this.user = { ...this.user, ...response.user };\r\n        this.authuserService.setCurrentUser(this.user);\r\n\r\n        // Recalculate progress\r\n        this.calculateProfileCompletion();\r\n\r\n        // Exit edit mode\r\n        this.isEditMode = false;\r\n\r\n        // Clear selected image\r\n        this.selectedImage = null;\r\n        this.previewUrl = null;\r\n        this.resetFileInput();\r\n\r\n        // Auto-hide message after 3 seconds\r\n        setTimeout(() => {\r\n          this.message = '';\r\n        }, 3000);\r\n      },\r\n      error: (err) => {\r\n        this.editLoading = false;\r\n        this.error = err.error?.message || 'An error occurred while updating your profile.';\r\n\r\n        // Auto-hide error after 5 seconds\r\n        setTimeout(() => {\r\n          this.error = '';\r\n        }, 5000);\r\n      }\r\n    });\r\n  }\r\n\r\n  cancelEdit(): void {\r\n    this.isEditMode = false;\r\n    this.populateEditForm(); // Reset form to original values\r\n    this.selectedImage = null;\r\n    this.previewUrl = null;\r\n    this.resetFileInput();\r\n    this.message = '';\r\n    this.error = '';\r\n  }\r\n\r\n  private markFormGroupTouched(): void {\r\n    Object.keys(this.editForm.controls).forEach(key => {\r\n      this.editForm.get(key)?.markAsTouched();\r\n    });\r\n  }\r\n\r\n  // Helper methods for template\r\n  getFieldError(fieldName: string): string {\r\n    const field = this.editForm.get(fieldName);\r\n    if (field?.errors && field.touched) {\r\n      if (field.errors['required']) return `${fieldName} is required`;\r\n      if (field.errors['minlength']) return `${fieldName} is too short`;\r\n      if (field.errors['email']) return `Invalid email format`;\r\n      if (field.errors['pattern']) return `${fieldName} format is invalid`;\r\n    }\r\n    return '';\r\n  }\r\n\r\n  isFieldInvalid(fieldName: string): boolean {\r\n    const field = this.editForm.get(fieldName);\r\n    return !!(field?.invalid && field.touched);\r\n  }\r\n\r\n  getProgressColor(): string {\r\n    if (this.progressPercentage < 25) return '#ef4444'; // red\r\n    if (this.progressPercentage < 50) return '#f97316'; // orange\r\n    if (this.progressPercentage < 75) return '#eab308'; // yellow\r\n    if (this.progressPercentage < 100) return '#22c55e'; // green\r\n    return '#10b981'; // emerald\r\n  }\r\n\r\n  getMotivationalMessage(): string {\r\n    if (this.progressPercentage < 25) {\r\n      return \"Let's complete your profile to unlock all features! 🚀\";\r\n    } else if (this.progressPercentage < 50) {\r\n      return \"You're making great progress! Keep going! 💪\";\r\n    } else if (this.progressPercentage < 75) {\r\n      return \"Excellent! You're more than halfway there! 🌟\";\r\n    } else if (this.progressPercentage < 100) {\r\n      return \"Almost perfect! Just a few more details! 🎯\";\r\n    } else {\r\n      return \"Perfect! Your profile is complete! ✨\";\r\n    }\r\n  }\r\n\r\n  logout(): void {\r\n    this.authuserService.logout().subscribe({\r\n      next: () => {\r\n        this.authuserService.clearAuthData();\r\n        setTimeout(() => {\r\n          this.router.navigate(['/login'], {\r\n            queryParams: { message: 'Déconnexion réussie' },\r\n            replaceUrl: true,\r\n          });\r\n        }, 100);\r\n      },\r\n      error: (err: any) => {\r\n        console.error('Logout error:', err);\r\n        this.authuserService.clearAuthData();\r\n        setTimeout(() => {\r\n          this.router.navigate(['/login'], {});\r\n        }, 100);\r\n      },\r\n    });\r\n  }\r\n\r\n  // New methods for enhanced profile functionality\r\n  setEditSection(section: string): void {\r\n    this.editSection = section;\r\n    this.isEditMode = true;\r\n    this.populateEditForm();\r\n  }\r\n\r\n  getRequiredFieldsCompletion(): number {\r\n    if (!this.user) return 0;\r\n\r\n    const requiredFields = ['firstName', 'lastName', 'fullName', 'email', 'dateOfBirth', 'phoneNumber', 'department', 'bio'];\r\n    let completed = 0;\r\n\r\n    requiredFields.forEach(field => {\r\n      const value = this.user[field];\r\n      if (value && value.toString().trim() !== '' && value !== 'uploads/default.png') {\r\n        completed++;\r\n      }\r\n    });\r\n\r\n    return Math.round((completed / requiredFields.length) * 100);\r\n  }\r\n\r\n  getOptionalFieldsCompletion(): number {\r\n    if (!this.user) return 0;\r\n\r\n    const optionalFields = ['position', 'address', 'skills'];\r\n    let completed = 0;\r\n\r\n    optionalFields.forEach(field => {\r\n      const value = this.user[field];\r\n      if (field === 'skills') {\r\n        // Special handling for skills array\r\n        if (Array.isArray(value) && value.length > 0) {\r\n          completed++;\r\n        }\r\n      } else if (value && value.toString().trim() !== '') {\r\n        completed++;\r\n      }\r\n    });\r\n\r\n    return Math.round((completed / optionalFields.length) * 100);\r\n  }\r\n\r\n  hasProfileImage(): boolean {\r\n    if (!this.user) return false;\r\n\r\n    return !!(this.user.profileImage &&\r\n             this.user.profileImage !== 'uploads/default.png' &&\r\n             this.user.profileImage !== 'assets/images/default-profile.png' &&\r\n             this.user.profileImage.trim() !== '');\r\n  }\r\n\r\n  getMissingFields(): Array<{field: string, label: string, required: boolean}> {\r\n    if (!this.user) return [];\r\n\r\n    const fields = [\r\n      { field: 'firstName', label: 'First Name', required: true },\r\n      { field: 'lastName', label: 'Last Name', required: true },\r\n      { field: 'fullName', label: 'Full Name', required: true },\r\n      { field: 'dateOfBirth', label: 'Date of Birth', required: true },\r\n      { field: 'phoneNumber', label: 'Phone Number', required: true },\r\n      { field: 'department', label: 'Department', required: true },\r\n      { field: 'bio', label: 'Bio', required: true },\r\n      { field: 'position', label: 'Position', required: false },\r\n      { field: 'address', label: 'Address', required: false },\r\n      { field: 'skills', label: 'Skills', required: false }\r\n    ];\r\n\r\n    return fields.filter(field => {\r\n      const value = this.user[field.field];\r\n      return !value || value.toString().trim() === '';\r\n    });\r\n  }\r\n}\r\n", "<!-- Begin Page Content -->\r\n<div\r\n  class=\"container-fluid p-4 md:p-6 bg-[#edf1f4] dark:bg-[#121212] min-h-screen relative\"\r\n>\r\n  <!-- Background decorative elements -->\r\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n    <div\r\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n    <div\r\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n\r\n    <!-- Grid pattern -->\r\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\r\n      <div class=\"h-full grid grid-cols-12\">\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"container mx-auto px-4 py-6 relative z-10\">\r\n    <!-- Page Title -->\r\n    <div class=\"mb-8\">\r\n      <h1\r\n        class=\"text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\r\n      >\r\n        Mon Profil\r\n      </h1>\r\n      <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mt-1\">\r\n        Gérez vos informations personnelles et vos préférences\r\n      </p>\r\n    </div>\r\n\r\n    <!-- Loading State -->\r\n    <div *ngIf=\"!user\" class=\"flex justify-center items-center py-20\">\r\n      <div class=\"relative\">\r\n        <div\r\n          class=\"w-14 h-14 border-4 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin\"\r\n        ></div>\r\n        <!-- Glow effect -->\r\n        <div\r\n          class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n        ></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Error Message -->\r\n    <div\r\n      *ngIf=\"error\"\r\n      class=\"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-3xl my-4 backdrop-blur-sm\"\r\n    >\r\n      <div class=\"flex items-start\">\r\n        <div class=\"text-[#ff6b69] dark:text-[#ff8785] mr-3 text-xl relative\">\r\n          <i class=\"fas fa-exclamation-triangle\"></i>\r\n          <!-- Glow effect -->\r\n          <div\r\n            class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n          ></div>\r\n        </div>\r\n        <div>\r\n          <h3 class=\"font-medium text-[#ff6b69] dark:text-[#ff8785] mb-1\">\r\n            Erreur\r\n          </h3>\r\n          <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">{{ error }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Success Message -->\r\n    <div\r\n      *ngIf=\"message\"\r\n      class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-4 mx-auto max-w-3xl my-4 backdrop-blur-sm\"\r\n    >\r\n      <div class=\"flex items-start\">\r\n        <div class=\"text-[#4f5fad] dark:text-[#6d78c9] mr-3 text-xl relative\">\r\n          <i class=\"fas fa-check-circle\"></i>\r\n          <!-- Glow effect -->\r\n          <div\r\n            class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n          ></div>\r\n        </div>\r\n        <div>\r\n          <h3 class=\"font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-1\">\r\n            Succès\r\n          </h3>\r\n          <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\r\n            {{ message }}\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Profile Completion Progress -->\r\n    <div *ngIf=\"user\" class=\"mb-8\">\r\n      <div class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-6 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative overflow-hidden\">\r\n        <!-- Decorative gradient top border -->\r\n        <div class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"></div>\r\n\r\n        <div class=\"flex items-center justify-between mb-4\">\r\n          <h3 class=\"text-lg font-semibold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\">\r\n            Profile Completion\r\n          </h3>\r\n          <span class=\"text-2xl font-bold\" [style.color]=\"getProgressColor()\">\r\n            {{ progressPercentage }}%\r\n          </span>\r\n        </div>\r\n\r\n        <!-- Progress Bar -->\r\n        <div class=\"w-full bg-[#e2e8f0] dark:bg-[#2a2a2a] rounded-full h-4 overflow-hidden mb-3\">\r\n          <div\r\n            class=\"h-full rounded-full transition-all duration-500 ease-out\"\r\n            [style.width.%]=\"progressPercentage\"\r\n            [style.background-color]=\"getProgressColor()\">\r\n          </div>\r\n        </div>\r\n\r\n        <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\r\n          {{ getMotivationalMessage() }}\r\n        </p>\r\n\r\n        <!-- Complete Profile Button (if not 100%) -->\r\n        <div *ngIf=\"progressPercentage < 100\" class=\"mt-4\">\r\n          <button\r\n            (click)=\"toggleEditMode()\"\r\n            class=\"px-4 py-2 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all text-sm font-medium\">\r\n            Complete Profile\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- User Profile -->\r\n    <div *ngIf=\"user\" class=\"space-y-6\">\r\n      <!-- Profile Header Card -->\r\n      <div\r\n        class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-6 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative overflow-hidden group\"\r\n      >\r\n        <!-- Decorative gradient top border -->\r\n        <div\r\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n        ></div>\r\n\r\n        <!-- Profile Header Content -->\r\n        <div class=\"flex flex-col md:flex-row items-center md:items-start gap-6\">\r\n          <!-- Profile Image Section -->\r\n          <div class=\"flex-shrink-0 text-center\">\r\n            <div class=\"relative group/avatar\">\r\n              <img\r\n                [src]=\"getProfileImageUrl()\"\r\n                alt=\"Profile Image\"\r\n                class=\"w-32 h-32 rounded-full object-cover border-4 border-[#4f5fad] dark:border-[#6d78c9] shadow-lg group-hover/avatar:scale-105 transition-transform duration-300\"\r\n              />\r\n              <!-- Profile Image Upload Overlay -->\r\n              <div class=\"absolute inset-0 bg-black bg-opacity-50 rounded-full opacity-0 group-hover/avatar:opacity-100 transition-opacity duration-300 flex items-center justify-center\">\r\n                <label for=\"profile-upload\" class=\"cursor-pointer text-white text-sm font-medium\">\r\n                  <i class=\"fas fa-camera text-xl mb-1 block\"></i>\r\n                  Change Photo\r\n                </label>\r\n                <input\r\n                  type=\"file\"\r\n                  id=\"profile-upload\"\r\n                  accept=\"image/*\"\r\n                  (change)=\"onFileSelected($event)\"\r\n                  class=\"hidden\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Upload/Remove Buttons -->\r\n            <div class=\"flex gap-2 mt-4\">\r\n              <button\r\n                *ngIf=\"selectedImage\"\r\n                (click)=\"onUpload()\"\r\n                [disabled]=\"uploadLoading\"\r\n                class=\"px-3 py-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white text-xs rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all disabled:opacity-50\">\r\n                <i *ngIf=\"!uploadLoading\" class=\"fas fa-upload mr-1\"></i>\r\n                <i *ngIf=\"uploadLoading\" class=\"fas fa-spinner fa-spin mr-1\"></i>\r\n                {{ uploadLoading ? 'Uploading...' : 'Upload' }}\r\n              </button>\r\n\r\n              <button\r\n                *ngIf=\"user.profileImage && user.profileImage !== 'assets/images/default-profile.png'\"\r\n                (click)=\"removeProfileImage()\"\r\n                [disabled]=\"removeLoading\"\r\n                class=\"px-3 py-1 bg-red-500 text-white text-xs rounded-lg hover:bg-red-600 transition-all disabled:opacity-50\">\r\n                <i *ngIf=\"!removeLoading\" class=\"fas fa-trash mr-1\"></i>\r\n                <i *ngIf=\"removeLoading\" class=\"fas fa-spinner fa-spin mr-1\"></i>\r\n                {{ removeLoading ? 'Removing...' : 'Remove' }}\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Profile Info Section -->\r\n          <div class=\"flex-1 text-center md:text-left\">\r\n            <h2 class=\"text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-2\">\r\n              {{ user.fullName || (user.firstName + ' ' + user.lastName) }}\r\n            </h2>\r\n            <p class=\"text-[#6d6870] dark:text-[#a0a0a0] mb-2\">{{ user.email }}</p>\r\n            <div class=\"flex flex-wrap gap-2 justify-center md:justify-start mb-4\">\r\n              <span class=\"px-3 py-1 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] text-sm rounded-full\">\r\n                {{ user.role | titlecase }}\r\n              </span>\r\n              <span *ngIf=\"user.department\" class=\"px-3 py-1 bg-[#3d4a85]/10 dark:bg-[#4f5fad]/10 text-[#3d4a85] dark:text-[#4f5fad] text-sm rounded-full\">\r\n                {{ user.department }}\r\n              </span>\r\n              <span *ngIf=\"user.position\" class=\"px-3 py-1 bg-[#7826b5]/10 dark:bg-[#6d78c9]/10 text-[#7826b5] dark:text-[#6d78c9] text-sm rounded-full\">\r\n                {{ user.position }}\r\n              </span>\r\n            </div>\r\n\r\n            <!-- Bio Preview -->\r\n            <p *ngIf=\"user.bio\" class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm leading-relaxed mb-4\">\r\n              {{ user.bio.length > 150 ? (user.bio | slice:0:150) + '...' : user.bio }}\r\n            </p>\r\n\r\n            <!-- Quick Actions -->\r\n            <div class=\"flex flex-wrap gap-2 justify-center md:justify-start\">\r\n              <button\r\n                (click)=\"toggleEditMode()\"\r\n                class=\"px-4 py-2 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white text-sm rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all flex items-center\">\r\n                <i class=\"fas fa-edit mr-2\"></i>\r\n                Edit Profile\r\n              </button>\r\n              <a\r\n                [routerLink]=\"user.role === 'admin' ? '/admin/dashboard' : '/home'\"\r\n                class=\"px-4 py-2 border border-[#4f5fad] dark:border-[#6d78c9] text-[#4f5fad] dark:text-[#6d78c9] text-sm rounded-lg hover:bg-[#4f5fad] hover:text-white dark:hover:bg-[#6d78c9] dark:hover:text-white transition-all flex items-center\">\r\n                <i class=\"fas fa-home mr-2\"></i>\r\n                Dashboard\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Navigation Tabs -->\r\n      <div class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\">\r\n        <div class=\"flex flex-wrap border-b border-[#edf1f4] dark:border-[#2a2a2a]\">\r\n          <button\r\n            *ngFor=\"let tab of profileTabs\"\r\n            (click)=\"activeTab = tab.id\"\r\n            [class.bg-gradient-to-r]=\"activeTab === tab.id\"\r\n            [class.from-[#4f5fad]]=\"activeTab === tab.id\"\r\n            [class.to-[#7826b5]]=\"activeTab === tab.id\"\r\n            [class.text-white]=\"activeTab === tab.id\"\r\n            [class.text-[#6d6870]]=\"activeTab !== tab.id\"\r\n            [class.dark:text-[#a0a0a0]]=\"activeTab !== tab.id\"\r\n            class=\"flex-1 px-4 py-3 text-sm font-medium transition-all hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 flex items-center justify-center gap-2\">\r\n            <i [class]=\"tab.icon\"></i>\r\n            {{ tab.label }}\r\n            <span *ngIf=\"tab.id === 'completion' && progressPercentage < 100\"\r\n                  class=\"px-2 py-1 bg-orange-500 text-white text-xs rounded-full\">\r\n              {{ progressPercentage }}%\r\n            </span>\r\n          </button>\r\n        </div>\r\n\r\n        <!-- Tab Content -->\r\n        <div class=\"p-6\">\r\n          <!-- Personal Information Tab -->\r\n          <div *ngIf=\"activeTab === 'personal'\" class=\"space-y-6\">\r\n            <div class=\"flex items-center justify-between mb-6\">\r\n              <h3 class=\"text-xl font-semibold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent flex items-center\">\r\n                <i class=\"fas fa-user mr-3 text-[#4f5fad] dark:text-[#6d78c9]\"></i>\r\n                Personal Information\r\n              </h3>\r\n              <button\r\n                (click)=\"setEditSection('personal')\"\r\n                class=\"px-3 py-1 text-sm bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] rounded-lg hover:bg-[#4f5fad]/20 dark:hover:bg-[#6d78c9]/20 transition-all\">\r\n                <i class=\"fas fa-edit mr-1\"></i>\r\n                Edit\r\n              </button>\r\n            </div>\r\n\r\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              <!-- First Name -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">First Name</label>\r\n                <p class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ user.firstName || 'Not provided' }}</p>\r\n              </div>\r\n\r\n              <!-- Last Name -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">Last Name</label>\r\n                <p class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ user.lastName || 'Not provided' }}</p>\r\n              </div>\r\n\r\n              <!-- Full Name -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">Full Name</label>\r\n                <p class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ user.fullName || 'Not provided' }}</p>\r\n              </div>\r\n\r\n              <!-- Date of Birth -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">Date of Birth</label>\r\n                <p class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">\r\n                  {{ user.dateOfBirth ? (user.dateOfBirth | date:'mediumDate') : 'Not provided' }}\r\n                </p>\r\n              </div>\r\n\r\n              <!-- Phone Number -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">Phone Number</label>\r\n                <p class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ user.phoneNumber || 'Not provided' }}</p>\r\n              </div>\r\n\r\n              <!-- Address -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">Address</label>\r\n                <p class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ user.address || 'Not provided' }}</p>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Bio Section -->\r\n            <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n              <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-2\">Bio</label>\r\n              <p class=\"text-[#4f5fad] dark:text-[#6d78c9] leading-relaxed\">\r\n                {{ user.bio || 'Tell us about yourself, your interests, and goals...' }}\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Professional Information Tab -->\r\n          <div *ngIf=\"activeTab === 'professional'\" class=\"space-y-6\">\r\n            <div class=\"flex items-center justify-between mb-6\">\r\n              <h3 class=\"text-xl font-semibold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent flex items-center\">\r\n                <i class=\"fas fa-briefcase mr-3 text-[#4f5fad] dark:text-[#6d78c9]\"></i>\r\n                Professional Information\r\n              </h3>\r\n              <button\r\n                (click)=\"setEditSection('professional')\"\r\n                class=\"px-3 py-1 text-sm bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] rounded-lg hover:bg-[#4f5fad]/20 dark:hover:bg-[#6d78c9]/20 transition-all\">\r\n                <i class=\"fas fa-edit mr-1\"></i>\r\n                Edit\r\n              </button>\r\n            </div>\r\n\r\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              <!-- Department -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">Department</label>\r\n                <p class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ user.department || 'Not specified' }}</p>\r\n              </div>\r\n\r\n              <!-- Position -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">Position</label>\r\n                <p class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ user.position || 'Not specified' }}</p>\r\n              </div>\r\n\r\n              <!-- Role -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">Role</label>\r\n                <p class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ user.role | titlecase }}</p>\r\n              </div>\r\n\r\n              <!-- Member Since -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">Member Since</label>\r\n                <p class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ user.createdAt | date:'mediumDate' }}</p>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Skills Section -->\r\n            <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n              <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-3\">Skills & Expertise</label>\r\n              <div *ngIf=\"user.skills && user.skills.length > 0; else noSkills\" class=\"flex flex-wrap gap-2\">\r\n                <span *ngFor=\"let skill of user.skills\"\r\n                      class=\"px-3 py-1 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] text-sm rounded-full border border-[#4f5fad]/20 dark:border-[#6d78c9]/20\">\r\n                  {{ skill }}\r\n                </span>\r\n              </div>\r\n              <ng-template #noSkills>\r\n                <p class=\"text-[#6d6870] dark:text-[#a0a0a0] italic\">No skills added yet</p>\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Account Settings Tab -->\r\n          <div *ngIf=\"activeTab === 'account'\" class=\"space-y-6\">\r\n            <div class=\"flex items-center justify-between mb-6\">\r\n              <h3 class=\"text-xl font-semibold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent flex items-center\">\r\n                <i class=\"fas fa-cog mr-3 text-[#4f5fad] dark:text-[#6d78c9]\"></i>\r\n                Account Settings\r\n              </h3>\r\n            </div>\r\n\r\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              <!-- Email -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">Email Address</label>\r\n                <p class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ user.email }}</p>\r\n                <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-1\">Primary contact email</p>\r\n              </div>\r\n\r\n              <!-- Account Status -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">Account Status</label>\r\n                <div class=\"flex items-center gap-2\">\r\n                  <span class=\"px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200 text-xs rounded-full\">\r\n                    Active\r\n                  </span>\r\n                  <span class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ user.role | titlecase }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Account Actions -->\r\n            <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-6 rounded-lg\">\r\n              <h4 class=\"text-lg font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-4\">Account Actions</h4>\r\n              <div class=\"flex flex-wrap gap-3\">\r\n                <!-- Change Password -->\r\n                <a\r\n                  routerLink=\"/change-password\"\r\n                  class=\"px-4 py-2 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] rounded-lg hover:bg-[#4f5fad]/20 dark:hover:bg-[#6d78c9]/20 transition-all flex items-center\">\r\n                  <i class=\"fas fa-key mr-2\"></i>\r\n                  Change Password\r\n                </a>\r\n\r\n                <!-- Logout -->\r\n                <button\r\n                  (click)=\"logout()\"\r\n                  class=\"px-4 py-2 bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/40 transition-all flex items-center\">\r\n                  <i class=\"fas fa-sign-out-alt mr-2\"></i>\r\n                  Logout\r\n                </button>\r\n\r\n                <!-- Dashboard -->\r\n                <a\r\n                  [routerLink]=\"user.role === 'admin' ? '/admin/dashboard' : '/home'\"\r\n                  class=\"px-4 py-2 bg-[#6d6870]/10 dark:bg-[#a0a0a0]/10 text-[#6d6870] dark:text-[#a0a0a0] rounded-lg hover:bg-[#6d6870]/20 dark:hover:bg-[#a0a0a0]/20 transition-all flex items-center\">\r\n                  <i class=\"fas fa-home mr-2\"></i>\r\n                  Go to Dashboard\r\n                </a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Profile Completion Tab -->\r\n          <div *ngIf=\"activeTab === 'completion'\" class=\"space-y-6\">\r\n            <div class=\"flex items-center justify-between mb-6\">\r\n              <h3 class=\"text-xl font-semibold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent flex items-center\">\r\n                <i class=\"fas fa-chart-line mr-3 text-[#4f5fad] dark:text-[#6d78c9]\"></i>\r\n                Profile Completion\r\n              </h3>\r\n              <span class=\"text-2xl font-bold\" [style.color]=\"getProgressColor()\">\r\n                {{ progressPercentage }}%\r\n              </span>\r\n            </div>\r\n\r\n            <!-- Progress Overview -->\r\n            <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-6 rounded-lg\">\r\n              <div class=\"flex items-center justify-between mb-4\">\r\n                <h4 class=\"text-lg font-medium text-[#4f5fad] dark:text-[#6d78c9]\">Overall Progress</h4>\r\n                <span class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">{{ getMotivationalMessage() }}</span>\r\n              </div>\r\n\r\n              <!-- Progress Bar -->\r\n              <div class=\"w-full bg-[#e2e8f0] dark:bg-[#2a2a2a] rounded-full h-4 overflow-hidden mb-4\">\r\n                <div\r\n                  class=\"h-full rounded-full transition-all duration-500 ease-out\"\r\n                  [style.width.%]=\"progressPercentage\"\r\n                  [style.background-color]=\"getProgressColor()\">\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Completion Breakdown -->\r\n              <div class=\"grid grid-cols-1 md:grid-cols-3 gap-4 mt-6\">\r\n                <div class=\"text-center p-4 bg-white dark:bg-[#1e1e1e] rounded-lg\">\r\n                  <div class=\"text-2xl font-bold text-[#4f5fad] dark:text-[#6d78c9]\">{{ getRequiredFieldsCompletion() }}%</div>\r\n                  <div class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">Required Fields</div>\r\n                  <div class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-1\">(60% weight)</div>\r\n                </div>\r\n\r\n                <div class=\"text-center p-4 bg-white dark:bg-[#1e1e1e] rounded-lg\">\r\n                  <div class=\"text-2xl font-bold text-[#7826b5] dark:text-[#6d78c9]\">{{ getOptionalFieldsCompletion() }}%</div>\r\n                  <div class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">Optional Fields</div>\r\n                  <div class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-1\">(30% weight)</div>\r\n                </div>\r\n\r\n                <div class=\"text-center p-4 bg-white dark:bg-[#1e1e1e] rounded-lg\">\r\n                  <div class=\"text-2xl font-bold text-[#3d4a85] dark:text-[#4f5fad]\">{{ hasProfileImage() ? 100 : 0 }}%</div>\r\n                  <div class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">Profile Image</div>\r\n                  <div class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-1\">(10% weight)</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Missing Fields -->\r\n            <div *ngIf=\"getMissingFields().length > 0\" class=\"bg-orange-50 dark:bg-orange-900/20 p-6 rounded-lg border border-orange-200 dark:border-orange-800\">\r\n              <h4 class=\"text-lg font-medium text-orange-800 dark:text-orange-200 mb-4 flex items-center\">\r\n                <i class=\"fas fa-exclamation-triangle mr-2\"></i>\r\n                Missing Information\r\n              </h4>\r\n              <div class=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\r\n                <div *ngFor=\"let field of getMissingFields()\"\r\n                     class=\"flex items-center justify-between p-3 bg-white dark:bg-[#1e1e1e] rounded-lg\">\r\n                  <span class=\"text-[#6d6870] dark:text-[#a0a0a0]\">{{ field.label }}</span>\r\n                  <span class=\"px-2 py-1 bg-orange-100 dark:bg-orange-900/40 text-orange-800 dark:text-orange-200 text-xs rounded-full\">\r\n                    {{ field.required ? 'Required' : 'Optional' }}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n              <button\r\n                (click)=\"activeTab = 'personal'\"\r\n                class=\"mt-4 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-all\">\r\n                Complete Missing Fields\r\n              </button>\r\n            </div>\r\n\r\n            <!-- Completion Tips -->\r\n            <div class=\"bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg border border-blue-200 dark:border-blue-800\">\r\n              <h4 class=\"text-lg font-medium text-blue-800 dark:text-blue-200 mb-4 flex items-center\">\r\n                <i class=\"fas fa-lightbulb mr-2\"></i>\r\n                Tips for a Complete Profile\r\n              </h4>\r\n              <ul class=\"space-y-2 text-blue-700 dark:text-blue-300\">\r\n                <li class=\"flex items-start\">\r\n                  <i class=\"fas fa-check-circle mr-2 mt-1 text-green-500\"></i>\r\n                  Add a professional profile photo to help others recognize you\r\n                </li>\r\n                <li class=\"flex items-start\">\r\n                  <i class=\"fas fa-check-circle mr-2 mt-1 text-green-500\"></i>\r\n                  Write a compelling bio that showcases your interests and goals\r\n                </li>\r\n                <li class=\"flex items-start\">\r\n                  <i class=\"fas fa-check-circle mr-2 mt-1 text-green-500\"></i>\r\n                  List your skills to help with project matching and collaboration\r\n                </li>\r\n                <li class=\"flex items-start\">\r\n                  <i class=\"fas fa-check-circle mr-2 mt-1 text-green-500\"></i>\r\n                  Keep your contact information up to date\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Edit Profile Modal/Form -->\r\n    <div *ngIf=\"isEditMode\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n      <div class=\"bg-white dark:bg-[#1e1e1e] rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\r\n        <!-- Header -->\r\n        <div class=\"bg-gradient-to-r from-[#4f5fad] to-[#7826b5] p-6 rounded-t-2xl\">\r\n          <div class=\"flex justify-between items-center\">\r\n            <h2 class=\"text-2xl font-bold text-white\">Edit Profile</h2>\r\n            <button\r\n              (click)=\"cancelEdit()\"\r\n              class=\"text-white hover:text-gray-200 transition-colors\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Form Content -->\r\n        <form [formGroup]=\"editForm\" (ngSubmit)=\"onEditSubmit()\" class=\"p-6\">\r\n          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n            <!-- First Name -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                First Name *\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                formControlName=\"firstName\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                [class.border-red-500]=\"isFieldInvalid('firstName')\"\r\n                placeholder=\"Enter your first name\">\r\n              <p *ngIf=\"getFieldError('firstName')\" class=\"text-red-500 text-sm mt-1\">\r\n                {{ getFieldError('firstName') }}\r\n              </p>\r\n            </div>\r\n\r\n            <!-- Last Name -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                Last Name *\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                formControlName=\"lastName\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                [class.border-red-500]=\"isFieldInvalid('lastName')\"\r\n                placeholder=\"Enter your last name\">\r\n              <p *ngIf=\"getFieldError('lastName')\" class=\"text-red-500 text-sm mt-1\">\r\n                {{ getFieldError('lastName') }}\r\n              </p>\r\n            </div>\r\n\r\n            <!-- Full Name -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                Full Name *\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                formControlName=\"fullName\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                [class.border-red-500]=\"isFieldInvalid('fullName')\"\r\n                placeholder=\"Enter your full name\">\r\n              <p *ngIf=\"getFieldError('fullName')\" class=\"text-red-500 text-sm mt-1\">\r\n                {{ getFieldError('fullName') }}\r\n              </p>\r\n            </div>\r\n\r\n            <!-- Email -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                Email *\r\n              </label>\r\n              <input\r\n                type=\"email\"\r\n                formControlName=\"email\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                [class.border-red-500]=\"isFieldInvalid('email')\"\r\n                placeholder=\"Enter your email\">\r\n              <p *ngIf=\"getFieldError('email')\" class=\"text-red-500 text-sm mt-1\">\r\n                {{ getFieldError('email') }}\r\n              </p>\r\n            </div>\r\n\r\n            <!-- Date of Birth -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                Date of Birth\r\n              </label>\r\n              <input\r\n                type=\"date\"\r\n                formControlName=\"dateOfBirth\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\">\r\n            </div>\r\n\r\n            <!-- Phone Number -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                Phone Number\r\n              </label>\r\n              <input\r\n                type=\"tel\"\r\n                formControlName=\"phoneNumber\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                [class.border-red-500]=\"isFieldInvalid('phoneNumber')\"\r\n                placeholder=\"Enter your phone number\">\r\n              <p *ngIf=\"getFieldError('phoneNumber')\" class=\"text-red-500 text-sm mt-1\">\r\n                {{ getFieldError('phoneNumber') }}\r\n              </p>\r\n            </div>\r\n\r\n            <!-- Department -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                Department\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                formControlName=\"department\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                placeholder=\"e.g., Computer Science, Marketing\">\r\n            </div>\r\n\r\n            <!-- Position -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                Position\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                formControlName=\"position\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                placeholder=\"e.g., Student, Professor, Developer\">\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Bio -->\r\n          <div class=\"mt-6\">\r\n            <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n              Bio\r\n            </label>\r\n            <textarea\r\n              formControlName=\"bio\"\r\n              rows=\"4\"\r\n              class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all resize-none\"\r\n              [class.border-red-500]=\"isFieldInvalid('bio')\"\r\n              placeholder=\"Tell us about yourself, your interests, and goals...\"></textarea>\r\n            <p *ngIf=\"getFieldError('bio')\" class=\"text-red-500 text-sm mt-1\">\r\n              {{ getFieldError('bio') }}\r\n            </p>\r\n          </div>\r\n\r\n          <!-- Address -->\r\n          <div class=\"mt-6\">\r\n            <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n              Address\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              formControlName=\"address\"\r\n              class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n              placeholder=\"Enter your address\">\r\n          </div>\r\n\r\n          <!-- Skills -->\r\n          <div class=\"mt-6\">\r\n            <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n              Skills\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              formControlName=\"skills\"\r\n              class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n              placeholder=\"e.g., JavaScript, Python, Project Management (comma separated)\">\r\n            <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-1\">\r\n              Separate skills with commas\r\n            </p>\r\n          </div>\r\n\r\n          <!-- Profile Picture Upload -->\r\n          <div class=\"mt-6\">\r\n            <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n              Profile Picture\r\n            </label>\r\n            <div class=\"flex items-center space-x-4\">\r\n              <div class=\"flex-shrink-0\">\r\n                <img\r\n                  [src]=\"previewUrl || getProfileImageUrl()\"\r\n                  alt=\"Profile preview\"\r\n                  class=\"w-20 h-20 rounded-full object-cover border-2 border-[#4f5fad] dark:border-[#6d78c9]\">\r\n              </div>\r\n              <div class=\"flex-1\">\r\n                <input\r\n                  type=\"file\"\r\n                  (change)=\"onFileSelected($event)\"\r\n                  accept=\"image/*\"\r\n                  class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\">\r\n                <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-1\">\r\n                  Upload a new profile picture (optional)\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Action Buttons -->\r\n          <div class=\"flex justify-end space-x-4 mt-8 pt-6 border-t border-[#edf1f4] dark:border-[#2a2a2a]\">\r\n            <button\r\n              type=\"button\"\r\n              (click)=\"cancelEdit()\"\r\n              class=\"px-6 py-3 text-[#6d6870] dark:text-[#a0a0a0] border border-[#6d6870] dark:border-[#a0a0a0] rounded-lg hover:bg-[#6d6870] hover:text-white dark:hover:bg-[#a0a0a0] dark:hover:text-black transition-all\">\r\n              Cancel\r\n            </button>\r\n\r\n            <button\r\n              type=\"submit\"\r\n              [disabled]=\"editLoading\"\r\n              class=\"px-8 py-3 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all disabled:opacity-50 disabled:cursor-not-allowed\">\r\n              <span *ngIf=\"!editLoading\">Save Changes</span>\r\n              <span *ngIf=\"editLoading\" class=\"flex items-center\">\r\n                <svg class=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                  <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\r\n                  <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                </svg>\r\n                Saving...\r\n              </span>\r\n            </button>\r\n          </div>\r\n\r\n          <!-- Messages -->\r\n          <div *ngIf=\"message\" class=\"mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\">\r\n            <p class=\"text-green-800 dark:text-green-200\">{{ message }}</p>\r\n          </div>\r\n\r\n          <div *ngIf=\"error\" class=\"mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\">\r\n            <p class=\"text-red-800 dark:text-red-200\">{{ error }}</p>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,QAAQ,QAAQ,gBAAgB;;;;;;;;;;IC0CrCC,EAAA,CAAAC,cAAA,cAAkE;IAE9DD,EAAA,CAAAE,SAAA,cAEO;IAKTF,EAAA,CAAAG,YAAA,EAAM;;;;;IAIRH,EAAA,CAAAC,cAAA,cAGC;IAGKD,EAAA,CAAAE,SAAA,YAA2C;IAK7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,UAAK;IAEDD,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsD;IAAAD,EAAA,CAAAI,MAAA,GAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAAfH,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAMvER,EAAA,CAAAC,cAAA,cAGC;IAGKD,EAAA,CAAAE,SAAA,YAAmC;IAKrCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,UAAK;IAEDD,EAAA,CAAAI,MAAA,oBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsD;IACpDD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAC,MAAA,CAAAC,OAAA,MACF;;;;;;IAkCFX,EAAA,CAAAC,cAAA,cAAmD;IAE/CD,EAAA,CAAAY,UAAA,mBAAAC,gEAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAF,MAAA,CAAAG,cAAA,EAAgB;IAAA,EAAC;IAE1BnB,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;IAjCfH,EAAA,CAAAC,cAAA,aAA+B;IAG3BD,EAAA,CAAAE,SAAA,cAAwI;IAExIF,EAAA,CAAAC,cAAA,cAAoD;IAEhDD,EAAA,CAAAI,MAAA,2BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAAoE;IAClED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAE,SAAA,cAIM;IACRF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,aAAsD;IACpDD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGJH,EAAA,CAAAoB,UAAA,KAAAC,uCAAA,kBAMM;IACRrB,EAAA,CAAAG,YAAA,EAAM;;;;IA1B+BH,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAsB,WAAA,UAAAC,MAAA,CAAAC,gBAAA,GAAkC;IACjExB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAc,MAAA,CAAAE,kBAAA,OACF;IAOEzB,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAsB,WAAA,UAAAC,MAAA,CAAAE,kBAAA,MAAoC,qBAAAF,MAAA,CAAAC,gBAAA;IAMtCxB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAc,MAAA,CAAAG,sBAAA,QACF;IAGM1B,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA2B,UAAA,SAAAJ,MAAA,CAAAE,kBAAA,OAA8B;;;;;IAsD5BzB,EAAA,CAAAE,SAAA,YAAyD;;;;;IACzDF,EAAA,CAAAE,SAAA,YAAiE;;;;;;IANnEF,EAAA,CAAAC,cAAA,iBAI0K;IAFxKD,EAAA,CAAAY,UAAA,mBAAAgB,mEAAA;MAAA5B,EAAA,CAAAc,aAAA,CAAAe,IAAA;MAAA,MAAAC,OAAA,GAAA9B,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAY,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAGpB/B,EAAA,CAAAoB,UAAA,IAAAY,8CAAA,gBAAyD;IACzDhC,EAAA,CAAAoB,UAAA,IAAAa,8CAAA,gBAAiE;IACjEjC,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IALPH,EAAA,CAAA2B,UAAA,aAAAO,MAAA,CAAAC,aAAA,CAA0B;IAEtBnC,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAA2B,UAAA,UAAAO,MAAA,CAAAC,aAAA,CAAoB;IACpBnC,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAA2B,UAAA,SAAAO,MAAA,CAAAC,aAAA,CAAmB;IACvBnC,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAyB,MAAA,CAAAC,aAAA,kCACF;;;;;IAOEnC,EAAA,CAAAE,SAAA,YAAwD;;;;;IACxDF,EAAA,CAAAE,SAAA,YAAiE;;;;;;IANnEF,EAAA,CAAAC,cAAA,iBAIiH;IAF/GD,EAAA,CAAAY,UAAA,mBAAAwB,mEAAA;MAAApC,EAAA,CAAAc,aAAA,CAAAuB,IAAA;MAAA,MAAAC,OAAA,GAAAtC,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAoB,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAG9BvC,EAAA,CAAAoB,UAAA,IAAAoB,8CAAA,gBAAwD;IACxDxC,EAAA,CAAAoB,UAAA,IAAAqB,8CAAA,gBAAiE;IACjEzC,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IALPH,EAAA,CAAA2B,UAAA,aAAAe,OAAA,CAAAC,aAAA,CAA0B;IAEtB3C,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAA2B,UAAA,UAAAe,OAAA,CAAAC,aAAA,CAAoB;IACpB3C,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAA2B,UAAA,SAAAe,OAAA,CAAAC,aAAA,CAAmB;IACvB3C,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAiC,OAAA,CAAAC,aAAA,iCACF;;;;;IAcA3C,EAAA,CAAAC,cAAA,eAA6I;IAC3ID,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAmC,OAAA,CAAAC,IAAA,CAAAC,UAAA,MACF;;;;;IACA9C,EAAA,CAAAC,cAAA,eAA2I;IACzID,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAsC,OAAA,CAAAF,IAAA,CAAAG,QAAA,MACF;;;;;IAIFhD,EAAA,CAAAC,cAAA,YAA4F;IAC1FD,EAAA,CAAAI,MAAA,GACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAwC,OAAA,CAAAJ,IAAA,CAAAK,GAAA,CAAAC,MAAA,SAAAnD,EAAA,CAAAoD,WAAA,OAAAH,OAAA,CAAAJ,IAAA,CAAAK,GAAA,oBAAAD,OAAA,CAAAJ,IAAA,CAAAK,GAAA,MACF;;;;;IAoCAlD,EAAA,CAAAC,cAAA,eACsE;IACpED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAA4C,OAAA,CAAA5B,kBAAA,OACF;;;;;;IAfFzB,EAAA,CAAAC,cAAA,iBASsJ;IAPpJD,EAAA,CAAAY,UAAA,mBAAA0C,mEAAA;MAAA,MAAAC,WAAA,GAAAvD,EAAA,CAAAc,aAAA,CAAA0C,IAAA;MAAA,MAAAC,OAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA3D,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAAyC,OAAA,CAAAC,SAAA,GAAAH,OAAA,CAAAI,EAAA;IAAA,EAA4B;IAQ5B7D,EAAA,CAAAE,SAAA,QAA0B;IAC1BF,EAAA,CAAAI,MAAA,GACA;IAAAJ,EAAA,CAAAoB,UAAA,IAAA0C,iDAAA,mBAGO;IACT9D,EAAA,CAAAG,YAAA,EAAS;;;;;IAbPH,EAAA,CAAA+D,WAAA,qBAAAC,OAAA,CAAAJ,SAAA,KAAAH,OAAA,CAAAI,EAAA,CAA+C,mBAAAG,OAAA,CAAAJ,SAAA,KAAAH,OAAA,CAAAI,EAAA,kBAAAG,OAAA,CAAAJ,SAAA,KAAAH,OAAA,CAAAI,EAAA,gBAAAG,OAAA,CAAAJ,SAAA,KAAAH,OAAA,CAAAI,EAAA,oBAAAG,OAAA,CAAAJ,SAAA,KAAAH,OAAA,CAAAI,EAAA,yBAAAG,OAAA,CAAAJ,SAAA,KAAAH,OAAA,CAAAI,EAAA;IAO5C7D,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAiE,UAAA,CAAAR,OAAA,CAAAS,IAAA,CAAkB;IACrBlE,EAAA,CAAAK,SAAA,GACA;IADAL,EAAA,CAAAS,kBAAA,MAAAgD,OAAA,CAAAU,KAAA,MACA;IAAOnE,EAAA,CAAAK,SAAA,GAAyD;IAAzDL,EAAA,CAAA2B,UAAA,SAAA8B,OAAA,CAAAI,EAAA,qBAAAG,OAAA,CAAAvC,kBAAA,OAAyD;;;;;;IAUlEzB,EAAA,CAAAC,cAAA,cAAwD;IAGlDD,EAAA,CAAAE,SAAA,YAAmE;IACnEF,EAAA,CAAAI,MAAA,6BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,iBAE+K;IAD7KD,EAAA,CAAAY,UAAA,mBAAAwD,gEAAA;MAAApE,EAAA,CAAAc,aAAA,CAAAuD,IAAA;MAAA,MAAAC,OAAA,GAAAtE,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAoD,OAAA,CAAAC,cAAA,CAAe,UAAU,CAAC;IAAA,EAAC;IAEpCvE,EAAA,CAAAE,SAAA,YAAgC;IAChCF,EAAA,CAAAI,MAAA,aACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAGXH,EAAA,CAAAC,cAAA,cAAmD;IAGkCD,EAAA,CAAAI,MAAA,kBAAU;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACnGH,EAAA,CAAAC,cAAA,aAA0D;IAAAD,EAAA,CAAAI,MAAA,IAAsC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAItGH,EAAA,CAAAC,cAAA,eAA2D;IACwBD,EAAA,CAAAI,MAAA,iBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAClGH,EAAA,CAAAC,cAAA,aAA0D;IAAAD,EAAA,CAAAI,MAAA,IAAqC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAIrGH,EAAA,CAAAC,cAAA,eAA2D;IACwBD,EAAA,CAAAI,MAAA,iBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAClGH,EAAA,CAAAC,cAAA,aAA0D;IAAAD,EAAA,CAAAI,MAAA,IAAqC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAIrGH,EAAA,CAAAC,cAAA,eAA2D;IACwBD,EAAA,CAAAI,MAAA,qBAAa;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACtGH,EAAA,CAAAC,cAAA,aAA0D;IACxDD,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAINH,EAAA,CAAAC,cAAA,eAA2D;IACwBD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACrGH,EAAA,CAAAC,cAAA,aAA0D;IAAAD,EAAA,CAAAI,MAAA,IAAwC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAIxGH,EAAA,CAAAC,cAAA,eAA2D;IACwBD,EAAA,CAAAI,MAAA,eAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAChGH,EAAA,CAAAC,cAAA,aAA0D;IAAAD,EAAA,CAAAI,MAAA,IAAoC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAKtGH,EAAA,CAAAC,cAAA,eAA2D;IACwBD,EAAA,CAAAI,MAAA,WAAG;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAC5FH,EAAA,CAAAC,cAAA,aAA8D;IAC5DD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAzCwDH,EAAA,CAAAK,SAAA,IAAsC;IAAtCL,EAAA,CAAAM,iBAAA,CAAAkE,OAAA,CAAA3B,IAAA,CAAA4B,SAAA,mBAAsC;IAMtCzE,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAM,iBAAA,CAAAkE,OAAA,CAAA3B,IAAA,CAAA6B,QAAA,mBAAqC;IAMrC1E,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAM,iBAAA,CAAAkE,OAAA,CAAA3B,IAAA,CAAA8B,QAAA,mBAAqC;IAO7F3E,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAA+D,OAAA,CAAA3B,IAAA,CAAA+B,WAAA,GAAA5E,EAAA,CAAA6E,WAAA,QAAAL,OAAA,CAAA3B,IAAA,CAAA+B,WAAA,sCACF;IAM0D5E,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAM,iBAAA,CAAAkE,OAAA,CAAA3B,IAAA,CAAAiC,WAAA,mBAAwC;IAMxC9E,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAM,iBAAA,CAAAkE,OAAA,CAAA3B,IAAA,CAAAkC,OAAA,mBAAoC;IAQ9F/E,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAA+D,OAAA,CAAA3B,IAAA,CAAAK,GAAA,gEACF;;;;;IAiDElD,EAAA,CAAAC,cAAA,gBACyK;IACvKD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAuE,SAAA,MACF;;;;;IAJFhF,EAAA,CAAAC,cAAA,eAA+F;IAC7FD,EAAA,CAAAoB,UAAA,IAAA6D,qDAAA,oBAGO;IACTjF,EAAA,CAAAG,YAAA,EAAM;;;;IAJoBH,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAA2B,UAAA,YAAAuD,OAAA,CAAArC,IAAA,CAAAsC,MAAA,CAAc;;;;;IAMtCnF,EAAA,CAAAC,cAAA,aAAqD;IAAAD,EAAA,CAAAI,MAAA,0BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IAlDlFH,EAAA,CAAAC,cAAA,cAA4D;IAGtDD,EAAA,CAAAE,SAAA,YAAwE;IACxEF,EAAA,CAAAI,MAAA,iCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,iBAE+K;IAD7KD,EAAA,CAAAY,UAAA,mBAAAwE,gEAAA;MAAApF,EAAA,CAAAc,aAAA,CAAAuE,IAAA;MAAA,MAAAC,OAAA,GAAAtF,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAoE,OAAA,CAAAf,cAAA,CAAe,cAAc,CAAC;IAAA,EAAC;IAExCvE,EAAA,CAAAE,SAAA,YAAgC;IAChCF,EAAA,CAAAI,MAAA,aACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAGXH,EAAA,CAAAC,cAAA,cAAmD;IAGkCD,EAAA,CAAAI,MAAA,kBAAU;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACnGH,EAAA,CAAAC,cAAA,aAA0D;IAAAD,EAAA,CAAAI,MAAA,IAAwC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAIxGH,EAAA,CAAAC,cAAA,eAA2D;IACwBD,EAAA,CAAAI,MAAA,gBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACjGH,EAAA,CAAAC,cAAA,aAA0D;IAAAD,EAAA,CAAAI,MAAA,IAAsC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAItGH,EAAA,CAAAC,cAAA,eAA2D;IACwBD,EAAA,CAAAI,MAAA,YAAI;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAC7FH,EAAA,CAAAC,cAAA,aAA0D;IAAAD,EAAA,CAAAI,MAAA,IAA2B;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAI3FH,EAAA,CAAAC,cAAA,eAA2D;IACwBD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACrGH,EAAA,CAAAC,cAAA,aAA0D;IAAAD,EAAA,CAAAI,MAAA,IAAwC;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAK1GH,EAAA,CAAAC,cAAA,eAA2D;IACwBD,EAAA,CAAAI,MAAA,0BAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAC3GH,EAAA,CAAAoB,UAAA,KAAAmE,8CAAA,kBAKM;IACNvF,EAAA,CAAAoB,UAAA,KAAAoE,sDAAA,kCAAAxF,EAAA,CAAAyF,sBAAA,CAEc;IAChBzF,EAAA,CAAAG,YAAA,EAAM;;;;;IAlCwDH,EAAA,CAAAK,SAAA,IAAwC;IAAxCL,EAAA,CAAAM,iBAAA,CAAAoF,OAAA,CAAA7C,IAAA,CAAAC,UAAA,oBAAwC;IAMxC9C,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAAM,iBAAA,CAAAoF,OAAA,CAAA7C,IAAA,CAAAG,QAAA,oBAAsC;IAMtChD,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAA2F,WAAA,QAAAD,OAAA,CAAA7C,IAAA,CAAA+C,IAAA,EAA2B;IAM3B5F,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAA6E,WAAA,QAAAa,OAAA,CAAA7C,IAAA,CAAAgD,SAAA,gBAAwC;IAO9F7F,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAA2B,UAAA,SAAA+D,OAAA,CAAA7C,IAAA,CAAAsC,MAAA,IAAAO,OAAA,CAAA7C,IAAA,CAAAsC,MAAA,CAAAhC,MAAA,KAA6C,aAAA2C,IAAA;;;;;;IAavD9F,EAAA,CAAAC,cAAA,cAAuD;IAGjDD,EAAA,CAAAE,SAAA,aAAkE;IAClEF,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGPH,EAAA,CAAAC,cAAA,cAAmD;IAGkCD,EAAA,CAAAI,MAAA,oBAAa;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACtGH,EAAA,CAAAC,cAAA,YAA0D;IAAAD,EAAA,CAAAI,MAAA,IAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAC9EH,EAAA,CAAAC,cAAA,cAA2D;IAAAD,EAAA,CAAAI,MAAA,6BAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAItFH,EAAA,CAAAC,cAAA,eAA2D;IACwBD,EAAA,CAAAI,MAAA,sBAAc;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACvGH,EAAA,CAAAC,cAAA,gBAAqC;IAEjCD,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAA6D;IAAAD,EAAA,CAAAI,MAAA,IAA2B;;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAMrGH,EAAA,CAAAC,cAAA,gBAA2D;IACeD,EAAA,CAAAI,MAAA,uBAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC5FH,EAAA,CAAAC,cAAA,gBAAkC;IAK9BD,EAAA,CAAAE,SAAA,cAA+B;IAC/BF,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGJH,EAAA,CAAAC,cAAA,mBAEuK;IADrKD,EAAA,CAAAY,UAAA,mBAAAmF,iEAAA;MAAA/F,EAAA,CAAAc,aAAA,CAAAkF,IAAA;MAAA,MAAAC,OAAA,GAAAjG,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAA+E,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAElBlG,EAAA,CAAAE,SAAA,cAAwC;IACxCF,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,cAEyL;IACvLD,EAAA,CAAAE,SAAA,aAAgC;IAChCF,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IA1CsDH,EAAA,CAAAK,SAAA,IAAgB;IAAhBL,EAAA,CAAAM,iBAAA,CAAA6F,OAAA,CAAAtD,IAAA,CAAAuD,KAAA,CAAgB;IAWXpG,EAAA,CAAAK,SAAA,IAA2B;IAA3BL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAA2F,WAAA,QAAAQ,OAAA,CAAAtD,IAAA,CAAA+C,IAAA,EAA2B;IA2BxF5F,EAAA,CAAAK,SAAA,IAAmE;IAAnEL,EAAA,CAAA2B,UAAA,eAAAwE,OAAA,CAAAtD,IAAA,CAAA+C,IAAA,4CAAmE;;;;;IAkErE5F,EAAA,CAAAC,cAAA,eACyF;IACtCD,EAAA,CAAAI,MAAA,GAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACzEH,EAAA,CAAAC,cAAA,gBAAsH;IACpHD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAH0CH,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAM,iBAAA,CAAA+F,SAAA,CAAAlC,KAAA,CAAiB;IAEhEnE,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAA4F,SAAA,CAAAC,QAAA,gCACF;;;;;;IAXNtG,EAAA,CAAAC,cAAA,eAAqJ;IAEjJD,EAAA,CAAAE,SAAA,aAAgD;IAChDF,EAAA,CAAAI,MAAA,4BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAAmD;IACjDD,EAAA,CAAAoB,UAAA,IAAAmF,oDAAA,mBAMM;IACRvG,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,kBAEgG;IAD9FD,EAAA,CAAAY,UAAA,mBAAA4F,uEAAA;MAAAxG,EAAA,CAAAc,aAAA,CAAA2F,IAAA;MAAA,MAAAC,OAAA,GAAA1G,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAAwF,OAAA,CAAA9C,SAAA,GAAqB,UAAU;IAAA,EAAC;IAEhC5D,EAAA,CAAAI,MAAA,gCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAZgBH,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAA2B,UAAA,YAAAgF,OAAA,CAAAC,gBAAA,GAAqB;;;;;IAxDlD5G,EAAA,CAAAC,cAAA,cAA0D;IAGpDD,EAAA,CAAAE,SAAA,aAAyE;IACzEF,EAAA,CAAAI,MAAA,2BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAAoE;IAClED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,eAA2D;IAEYD,EAAA,CAAAI,MAAA,wBAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACxFH,EAAA,CAAAC,cAAA,gBAAyD;IAAAD,EAAA,CAAAI,MAAA,IAA8B;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAIhGH,EAAA,CAAAC,cAAA,gBAAyF;IACvFD,EAAA,CAAAE,SAAA,eAIM;IACRF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,gBAAwD;IAEeD,EAAA,CAAAI,MAAA,IAAoC;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC7GH,EAAA,CAAAC,cAAA,eAAwD;IAAAD,EAAA,CAAAI,MAAA,uBAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC7EH,EAAA,CAAAC,cAAA,gBAA6D;IAAAD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAGjFH,EAAA,CAAAC,cAAA,gBAAmE;IACED,EAAA,CAAAI,MAAA,IAAoC;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC7GH,EAAA,CAAAC,cAAA,eAAwD;IAAAD,EAAA,CAAAI,MAAA,uBAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC7EH,EAAA,CAAAC,cAAA,gBAA6D;IAAAD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAGjFH,EAAA,CAAAC,cAAA,gBAAmE;IACED,EAAA,CAAAI,MAAA,IAAkC;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC3GH,EAAA,CAAAC,cAAA,eAAwD;IAAAD,EAAA,CAAAI,MAAA,qBAAa;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC3EH,EAAA,CAAAC,cAAA,gBAA6D;IAAAD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAMrFH,EAAA,CAAAoB,UAAA,KAAAyF,8CAAA,mBAmBM;IAGN7G,EAAA,CAAAC,cAAA,gBAAuG;IAEnGD,EAAA,CAAAE,SAAA,cAAqC;IACrCF,EAAA,CAAAI,MAAA,qCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAAuD;IAEnDD,EAAA,CAAAE,SAAA,cAA4D;IAC5DF,EAAA,CAAAI,MAAA,uEACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAE,SAAA,cAA4D;IAC5DF,EAAA,CAAAI,MAAA,wEACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAE,SAAA,cAA4D;IAC5DF,EAAA,CAAAI,MAAA,0EACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAE,SAAA,cAA4D;IAC5DF,EAAA,CAAAI,MAAA,kDACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;;;;IAvF0BH,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAsB,WAAA,UAAAwF,OAAA,CAAAtF,gBAAA,GAAkC;IACjExB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAqG,OAAA,CAAArF,kBAAA,OACF;IAO2DzB,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAM,iBAAA,CAAAwG,OAAA,CAAApF,sBAAA,GAA8B;IAOrF1B,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAsB,WAAA,UAAAwF,OAAA,CAAArF,kBAAA,MAAoC,qBAAAqF,OAAA,CAAAtF,gBAAA;IAQ+BxB,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAS,kBAAA,KAAAqG,OAAA,CAAAC,2BAAA,QAAoC;IAMpC/G,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAS,kBAAA,KAAAqG,OAAA,CAAAE,2BAAA,QAAoC;IAMpChH,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAS,kBAAA,KAAAqG,OAAA,CAAAG,eAAA,kBAAkC;IAQrGjH,EAAA,CAAAK,SAAA,GAAmC;IAAnCL,EAAA,CAAA2B,UAAA,SAAAmF,OAAA,CAAAF,gBAAA,GAAAzD,MAAA,KAAmC;;;;;;IArWjDnD,EAAA,CAAAC,cAAA,cAAoC;IAMhCD,EAAA,CAAAE,SAAA,cAEO;IAGPF,EAAA,CAAAC,cAAA,cAAyE;IAInED,EAAA,CAAAE,SAAA,cAIE;IAEFF,EAAA,CAAAC,cAAA,cAA4K;IAExKD,EAAA,CAAAE,SAAA,YAAgD;IAChDF,EAAA,CAAAI,MAAA,sBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAME;IAFAD,EAAA,CAAAY,UAAA,oBAAAsG,0DAAAC,MAAA;MAAAnH,EAAA,CAAAc,aAAA,CAAAsG,IAAA;MAAA,MAAAC,OAAA,GAAArH,EAAA,CAAAiB,aAAA;MAAA,OAAUjB,EAAA,CAAAkB,WAAA,CAAAmG,OAAA,CAAAC,cAAA,CAAAH,MAAA,CAAsB;IAAA,EAAC;IAJnCnH,EAAA,CAAAG,YAAA,EAME;IAKNH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAoB,UAAA,KAAAmG,0CAAA,qBAQS;IAETvH,EAAA,CAAAoB,UAAA,KAAAoG,0CAAA,qBAQS;IACXxH,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,eAA6C;IAEzCD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAmD;IAAAD,EAAA,CAAAI,MAAA,IAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACvEH,EAAA,CAAAC,cAAA,eAAuE;IAEnED,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAoB,UAAA,KAAAqG,wCAAA,mBAEO;IACPzH,EAAA,CAAAoB,UAAA,KAAAsG,wCAAA,mBAEO;IACT1H,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAoB,UAAA,KAAAuG,qCAAA,gBAEI;IAGJ3H,EAAA,CAAAC,cAAA,eAAkE;IAE9DD,EAAA,CAAAY,UAAA,mBAAAgH,0DAAA;MAAA5H,EAAA,CAAAc,aAAA,CAAAsG,IAAA;MAAA,MAAAS,OAAA,GAAA7H,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAA2G,OAAA,CAAA1G,cAAA,EAAgB;IAAA,EAAC;IAE1BnB,EAAA,CAAAE,SAAA,aAAgC;IAChCF,EAAA,CAAAI,MAAA,sBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,aAE2O;IACzOD,EAAA,CAAAE,SAAA,aAAgC;IAChCF,EAAA,CAAAI,MAAA,mBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAOZH,EAAA,CAAAC,cAAA,eAAuK;IAEnKD,EAAA,CAAAoB,UAAA,KAAA0G,0CAAA,sBAgBS;IACX9H,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAiB;IAEfD,EAAA,CAAAoB,UAAA,KAAA2G,uCAAA,oBA6DM;IAGN/H,EAAA,CAAAoB,UAAA,KAAA4G,uCAAA,oBAqDM;IAGNhI,EAAA,CAAAoB,UAAA,KAAA6G,uCAAA,mBAyDM;IAGNjI,EAAA,CAAAoB,UAAA,KAAA8G,uCAAA,oBAgGM;IACRlI,EAAA,CAAAG,YAAA,EAAM;;;;IApYEH,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA2B,UAAA,QAAAwG,MAAA,CAAAC,kBAAA,IAAApI,EAAA,CAAAqI,aAAA,CAA4B;IAuB3BrI,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAA2B,UAAA,SAAAwG,MAAA,CAAAG,aAAA,CAAmB;IAUnBtI,EAAA,CAAAK,SAAA,GAAoF;IAApFL,EAAA,CAAA2B,UAAA,SAAAwG,MAAA,CAAAtF,IAAA,CAAA0F,YAAA,IAAAJ,MAAA,CAAAtF,IAAA,CAAA0F,YAAA,yCAAoF;IAcvFvI,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAA0H,MAAA,CAAAtF,IAAA,CAAA8B,QAAA,IAAAwD,MAAA,CAAAtF,IAAA,CAAA4B,SAAA,SAAA0D,MAAA,CAAAtF,IAAA,CAAA6B,QAAA,MACF;IACmD1E,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAM,iBAAA,CAAA6H,MAAA,CAAAtF,IAAA,CAAAuD,KAAA,CAAgB;IAG/DpG,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAT,EAAA,CAAA2F,WAAA,SAAAwC,MAAA,CAAAtF,IAAA,CAAA+C,IAAA,OACF;IACO5F,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAA2B,UAAA,SAAAwG,MAAA,CAAAtF,IAAA,CAAAC,UAAA,CAAqB;IAGrB9C,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAA2B,UAAA,SAAAwG,MAAA,CAAAtF,IAAA,CAAAG,QAAA,CAAmB;IAMxBhD,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAA2B,UAAA,SAAAwG,MAAA,CAAAtF,IAAA,CAAAK,GAAA,CAAc;IAadlD,EAAA,CAAAK,SAAA,GAAmE;IAAnEL,EAAA,CAAA2B,UAAA,eAAAwG,MAAA,CAAAtF,IAAA,CAAA+C,IAAA,4CAAmE;IAcvD5F,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAA2B,UAAA,YAAAwG,MAAA,CAAAK,WAAA,CAAc;IAqB1BxI,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA2B,UAAA,SAAAwG,MAAA,CAAAvE,SAAA,gBAA8B;IAgE9B5D,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAA2B,UAAA,SAAAwG,MAAA,CAAAvE,SAAA,oBAAkC;IAwDlC5D,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAA2B,UAAA,SAAAwG,MAAA,CAAAvE,SAAA,eAA6B;IA4D7B5D,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAA2B,UAAA,SAAAwG,MAAA,CAAAvE,SAAA,kBAAgC;;;;;IAoIlC5D,EAAA,CAAAC,cAAA,aAAwE;IACtED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAgI,OAAA,CAAAC,aAAA,mBACF;;;;;IAcA1I,EAAA,CAAAC,cAAA,aAAuE;IACrED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAkI,OAAA,CAAAD,aAAA,kBACF;;;;;IAcA1I,EAAA,CAAAC,cAAA,aAAuE;IACrED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAmI,OAAA,CAAAF,aAAA,kBACF;;;;;IAcA1I,EAAA,CAAAC,cAAA,aAAoE;IAClED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAoI,OAAA,CAAAH,aAAA,eACF;;;;;IAyBA1I,EAAA,CAAAC,cAAA,aAA0E;IACxED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAqI,OAAA,CAAAJ,aAAA,qBACF;;;;;IAuCF1I,EAAA,CAAAC,cAAA,aAAkE;IAChED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAsI,OAAA,CAAAL,aAAA,aACF;;;;;IAoEE1I,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAI,MAAA,mBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAC9CH,EAAA,CAAAC,cAAA,gBAAoD;IAClDD,EAAA,CAAAgJ,cAAA,EAA2H;IAA3HhJ,EAAA,CAAAC,cAAA,eAA2H;IACzHD,EAAA,CAAAE,SAAA,kBAAkG;IAEpGF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,kBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAKXH,EAAA,CAAAC,cAAA,eAAgI;IAChFD,EAAA,CAAAI,MAAA,GAAa;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAAjBH,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAM,iBAAA,CAAA2I,OAAA,CAAAtI,OAAA,CAAa;;;;;IAG7DX,EAAA,CAAAC,cAAA,eAAsH;IAC1ED,EAAA,CAAAI,MAAA,GAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAAfH,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAAM,iBAAA,CAAA4I,OAAA,CAAA1I,KAAA,CAAW;;;;;;IAzO7DR,EAAA,CAAAC,cAAA,eAA+G;IAK7DD,EAAA,CAAAI,MAAA,mBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC3DH,EAAA,CAAAC,cAAA,kBAE2D;IADzDD,EAAA,CAAAY,UAAA,mBAAAuI,yDAAA;MAAAnJ,EAAA,CAAAc,aAAA,CAAAsI,IAAA;MAAA,MAAAC,OAAA,GAAArJ,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAmI,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAEtBtJ,EAAA,CAAAgJ,cAAA,EAA8G;IAA9GhJ,EAAA,CAAAC,cAAA,eAA8G;IAC5GD,EAAA,CAAAE,SAAA,gBAAiG;IACnGF,EAAA,CAAAG,YAAA,EAAM;IAMZH,EAAA,CAAAuJ,eAAA,EAAqE;IAArEvJ,EAAA,CAAAC,cAAA,gBAAqE;IAAxCD,EAAA,CAAAY,UAAA,sBAAA4I,0DAAA;MAAAxJ,EAAA,CAAAc,aAAA,CAAAsI,IAAA;MAAA,MAAAK,OAAA,GAAAzJ,EAAA,CAAAiB,aAAA;MAAA,OAAYjB,EAAA,CAAAkB,WAAA,CAAAuI,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IACtD1J,EAAA,CAAAC,cAAA,eAAmD;IAI7CD,EAAA,CAAAI,MAAA,sBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAKsC;IACtCF,EAAA,CAAAoB,UAAA,KAAAuI,qCAAA,iBAEI;IACN3J,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAKqC;IACrCF,EAAA,CAAAoB,UAAA,KAAAwI,qCAAA,iBAEI;IACN5J,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAKqC;IACrCF,EAAA,CAAAoB,UAAA,KAAAyI,qCAAA,iBAEI;IACN7J,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAKiC;IACjCF,EAAA,CAAAoB,UAAA,KAAA0I,qCAAA,iBAEI;IACN9J,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,uBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAGyS;IAC3SF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,sBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAKwC;IACxCF,EAAA,CAAAoB,UAAA,KAAA2I,qCAAA,iBAEI;IACN/J,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,oBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAIkD;IACpDF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,kBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAIoD;IACtDF,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,gBAAkB;IAEdD,EAAA,CAAAI,MAAA,aACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,qBAKgF;IAChFF,EAAA,CAAAoB,UAAA,KAAA4I,qCAAA,iBAEI;IACNhK,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,gBAAkB;IAEdD,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAImC;IACrCF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,gBAAkB;IAEdD,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAI+E;IAC/EF,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAI,MAAA,qCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAINH,EAAA,CAAAC,cAAA,gBAAkB;IAEdD,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAyC;IAErCD,EAAA,CAAAE,SAAA,gBAG8F;IAChGF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAoB;IAGhBD,EAAA,CAAAY,UAAA,oBAAAqJ,0DAAA9C,MAAA;MAAAnH,EAAA,CAAAc,aAAA,CAAAsI,IAAA;MAAA,MAAAc,OAAA,GAAAlK,EAAA,CAAAiB,aAAA;MAAA,OAAUjB,EAAA,CAAAkB,WAAA,CAAAgJ,OAAA,CAAA5C,cAAA,CAAAH,MAAA,CAAsB;IAAA,EAAC;IAFnCnH,EAAA,CAAAG,YAAA,EAIyS;IACzSH,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAI,MAAA,iDACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAMVH,EAAA,CAAAC,cAAA,gBAAkG;IAG9FD,EAAA,CAAAY,UAAA,mBAAAuJ,0DAAA;MAAAnK,EAAA,CAAAc,aAAA,CAAAsI,IAAA;MAAA,MAAAgB,OAAA,GAAApK,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAkJ,OAAA,CAAAd,UAAA,EAAY;IAAA,EAAC;IAEtBtJ,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,mBAG8L;IAC5LD,EAAA,CAAAoB,UAAA,KAAAiJ,wCAAA,oBAA8C;IAC9CrK,EAAA,CAAAoB,UAAA,KAAAkJ,wCAAA,oBAMO;IACTtK,EAAA,CAAAG,YAAA,EAAS;IAIXH,EAAA,CAAAoB,UAAA,KAAAmJ,uCAAA,mBAEM;IAENvK,EAAA,CAAAoB,UAAA,KAAAoJ,uCAAA,mBAEM;IACRxK,EAAA,CAAAG,YAAA,EAAO;;;;IA1NDH,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA2B,UAAA,cAAA8I,MAAA,CAAAC,QAAA,CAAsB;IAWpB1K,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAA+D,WAAA,mBAAA0G,MAAA,CAAAE,cAAA,cAAoD;IAElD3K,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAA2B,UAAA,SAAA8I,MAAA,CAAA/B,aAAA,cAAgC;IAclC1I,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAA+D,WAAA,mBAAA0G,MAAA,CAAAE,cAAA,aAAmD;IAEjD3K,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA2B,UAAA,SAAA8I,MAAA,CAAA/B,aAAA,aAA+B;IAcjC1I,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAA+D,WAAA,mBAAA0G,MAAA,CAAAE,cAAA,aAAmD;IAEjD3K,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA2B,UAAA,SAAA8I,MAAA,CAAA/B,aAAA,aAA+B;IAcjC1I,EAAA,CAAAK,SAAA,GAAgD;IAAhDL,EAAA,CAAA+D,WAAA,mBAAA0G,MAAA,CAAAE,cAAA,UAAgD;IAE9C3K,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA2B,UAAA,SAAA8I,MAAA,CAAA/B,aAAA,UAA4B;IAyB9B1I,EAAA,CAAAK,SAAA,GAAsD;IAAtDL,EAAA,CAAA+D,WAAA,mBAAA0G,MAAA,CAAAE,cAAA,gBAAsD;IAEpD3K,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAA2B,UAAA,SAAA8I,MAAA,CAAA/B,aAAA,gBAAkC;IAuCtC1I,EAAA,CAAAK,SAAA,IAA8C;IAA9CL,EAAA,CAAA+D,WAAA,mBAAA0G,MAAA,CAAAE,cAAA,QAA8C;IAE5C3K,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAA2B,UAAA,SAAA8I,MAAA,CAAA/B,aAAA,QAA0B;IAwCxB1I,EAAA,CAAAK,SAAA,IAA0C;IAA1CL,EAAA,CAAA2B,UAAA,QAAA8I,MAAA,CAAAG,UAAA,IAAAH,MAAA,CAAArC,kBAAA,IAAApI,EAAA,CAAAqI,aAAA,CAA0C;IA4B9CrI,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAA2B,UAAA,aAAA8I,MAAA,CAAAI,WAAA,CAAwB;IAEjB7K,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAA2B,UAAA,UAAA8I,MAAA,CAAAI,WAAA,CAAkB;IAClB7K,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAA2B,UAAA,SAAA8I,MAAA,CAAAI,WAAA,CAAiB;IAWtB7K,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAA2B,UAAA,SAAA8I,MAAA,CAAA9J,OAAA,CAAa;IAIbX,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAA2B,UAAA,SAAA8I,MAAA,CAAAjK,KAAA,CAAW;;;ADlwB3B,OAAM,MAAOsK,gBAAgB;EA0B3BC,YACUC,WAAwB,EACxBC,eAAgC,EAChCC,WAAwB,EACxBC,MAAc,EACdC,EAAe;IAJf,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,EAAE,GAAFA,EAAE;IA7BZ,KAAA9C,aAAa,GAAgB,IAAI;IACjC,KAAAsC,UAAU,GAAkB,IAAI;IAChC,KAAAjK,OAAO,GAAG,EAAE;IACZ,KAAAH,KAAK,GAAG,EAAE;IACV,KAAA2B,aAAa,GAAG,KAAK;IACrB,KAAAQ,aAAa,GAAG,KAAK;IAErB;IACA,KAAA0I,UAAU,GAAG,KAAK;IAElB,KAAAR,WAAW,GAAG,KAAK;IACnB,KAAApJ,kBAAkB,GAAG,CAAC;IAEtB;IACA,KAAAmC,SAAS,GAAG,UAAU;IACtB,KAAA0H,WAAW,GAAG,EAAE;IAEhB,KAAA9C,WAAW,GAAG,CACZ;MAAE3E,EAAE,EAAE,UAAU;MAAEM,KAAK,EAAE,eAAe;MAAED,IAAI,EAAE;IAAa,CAAE,EAC/D;MAAEL,EAAE,EAAE,cAAc;MAAEM,KAAK,EAAE,cAAc;MAAED,IAAI,EAAE;IAAkB,CAAE,EACvE;MAAEL,EAAE,EAAE,SAAS;MAAEM,KAAK,EAAE,SAAS;MAAED,IAAI,EAAE;IAAY,CAAE,EACvD;MAAEL,EAAE,EAAE,YAAY;MAAEM,KAAK,EAAE,YAAY;MAAED,IAAI,EAAE;IAAmB,CAAE,CACrE;IASC,IAAI,CAACwG,QAAQ,GAAG,IAAI,CAACU,EAAE,CAACG,KAAK,CAAC;MAC5B9G,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC3E,UAAU,CAACwG,QAAQ,EAAExG,UAAU,CAAC0L,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/D9G,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC5E,UAAU,CAACwG,QAAQ,EAAExG,UAAU,CAAC0L,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9D7G,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC7E,UAAU,CAACwG,QAAQ,EAAExG,UAAU,CAAC0L,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DpF,KAAK,EAAE,CAAC,EAAE,EAAE,CAACtG,UAAU,CAACwG,QAAQ,EAAExG,UAAU,CAACsG,KAAK,CAAC,CAAC;MACpDxB,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBE,WAAW,EAAE,CAAC,EAAE,EAAE,CAAChF,UAAU,CAAC2L,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC;MAC1D3I,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBE,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdE,GAAG,EAAE,CAAC,EAAE,EAAE,CAACpD,UAAU,CAAC0L,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACrCzG,OAAO,EAAE,CAAC,EAAE,CAAC;MACbI,MAAM,EAAE,CAAC,EAAE;KACZ,CAAC;EACJ;EAEAuG,QAAQA,CAAA;IACN;IACA,IAAI,CAACR,WAAW,CAACS,UAAU,EAAE,CAACC,SAAS,CAAC;MACtCC,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAI,CAACjJ,IAAI,GAAGiJ,GAAG;QAEf;QACA,IAAI,CAAC,IAAI,CAACjJ,IAAI,CAAC0F,YAAY,IAAI,IAAI,CAAC1F,IAAI,CAACkJ,KAAK,EAAE;UAC9C,IAAI,CAAClJ,IAAI,CAAC0F,YAAY,GAAG,IAAI,CAAC1F,IAAI,CAACkJ,KAAK;SACzC,MAAM,IAAI,CAAC,IAAI,CAAClJ,IAAI,CAACkJ,KAAK,IAAI,IAAI,CAAClJ,IAAI,CAAC0F,YAAY,EAAE;UACrD,IAAI,CAAC1F,IAAI,CAACkJ,KAAK,GAAG,IAAI,CAAClJ,IAAI,CAAC0F,YAAY;;QAG1C;QACA,IACE,CAAC,IAAI,CAAC1F,IAAI,CAAC0F,YAAY,IACvB,IAAI,CAAC1F,IAAI,CAAC0F,YAAY,KAAK,MAAM,IACjC,IAAI,CAAC1F,IAAI,CAAC0F,YAAY,CAACyD,IAAI,EAAE,KAAK,EAAE,EACpC;UACA,IAAI,CAACnJ,IAAI,CAAC0F,YAAY,GAAG,mCAAmC;UAC5D,IAAI,CAAC1F,IAAI,CAACkJ,KAAK,GAAG,mCAAmC;;QAGvD;QACA,IAAI,CAAC,IAAI,CAAClJ,IAAI,CAACoJ,eAAe,EAAE;UAC9B,IAAI,CAACpJ,IAAI,CAACoJ,eAAe,GAAG,IAAI,CAACpJ,IAAI,CAAC0F,YAAY,IAAI,IAAI,CAAC1F,IAAI,CAACkJ,KAAK;;QAGvE;QACA,IAAI,CAACG,0BAA0B,EAAE;QAEjC;QACA,IAAI,CAACC,gBAAgB,EAAE;MACzB,CAAC;MACD3L,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACA,KAAK,GAAG,yBAAyB;MACxC;KACD,CAAC;EACJ;EAEA0L,0BAA0BA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACrJ,IAAI,EAAE;IAEhB,MAAMuJ,cAAc,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,KAAK,CAAC;IACxH,MAAMC,cAAc,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC;IAExD,IAAIC,iBAAiB,GAAG,CAAC;IACzB,IAAIC,iBAAiB,GAAG,CAAC;IAEzB;IACAH,cAAc,CAACI,OAAO,CAACC,KAAK,IAAG;MAC7B,MAAMC,KAAK,GAAG,IAAI,CAAC7J,IAAI,CAAC4J,KAAK,CAAC;MAC9B,IAAIC,KAAK,IAAIA,KAAK,CAACC,QAAQ,EAAE,CAACX,IAAI,EAAE,KAAK,EAAE,IAAIU,KAAK,KAAK,qBAAqB,EAAE;QAC9EJ,iBAAiB,EAAE;;IAEvB,CAAC,CAAC;IAEF;IACAD,cAAc,CAACG,OAAO,CAACC,KAAK,IAAG;MAC7B,MAAMC,KAAK,GAAG,IAAI,CAAC7J,IAAI,CAAC4J,KAAK,CAAC;MAC9B,IAAIC,KAAK,IAAIA,KAAK,CAACC,QAAQ,EAAE,CAACX,IAAI,EAAE,KAAK,EAAE,EAAE;QAC3CO,iBAAiB,EAAE;;IAEvB,CAAC,CAAC;IAEF;IACA,IAAItF,eAAe,GAAG,CAAC;IACvB,IAAI,IAAI,CAACpE,IAAI,CAAC0F,YAAY,IACtB,IAAI,CAAC1F,IAAI,CAAC0F,YAAY,KAAK,qBAAqB,IAChD,IAAI,CAAC1F,IAAI,CAAC0F,YAAY,KAAK,mCAAmC,IAC9D,IAAI,CAAC1F,IAAI,CAAC0F,YAAY,CAACyD,IAAI,EAAE,KAAK,EAAE,EAAE;MACxC/E,eAAe,GAAG,CAAC;;IAGrB;IACA,MAAM2F,kBAAkB,GAAIN,iBAAiB,GAAGF,cAAc,CAACjJ,MAAM,GAAI,EAAE;IAC3E,MAAM0J,kBAAkB,GAAIN,iBAAiB,GAAGF,cAAc,CAAClJ,MAAM,GAAI,EAAE;IAC3E,MAAM2J,eAAe,GAAG7F,eAAe,GAAG,EAAE;IAE5C,IAAI,CAACxF,kBAAkB,GAAGsL,IAAI,CAACC,KAAK,CAACJ,kBAAkB,GAAGC,kBAAkB,GAAGC,eAAe,CAAC;EACjG;EAEAX,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACtJ,IAAI,EAAE;IAEhB,IAAI,CAAC6H,QAAQ,CAACuC,UAAU,CAAC;MACvBxI,SAAS,EAAE,IAAI,CAAC5B,IAAI,CAAC4B,SAAS,IAAI,EAAE;MACpCC,QAAQ,EAAE,IAAI,CAAC7B,IAAI,CAAC6B,QAAQ,IAAI,EAAE;MAClCC,QAAQ,EAAE,IAAI,CAAC9B,IAAI,CAAC8B,QAAQ,IAAI,EAAE;MAClCyB,KAAK,EAAE,IAAI,CAACvD,IAAI,CAACuD,KAAK,IAAI,EAAE;MAC5BxB,WAAW,EAAE,IAAI,CAAC/B,IAAI,CAAC+B,WAAW,IAAI,EAAE;MACxCE,WAAW,EAAE,IAAI,CAACjC,IAAI,CAACiC,WAAW,IAAI,EAAE;MACxChC,UAAU,EAAE,IAAI,CAACD,IAAI,CAACC,UAAU,IAAI,EAAE;MACtCE,QAAQ,EAAE,IAAI,CAACH,IAAI,CAACG,QAAQ,IAAI,EAAE;MAClCE,GAAG,EAAE,IAAI,CAACL,IAAI,CAACK,GAAG,IAAI,EAAE;MACxB6B,OAAO,EAAE,IAAI,CAAClC,IAAI,CAACkC,OAAO,IAAI,EAAE;MAChCI,MAAM,EAAE+H,KAAK,CAACC,OAAO,CAAC,IAAI,CAACtK,IAAI,CAACsC,MAAM,CAAC,GAAG,IAAI,CAACtC,IAAI,CAACsC,MAAM,CAACiI,IAAI,CAAC,IAAI,CAAC,GAAI,IAAI,CAACvK,IAAI,CAACsC,MAAM,IAAI;KAC9F,CAAC;EACJ;EAEA;;;;EAIAiD,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACvF,IAAI,EAAE,OAAO,mCAAmC;IAE1D;IACA,IACE,IAAI,CAACA,IAAI,CAAC0F,YAAY,IACtB,IAAI,CAAC1F,IAAI,CAAC0F,YAAY,KAAK,MAAM,IACjC,IAAI,CAAC1F,IAAI,CAAC0F,YAAY,CAACyD,IAAI,EAAE,KAAK,EAAE,EACpC;MACA,OAAO,IAAI,CAACnJ,IAAI,CAAC0F,YAAY;;IAG/B;IACA,IACE,IAAI,CAAC1F,IAAI,CAACkJ,KAAK,IACf,IAAI,CAAClJ,IAAI,CAACkJ,KAAK,KAAK,MAAM,IAC1B,IAAI,CAAClJ,IAAI,CAACkJ,KAAK,CAACC,IAAI,EAAE,KAAK,EAAE,EAC7B;MACA,OAAO,IAAI,CAACnJ,IAAI,CAACkJ,KAAK;;IAGxB;IACA,IACE,IAAI,CAAClJ,IAAI,CAACoJ,eAAe,IACzB,IAAI,CAACpJ,IAAI,CAACoJ,eAAe,KAAK,MAAM,IACpC,IAAI,CAACpJ,IAAI,CAACoJ,eAAe,CAACD,IAAI,EAAE,KAAK,EAAE,EACvC;MACA,OAAO,IAAI,CAACnJ,IAAI,CAACoJ,eAAe;;IAGlC;IACA,OAAO,mCAAmC;EAC5C;EAEA3E,cAAcA,CAAC+F,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,EAAErK,MAAM,EAAE;MACvB,MAAMsK,IAAI,GAAGH,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAE3B,MAAME,UAAU,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC;MAC5D,IAAI,CAACA,UAAU,CAACC,QAAQ,CAACF,IAAI,CAACG,IAAI,CAAC,EAAE;QACnC,IAAI,CAACpN,KAAK,GAAG,4CAA4C;QACzD,IAAI,CAACqN,cAAc,EAAE;QACrB;;MAGF,IAAIJ,IAAI,CAACK,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/B,IAAI,CAACtN,KAAK,GAAG,kCAAkC;QAC/C,IAAI,CAACqN,cAAc,EAAE;QACrB;;MAGF,IAAI,CAACvF,aAAa,GAAGmF,IAAI;MACzB,IAAI,CAACjN,KAAK,GAAG,EAAE;MAEf,MAAMuN,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAI;QACpB,IAAI,CAACtD,UAAU,GAAIsD,CAAC,CAACX,MAAM,EAAEY,MAAiB,IAAI,IAAI;MACxD,CAAC;MACDJ,MAAM,CAACK,aAAa,CAACX,IAAI,CAAC;;EAE9B;EAEA1L,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACuG,aAAa,EAAE;IAEzB,IAAI,CAACnG,aAAa,GAAG,IAAI,CAAC,CAAC;IAC3B,IAAI,CAACxB,OAAO,GAAG,EAAE;IACjB,IAAI,CAACH,KAAK,GAAG,EAAE;IAEf6N,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAACnM,aAAa,CAAC;IAEjE,IAAI,CAAC+I,WAAW,CACbqD,kBAAkB,CAAC,IAAI,CAACjG,aAAa,CAAC,CACtCkG,IAAI,CACHzO,QAAQ,CAAC,MAAK;MACZ,IAAI,CAACoC,aAAa,GAAG,KAAK;MAC1BkM,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACnM,aAAa,CAAC;IACpE,CAAC,CAAC,CACH,CACAyJ,SAAS,CAAC;MACTC,IAAI,EAAG4C,QAAa,IAAI;QACtB,IAAI,CAAC9N,OAAO,GAAG8N,QAAQ,CAAC9N,OAAO,IAAI,8BAA8B;QAEjE;QACA,IAAI,CAACkC,IAAI,CAACoJ,eAAe,GAAGwC,QAAQ,CAACC,QAAQ;QAC7C,IAAI,CAAC7L,IAAI,CAAC0F,YAAY,GAAGkG,QAAQ,CAACC,QAAQ;QAC1C,IAAI,CAAC7L,IAAI,CAACkJ,KAAK,GAAG0C,QAAQ,CAACC,QAAQ;QAEnC;QACA,IAAI,CAACxD,WAAW,CAACyD,iBAAiB,CAAC;UACjCpG,YAAY,EAAEkG,QAAQ,CAACC,QAAQ;UAC/B3C,KAAK,EAAE0C,QAAQ,CAACC;SACjB,CAAC;QAEF;QACA,IAAI,CAACzD,eAAe,CAAC2D,cAAc,CAAC;UAClC,GAAG,IAAI,CAAC/L,IAAI;UACZ0F,YAAY,EAAEkG,QAAQ,CAACC,QAAQ;UAC/B3C,KAAK,EAAE0C,QAAQ,CAACC;SACjB,CAAC;QAEF,IAAI,CAACpG,aAAa,GAAG,IAAI;QACzB,IAAI,CAACsC,UAAU,GAAG,IAAI;QACtB,IAAI,CAACiD,cAAc,EAAE;QAErB,IAAIY,QAAQ,CAACI,KAAK,EAAE;UAClBC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEN,QAAQ,CAACI,KAAK,CAAC;;QAG/C;QACAG,UAAU,CAAC,MAAK;UACd,IAAI,CAACrO,OAAO,GAAG,EAAE;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDH,KAAK,EAAGyO,GAAmC,IAAI;QAC7C,IAAI,CAACzO,KAAK,GAAGyO,GAAG,CAACzO,KAAK,EAAEG,OAAO,IAAI,eAAe;QAClD;QACAqO,UAAU,CAAC,MAAK;UACd,IAAI,CAACxO,KAAK,GAAG,EAAE;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACN;EAEA+B,kBAAkBA,CAAA;IAChB,IAAI,CAAC2M,OAAO,CAAC,uDAAuD,CAAC,EACnE;IAEF,IAAI,CAACvM,aAAa,GAAG,IAAI;IACzB,IAAI,CAAChC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACH,KAAK,GAAG,EAAE;IAEf,IAAI,CAAC0K,WAAW,CACb3I,kBAAkB,EAAE,CACpBiM,IAAI,CAACzO,QAAQ,CAAC,MAAO,IAAI,CAAC4C,aAAa,GAAG,KAAM,CAAC,CAAC,CAClDiJ,SAAS,CAAC;MACTC,IAAI,EAAG4C,QAAa,IAAI;QACtB,IAAI,CAAC9N,OAAO,GACV8N,QAAQ,CAAC9N,OAAO,IAAI,sCAAsC;QAE5D;QACA,IAAI,CAACkC,IAAI,CAACoJ,eAAe,GAAG,IAAI;QAChC,IAAI,CAACpJ,IAAI,CAAC0F,YAAY,GAAG,IAAI;QAC7B,IAAI,CAAC1F,IAAI,CAACkJ,KAAK,GAAG,IAAI;QAEtB;QACA,IAAI,CAACb,WAAW,CAACyD,iBAAiB,CAAC;UACjCpG,YAAY,EAAE,mCAAmC;UACjDwD,KAAK,EAAE;SACR,CAAC;QAEF;QACA,IAAI,CAACd,eAAe,CAAC2D,cAAc,CAAC;UAClC,GAAG,IAAI,CAAC/L,IAAI;UACZ0F,YAAY,EAAE,mCAAmC;UACjDwD,KAAK,EAAE;SACR,CAAC;QAEF,IAAI0C,QAAQ,CAACI,KAAK,EAAE;UAClBC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEN,QAAQ,CAACI,KAAK,CAAC;;QAG/C;QACAG,UAAU,CAAC,MAAK;UACd,IAAI,CAACrO,OAAO,GAAG,EAAE;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDH,KAAK,EAAGyO,GAAmC,IAAI;QAC7C,IAAI,CAACzO,KAAK,GAAGyO,GAAG,CAACzO,KAAK,EAAEG,OAAO,IAAI,gBAAgB;QACnD;QACAqO,UAAU,CAAC,MAAK;UACd,IAAI,CAACxO,KAAK,GAAG,EAAE;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACN;EAEQqN,cAAcA,CAAA;IACpB,IAAI,CAACvF,aAAa,GAAG,IAAI;IACzB,IAAI,CAACsC,UAAU,GAAG,IAAI;IACtB,MAAMuE,SAAS,GAAGC,QAAQ,CAACC,cAAc,CACvC,gBAAgB,CACG;IACrB,IAAIF,SAAS,EAAEA,SAAS,CAACzC,KAAK,GAAG,EAAE;EACrC;EAEA4C,UAAUA,CAACC,IAAY;IACrB,IAAI,CAACpE,MAAM,CAACqE,QAAQ,CAAC,CAACD,IAAI,CAAC,CAAC;EAC9B;EAEA;EACApO,cAAcA,CAAA;IACZ,IAAI,CAACkK,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,IAAI,CAACA,UAAU,EAAE;MACnB,IAAI,CAACc,gBAAgB,EAAE;;IAEzB,IAAI,CAACxL,OAAO,GAAG,EAAE;IACjB,IAAI,CAACH,KAAK,GAAG,EAAE;EACjB;EAEAkJ,YAAYA,CAAA;IACV,IAAI,IAAI,CAACgB,QAAQ,CAAC+E,OAAO,EAAE;MACzB,IAAI,CAACC,oBAAoB,EAAE;MAC3B;;IAGF,IAAI,CAAC7E,WAAW,GAAG,IAAI;IACvB,IAAI,CAACrK,KAAK,GAAG,EAAE;IACf,IAAI,CAACG,OAAO,GAAG,EAAE;IAEjB,MAAMgP,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAE/B;IACAC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACpF,QAAQ,CAACgC,KAAK,CAAC,CAACF,OAAO,CAACuD,GAAG,IAAG;MAC7C,MAAMrD,KAAK,GAAG,IAAI,CAAChC,QAAQ,CAACgC,KAAK,CAACqD,GAAG,CAAC;MACtC,IAAIA,GAAG,KAAK,QAAQ,IAAIrD,KAAK,EAAE;QAC7B;QACA,MAAMsD,WAAW,GAAGtD,KAAK,CAACuD,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,KAAa,IAAKA,KAAK,CAACnE,IAAI,EAAE,CAAC,CAACoE,MAAM,CAAED,KAAa,IAAKA,KAAK,CAAC;QAC1GR,QAAQ,CAACU,MAAM,CAACN,GAAG,EAAEO,IAAI,CAACC,SAAS,CAACP,WAAW,CAAC,CAAC;OAClD,MAAM,IAAItD,KAAK,EAAE;QAChBiD,QAAQ,CAACU,MAAM,CAACN,GAAG,EAAErD,KAAK,CAAC;;IAE/B,CAAC,CAAC;IAEF;IACA,IAAI,IAAI,CAACpE,aAAa,EAAE;MACtBqH,QAAQ,CAACU,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC/H,aAAa,CAAC;;IAG9C,IAAI,CAAC4C,WAAW,CAACsF,eAAe,CAACb,QAAQ,CAAC,CAAC/D,SAAS,CAAC;MACnDC,IAAI,EAAG4C,QAAa,IAAI;QACtB,IAAI,CAAC5D,WAAW,GAAG,KAAK;QACxB,IAAI,CAAClK,OAAO,GAAG,+BAA+B;QAE9C;QACA,IAAI,CAACkC,IAAI,GAAG;UAAE,GAAG,IAAI,CAACA,IAAI;UAAE,GAAG4L,QAAQ,CAAC5L;QAAI,CAAE;QAC9C,IAAI,CAACoI,eAAe,CAAC2D,cAAc,CAAC,IAAI,CAAC/L,IAAI,CAAC;QAE9C;QACA,IAAI,CAACqJ,0BAA0B,EAAE;QAEjC;QACA,IAAI,CAACb,UAAU,GAAG,KAAK;QAEvB;QACA,IAAI,CAAC/C,aAAa,GAAG,IAAI;QACzB,IAAI,CAACsC,UAAU,GAAG,IAAI;QACtB,IAAI,CAACiD,cAAc,EAAE;QAErB;QACAmB,UAAU,CAAC,MAAK;UACd,IAAI,CAACrO,OAAO,GAAG,EAAE;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDH,KAAK,EAAGyO,GAAG,IAAI;QACb,IAAI,CAACpE,WAAW,GAAG,KAAK;QACxB,IAAI,CAACrK,KAAK,GAAGyO,GAAG,CAACzO,KAAK,EAAEG,OAAO,IAAI,gDAAgD;QAEnF;QACAqO,UAAU,CAAC,MAAK;UACd,IAAI,CAACxO,KAAK,GAAG,EAAE;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACJ;EAEA8I,UAAUA,CAAA;IACR,IAAI,CAAC+B,UAAU,GAAG,KAAK;IACvB,IAAI,CAACc,gBAAgB,EAAE,CAAC,CAAC;IACzB,IAAI,CAAC7D,aAAa,GAAG,IAAI;IACzB,IAAI,CAACsC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACiD,cAAc,EAAE;IACrB,IAAI,CAAClN,OAAO,GAAG,EAAE;IACjB,IAAI,CAACH,KAAK,GAAG,EAAE;EACjB;EAEQkP,oBAAoBA,CAAA;IAC1BG,MAAM,CAACC,IAAI,CAAC,IAAI,CAACpF,QAAQ,CAAC+F,QAAQ,CAAC,CAACjE,OAAO,CAACuD,GAAG,IAAG;MAChD,IAAI,CAACrF,QAAQ,CAACgG,GAAG,CAACX,GAAG,CAAC,EAAEY,aAAa,EAAE;IACzC,CAAC,CAAC;EACJ;EAEA;EACAjI,aAAaA,CAACkI,SAAiB;IAC7B,MAAMnE,KAAK,GAAG,IAAI,CAAC/B,QAAQ,CAACgG,GAAG,CAACE,SAAS,CAAC;IAC1C,IAAInE,KAAK,EAAEoE,MAAM,IAAIpE,KAAK,CAACqE,OAAO,EAAE;MAClC,IAAIrE,KAAK,CAACoE,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,GAAGD,SAAS,cAAc;MAC/D,IAAInE,KAAK,CAACoE,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,GAAGD,SAAS,eAAe;MACjE,IAAInE,KAAK,CAACoE,MAAM,CAAC,OAAO,CAAC,EAAE,OAAO,sBAAsB;MACxD,IAAIpE,KAAK,CAACoE,MAAM,CAAC,SAAS,CAAC,EAAE,OAAO,GAAGD,SAAS,oBAAoB;;IAEtE,OAAO,EAAE;EACX;EAEAjG,cAAcA,CAACiG,SAAiB;IAC9B,MAAMnE,KAAK,GAAG,IAAI,CAAC/B,QAAQ,CAACgG,GAAG,CAACE,SAAS,CAAC;IAC1C,OAAO,CAAC,EAAEnE,KAAK,EAAEgD,OAAO,IAAIhD,KAAK,CAACqE,OAAO,CAAC;EAC5C;EAEAtP,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACC,kBAAkB,GAAG,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACpD,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACpD,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACpD,IAAI,IAAI,CAACA,kBAAkB,GAAG,GAAG,EAAE,OAAO,SAAS,CAAC,CAAC;IACrD,OAAO,SAAS,CAAC,CAAC;EACpB;;EAEAC,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAACD,kBAAkB,GAAG,EAAE,EAAE;MAChC,OAAO,wDAAwD;KAChE,MAAM,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE;MACvC,OAAO,8CAA8C;KACtD,MAAM,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE;MACvC,OAAO,+CAA+C;KACvD,MAAM,IAAI,IAAI,CAACA,kBAAkB,GAAG,GAAG,EAAE;MACxC,OAAO,6CAA6C;KACrD,MAAM;MACL,OAAO,sCAAsC;;EAEjD;EAEAyE,MAAMA,CAAA;IACJ,IAAI,CAAC+E,eAAe,CAAC/E,MAAM,EAAE,CAAC0F,SAAS,CAAC;MACtCC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACZ,eAAe,CAAC8F,aAAa,EAAE;QACpC/B,UAAU,CAAC,MAAK;UACd,IAAI,CAAC7D,MAAM,CAACqE,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;YAC/BwB,WAAW,EAAE;cAAErQ,OAAO,EAAE;YAAqB,CAAE;YAC/CsQ,UAAU,EAAE;WACb,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDzQ,KAAK,EAAGyO,GAAQ,IAAI;QAClBZ,OAAO,CAAC7N,KAAK,CAAC,eAAe,EAAEyO,GAAG,CAAC;QACnC,IAAI,CAAChE,eAAe,CAAC8F,aAAa,EAAE;QACpC/B,UAAU,CAAC,MAAK;UACd,IAAI,CAAC7D,MAAM,CAACqE,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;QACtC,CAAC,EAAE,GAAG,CAAC;MACT;KACD,CAAC;EACJ;EAEA;EACAjL,cAAcA,CAAC2M,OAAe;IAC5B,IAAI,CAAC5F,WAAW,GAAG4F,OAAO;IAC1B,IAAI,CAAC7F,UAAU,GAAG,IAAI;IACtB,IAAI,CAACc,gBAAgB,EAAE;EACzB;EAEApF,2BAA2BA,CAAA;IACzB,IAAI,CAAC,IAAI,CAAClE,IAAI,EAAE,OAAO,CAAC;IAExB,MAAMuJ,cAAc,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,KAAK,CAAC;IACxH,IAAI+E,SAAS,GAAG,CAAC;IAEjB/E,cAAc,CAACI,OAAO,CAACC,KAAK,IAAG;MAC7B,MAAMC,KAAK,GAAG,IAAI,CAAC7J,IAAI,CAAC4J,KAAK,CAAC;MAC9B,IAAIC,KAAK,IAAIA,KAAK,CAACC,QAAQ,EAAE,CAACX,IAAI,EAAE,KAAK,EAAE,IAAIU,KAAK,KAAK,qBAAqB,EAAE;QAC9EyE,SAAS,EAAE;;IAEf,CAAC,CAAC;IAEF,OAAOpE,IAAI,CAACC,KAAK,CAAEmE,SAAS,GAAG/E,cAAc,CAACjJ,MAAM,GAAI,GAAG,CAAC;EAC9D;EAEA6D,2BAA2BA,CAAA;IACzB,IAAI,CAAC,IAAI,CAACnE,IAAI,EAAE,OAAO,CAAC;IAExB,MAAMwJ,cAAc,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC;IACxD,IAAI8E,SAAS,GAAG,CAAC;IAEjB9E,cAAc,CAACG,OAAO,CAACC,KAAK,IAAG;MAC7B,MAAMC,KAAK,GAAG,IAAI,CAAC7J,IAAI,CAAC4J,KAAK,CAAC;MAC9B,IAAIA,KAAK,KAAK,QAAQ,EAAE;QACtB;QACA,IAAIS,KAAK,CAACC,OAAO,CAACT,KAAK,CAAC,IAAIA,KAAK,CAACvJ,MAAM,GAAG,CAAC,EAAE;UAC5CgO,SAAS,EAAE;;OAEd,MAAM,IAAIzE,KAAK,IAAIA,KAAK,CAACC,QAAQ,EAAE,CAACX,IAAI,EAAE,KAAK,EAAE,EAAE;QAClDmF,SAAS,EAAE;;IAEf,CAAC,CAAC;IAEF,OAAOpE,IAAI,CAACC,KAAK,CAAEmE,SAAS,GAAG9E,cAAc,CAAClJ,MAAM,GAAI,GAAG,CAAC;EAC9D;EAEA8D,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACpE,IAAI,EAAE,OAAO,KAAK;IAE5B,OAAO,CAAC,EAAE,IAAI,CAACA,IAAI,CAAC0F,YAAY,IACvB,IAAI,CAAC1F,IAAI,CAAC0F,YAAY,KAAK,qBAAqB,IAChD,IAAI,CAAC1F,IAAI,CAAC0F,YAAY,KAAK,mCAAmC,IAC9D,IAAI,CAAC1F,IAAI,CAAC0F,YAAY,CAACyD,IAAI,EAAE,KAAK,EAAE,CAAC;EAChD;EAEApF,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAAC/D,IAAI,EAAE,OAAO,EAAE;IAEzB,MAAMuO,MAAM,GAAG,CACb;MAAE3E,KAAK,EAAE,WAAW;MAAEtI,KAAK,EAAE,YAAY;MAAEmC,QAAQ,EAAE;IAAI,CAAE,EAC3D;MAAEmG,KAAK,EAAE,UAAU;MAAEtI,KAAK,EAAE,WAAW;MAAEmC,QAAQ,EAAE;IAAI,CAAE,EACzD;MAAEmG,KAAK,EAAE,UAAU;MAAEtI,KAAK,EAAE,WAAW;MAAEmC,QAAQ,EAAE;IAAI,CAAE,EACzD;MAAEmG,KAAK,EAAE,aAAa;MAAEtI,KAAK,EAAE,eAAe;MAAEmC,QAAQ,EAAE;IAAI,CAAE,EAChE;MAAEmG,KAAK,EAAE,aAAa;MAAEtI,KAAK,EAAE,cAAc;MAAEmC,QAAQ,EAAE;IAAI,CAAE,EAC/D;MAAEmG,KAAK,EAAE,YAAY;MAAEtI,KAAK,EAAE,YAAY;MAAEmC,QAAQ,EAAE;IAAI,CAAE,EAC5D;MAAEmG,KAAK,EAAE,KAAK;MAAEtI,KAAK,EAAE,KAAK;MAAEmC,QAAQ,EAAE;IAAI,CAAE,EAC9C;MAAEmG,KAAK,EAAE,UAAU;MAAEtI,KAAK,EAAE,UAAU;MAAEmC,QAAQ,EAAE;IAAK,CAAE,EACzD;MAAEmG,KAAK,EAAE,SAAS;MAAEtI,KAAK,EAAE,SAAS;MAAEmC,QAAQ,EAAE;IAAK,CAAE,EACvD;MAAEmG,KAAK,EAAE,QAAQ;MAAEtI,KAAK,EAAE,QAAQ;MAAEmC,QAAQ,EAAE;IAAK,CAAE,CACtD;IAED,OAAO8K,MAAM,CAAChB,MAAM,CAAC3D,KAAK,IAAG;MAC3B,MAAMC,KAAK,GAAG,IAAI,CAAC7J,IAAI,CAAC4J,KAAK,CAACA,KAAK,CAAC;MACpC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,EAAE,CAACX,IAAI,EAAE,KAAK,EAAE;IACjD,CAAC,CAAC;EACJ;;;uBAxjBWlB,gBAAgB,EAAA9K,EAAA,CAAAqR,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvR,EAAA,CAAAqR,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAzR,EAAA,CAAAqR,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA3R,EAAA,CAAAqR,iBAAA,CAAAO,EAAA,CAAAC,MAAA,GAAA7R,EAAA,CAAAqR,iBAAA,CAAAS,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBjH,gBAAgB;MAAAkH,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCb7BtS,EAAA,CAAAC,cAAA,aAEC;UAGGD,EAAA,CAAAE,SAAA,aAEO;UAMPF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAC,cAAA,cAAuD;UAMjDD,EAAA,CAAAI,MAAA,oBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAA2D;UACzDD,EAAA,CAAAI,MAAA,+EACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAINH,EAAA,CAAAoB,UAAA,KAAAoR,gCAAA,kBAUM;UAGNxS,EAAA,CAAAoB,UAAA,KAAAqR,gCAAA,mBAmBM;UAGNzS,EAAA,CAAAoB,UAAA,KAAAsR,gCAAA,mBAqBM;UAGN1S,EAAA,CAAAoB,UAAA,KAAAuR,gCAAA,mBAoCM;UAGN3S,EAAA,CAAAoB,UAAA,KAAAwR,gCAAA,oBAsZM;UAGN5S,EAAA,CAAAoB,UAAA,KAAAyR,gCAAA,oBA6OM;UACR7S,EAAA,CAAAG,YAAA,EAAM;;;UAzuBEH,EAAA,CAAAK,SAAA,IAAW;UAAXL,EAAA,CAAA2B,UAAA,UAAA4Q,GAAA,CAAA1P,IAAA,CAAW;UAcd7C,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAA2B,UAAA,SAAA4Q,GAAA,CAAA/R,KAAA,CAAW;UAsBXR,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAA2B,UAAA,SAAA4Q,GAAA,CAAA5R,OAAA,CAAa;UAuBVX,EAAA,CAAAK,SAAA,GAAU;UAAVL,EAAA,CAAA2B,UAAA,SAAA4Q,GAAA,CAAA1P,IAAA,CAAU;UAuCV7C,EAAA,CAAAK,SAAA,GAAU;UAAVL,EAAA,CAAA2B,UAAA,SAAA4Q,GAAA,CAAA1P,IAAA,CAAU;UAyZV7C,EAAA,CAAAK,SAAA,GAAgB;UAAhBL,EAAA,CAAA2B,UAAA,SAAA4Q,GAAA,CAAAlH,UAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}