{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nexport let DashboardComponent = class DashboardComponent {\n  constructor(authService, router, toastService) {\n    this.authService = authService;\n    this.router = router;\n    this.toastService = toastService;\n    this.users = [];\n    this.error = '';\n    this.message = '';\n    this.roles = ['student', 'teacher', 'admin'];\n    this.loading = true;\n    this.currentUser = null;\n    this.searchTerm = '';\n    this.filteredUsers = [];\n    // Create user modal properties\n    this.showCreateUserModal = false;\n    this.creatingUser = false;\n    this.newUser = {\n      fullName: '',\n      email: '',\n      role: 'student'\n    };\n    // Enhanced dashboard data\n    this.systemStats = {\n      totalUsers: 0,\n      activeUsers: 0,\n      newUsersToday: 0,\n      newUsersThisWeek: 0,\n      totalProjects: 0,\n      activeProjects: 0,\n      totalTeams: 0,\n      systemUptime: '99.9%'\n    };\n    // Activity logs\n    this.recentActivities = [];\n    // Filters and sorting\n    this.selectedRole = '';\n    this.selectedStatus = '';\n    this.sortBy = 'fullName';\n    this.sortOrder = 'asc';\n    // Bulk operations\n    this.selectedUsers = new Set();\n    this.showBulkActions = false;\n    // Advanced search\n    this.showAdvancedSearch = false;\n    this.advancedFilters = {\n      dateFrom: '',\n      dateTo: '',\n      verified: '',\n      hasGroup: ''\n    };\n  }\n  ngOnInit() {\n    this.loadUserData();\n  }\n  loadUserData() {\n    this.loading = true;\n    const token = localStorage.getItem('token');\n    const userStr = localStorage.getItem('user');\n    if (!token || !userStr) {\n      this.router.navigate(['/admin/login']);\n      return;\n    }\n    this.currentUser = JSON.parse(userStr);\n    // Check if user is admin\n    if (this.currentUser.role !== 'admin') {\n      this.router.navigate(['/']);\n      return;\n    }\n    this.authService.getAllUsers(token).subscribe({\n      next: res => {\n        this.users = res;\n        this.filteredUsers = [...this.users];\n        this.updateSystemStats();\n        this.loadRecentActivities();\n        this.loading = false;\n      },\n      error: err => {\n        this.error = err.error?.message || 'Failed to fetch users';\n        this.loading = false;\n      }\n    });\n  }\n  searchUsers() {\n    this.applyAdvancedFilters();\n  }\n  clearSearch() {\n    this.searchTerm = '';\n    this.applyAdvancedFilters();\n  }\n  onRoleChange(userId, newRole) {\n    const token = localStorage.getItem('token');\n    this.authService.updateUserRole(userId, newRole, token).subscribe({\n      next: res => {\n        this.message = res.message;\n        this.error = '';\n        // Update the user in the local arrays\n        const userIndex = this.users.findIndex(u => u._id === userId);\n        if (userIndex !== -1) {\n          this.users[userIndex].role = newRole;\n        }\n        const filteredIndex = this.filteredUsers.findIndex(u => u._id === userId);\n        if (filteredIndex !== -1) {\n          this.filteredUsers[filteredIndex].role = newRole;\n        }\n        // Auto-hide message after 3 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 3000);\n      },\n      error: err => {\n        this.error = err.error?.message || 'Failed to update role';\n        this.message = '';\n        // Auto-hide error after 3 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 3000);\n      }\n    });\n  }\n  onDeleteUser(userId) {\n    const confirmDelete = confirm('Are you sure you want to delete this user?');\n    if (!confirmDelete) return;\n    const token = localStorage.getItem('token');\n    this.authService.deleteUser(userId, token).subscribe({\n      next: res => {\n        this.message = res.message;\n        this.error = '';\n        // Remove user from both arrays\n        this.users = this.users.filter(u => u._id !== userId);\n        this.filteredUsers = this.filteredUsers.filter(u => u._id !== userId);\n        // Auto-hide message after 3 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 3000);\n      },\n      error: err => {\n        this.error = err.error?.message || 'Failed to delete user';\n        this.message = '';\n        // Auto-hide error after 3 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 3000);\n      }\n    });\n  }\n  toggleUserActivation(userId, currentStatus) {\n    const newStatus = !currentStatus;\n    const action = newStatus ? 'activate' : 'deactivate';\n    // Find the user to get their name for better messaging\n    const user = this.users.find(u => u._id === userId);\n    const userName = user?.fullName || user?.firstName || 'User';\n    const confirmAction = confirm(`Are you sure you want to ${action} ${userName}?`);\n    if (!confirmAction) return;\n    const token = localStorage.getItem('token');\n    this.authService.toggleUserActivation(userId, newStatus, token).subscribe({\n      next: res => {\n        const statusText = newStatus ? 'activated' : 'deactivated';\n        const successMessage = `${userName} has been ${statusText} successfully`;\n        // Show success toast\n        this.toastService.showSuccess(successMessage);\n        // Clear any existing messages\n        this.message = '';\n        this.error = '';\n        // Update user in both arrays\n        const userIndex = this.users.findIndex(u => u._id === userId);\n        if (userIndex !== -1) {\n          this.users[userIndex].isActive = newStatus;\n        }\n        const filteredIndex = this.filteredUsers.findIndex(u => u._id === userId);\n        if (filteredIndex !== -1) {\n          this.filteredUsers[filteredIndex].isActive = newStatus;\n        }\n        // Apply filters to refresh the view\n        this.applyFilters();\n      },\n      error: err => {\n        const statusText = newStatus ? 'activate' : 'deactivate';\n        const errorMessage = err.error?.message || `Failed to ${statusText} ${userName}`;\n        // Show error toast\n        this.toastService.showError(errorMessage);\n        // Clear any existing messages\n        this.message = '';\n        this.error = '';\n      }\n    });\n  }\n  getStudentCount() {\n    return this.users.filter(u => u.role === 'student').length;\n  }\n  getTeacherCount() {\n    return this.users.filter(u => u.role === 'teacher').length;\n  }\n  getAdminCount() {\n    return this.users.filter(u => u.role === 'admin').length;\n  }\n  getActiveCount() {\n    return this.users.filter(u => u.isActive !== false).length;\n  }\n  getInactiveCount() {\n    return this.users.filter(u => u.isActive === false).length;\n  }\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/admin/login']);\n  }\n  showUserDetails(userId) {\n    this.router.navigate(['/admin/userdetails', userId]);\n  }\n  applyFilters() {\n    this.applyAdvancedFilters();\n  }\n  // Create user modal methods\n  openCreateUserModal() {\n    this.showCreateUserModal = true;\n    this.resetNewUserForm();\n  }\n  closeCreateUserModal() {\n    this.showCreateUserModal = false;\n    this.resetNewUserForm();\n  }\n  resetNewUserForm() {\n    this.newUser = {\n      fullName: '',\n      email: '',\n      role: 'student'\n    };\n    this.creatingUser = false;\n  }\n  onCreateUser(form) {\n    if (form.invalid || this.creatingUser) {\n      return;\n    }\n    this.creatingUser = true;\n    this.error = '';\n    this.message = '';\n    const token = localStorage.getItem('token');\n    this.authService.createUser(this.newUser, token).subscribe({\n      next: res => {\n        // Show success message\n        this.message = res.message;\n        this.error = '';\n        // Add new user to the lists\n        this.users.push(res.user);\n        this.filteredUsers = [...this.users];\n        this.applyFilters();\n        // Show success toast with credentials info\n        this.toastService.showSuccess(`User created successfully! Login credentials sent to ${res.user.email}`);\n        // Close modal and reset form\n        this.closeCreateUserModal();\n        // Auto-hide message after 5 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 5000);\n      },\n      error: err => {\n        this.error = err.error?.message || 'Failed to create user';\n        this.message = '';\n        this.creatingUser = false;\n        // Show error toast\n        this.toastService.showError(this.error);\n        // Auto-hide error after 3 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 3000);\n      }\n    });\n  }\n  // Enhanced dashboard methods\n  updateSystemStats() {\n    const now = new Date();\n    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);\n    this.systemStats = {\n      totalUsers: this.users.length,\n      activeUsers: this.users.filter(u => u.isActive !== false).length,\n      newUsersToday: this.users.filter(u => {\n        const userDate = new Date(u.createdAt);\n        return userDate >= today;\n      }).length,\n      newUsersThisWeek: this.users.filter(u => {\n        const userDate = new Date(u.createdAt);\n        return userDate >= weekAgo;\n      }).length,\n      totalProjects: 0,\n      activeProjects: 0,\n      totalTeams: 0,\n      systemUptime: '99.9%'\n    };\n  }\n  loadRecentActivities() {\n    // Mock recent activities - in real app, this would come from an API\n    this.recentActivities = [{\n      id: 1,\n      type: 'user_created',\n      message: 'New user registered',\n      user: 'John Doe',\n      timestamp: new Date(Date.now() - 1000 * 60 * 30),\n      icon: 'fas fa-user-plus',\n      color: 'text-green-600'\n    }, {\n      id: 2,\n      type: 'user_login',\n      message: 'User logged in',\n      user: 'Jane Smith',\n      timestamp: new Date(Date.now() - 1000 * 60 * 60),\n      icon: 'fas fa-sign-in-alt',\n      color: 'text-blue-600'\n    }, {\n      id: 3,\n      type: 'role_changed',\n      message: 'User role updated to Teacher',\n      user: 'Mike Johnson',\n      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),\n      icon: 'fas fa-user-tag',\n      color: 'text-purple-600'\n    }];\n  }\n  // Advanced filtering and sorting\n  applyAdvancedFilters() {\n    let filtered = [...this.users];\n    // Apply role filter\n    if (this.selectedRole) {\n      filtered = filtered.filter(user => user.role === this.selectedRole);\n    }\n    // Apply status filter\n    if (this.selectedStatus) {\n      if (this.selectedStatus === 'active') {\n        filtered = filtered.filter(user => user.isActive !== false);\n      } else if (this.selectedStatus === 'inactive') {\n        filtered = filtered.filter(user => user.isActive === false);\n      }\n    }\n    // Apply verification filter\n    if (this.advancedFilters.verified) {\n      const isVerified = this.advancedFilters.verified === 'true';\n      filtered = filtered.filter(user => user.verified === isVerified);\n    }\n    // Apply date range filter\n    if (this.advancedFilters.dateFrom) {\n      const fromDate = new Date(this.advancedFilters.dateFrom);\n      filtered = filtered.filter(user => new Date(user.createdAt) >= fromDate);\n    }\n    if (this.advancedFilters.dateTo) {\n      const toDate = new Date(this.advancedFilters.dateTo);\n      filtered = filtered.filter(user => new Date(user.createdAt) <= toDate);\n    }\n    // Apply search term\n    if (this.searchTerm.trim()) {\n      const term = this.searchTerm.toLowerCase().trim();\n      filtered = filtered.filter(user => user.fullName.toLowerCase().includes(term) || user.email.toLowerCase().includes(term) || user.role.toLowerCase().includes(term));\n    }\n    // Apply sorting\n    filtered.sort((a, b) => {\n      let aValue = a[this.sortBy];\n      let bValue = b[this.sortBy];\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (this.sortOrder === 'asc') {\n        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n      } else {\n        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n      }\n    });\n    this.filteredUsers = filtered;\n  }\n  // Bulk operations\n  toggleUserSelection(userId) {\n    if (this.selectedUsers.has(userId)) {\n      this.selectedUsers.delete(userId);\n    } else {\n      this.selectedUsers.add(userId);\n    }\n    this.showBulkActions = this.selectedUsers.size > 0;\n  }\n  selectAllUsers() {\n    if (this.selectedUsers.size === this.filteredUsers.length) {\n      this.selectedUsers.clear();\n    } else {\n      this.filteredUsers.forEach(user => this.selectedUsers.add(user._id));\n    }\n    this.showBulkActions = this.selectedUsers.size > 0;\n  }\n  bulkDeleteUsers() {\n    if (this.selectedUsers.size === 0) return;\n    const confirmDelete = confirm(`Are you sure you want to delete ${this.selectedUsers.size} users?`);\n    if (!confirmDelete) return;\n    const token = localStorage.getItem('token');\n    const userIds = Array.from(this.selectedUsers);\n    // For now, delete users one by one (in real app, implement bulk delete API)\n    let deletedCount = 0;\n    userIds.forEach(userId => {\n      this.authService.deleteUser(userId, token).subscribe({\n        next: () => {\n          deletedCount++;\n          this.users = this.users.filter(u => u._id !== userId);\n          if (deletedCount === userIds.length) {\n            this.selectedUsers.clear();\n            this.showBulkActions = false;\n            this.applyAdvancedFilters();\n            this.toastService.showSuccess(`${deletedCount} users deleted successfully`);\n          }\n        },\n        error: err => {\n          this.toastService.showError(`Failed to delete some users: ${err.error?.message}`);\n        }\n      });\n    });\n  }\n  bulkChangeRole(newRole) {\n    if (this.selectedUsers.size === 0) return;\n    const confirmChange = confirm(`Are you sure you want to change the role of ${this.selectedUsers.size} users to ${newRole}?`);\n    if (!confirmChange) return;\n    const token = localStorage.getItem('token');\n    const userIds = Array.from(this.selectedUsers);\n    let updatedCount = 0;\n    userIds.forEach(userId => {\n      this.authService.updateUserRole(userId, newRole, token).subscribe({\n        next: () => {\n          updatedCount++;\n          const userIndex = this.users.findIndex(u => u._id === userId);\n          if (userIndex !== -1) {\n            this.users[userIndex].role = newRole;\n          }\n          if (updatedCount === userIds.length) {\n            this.selectedUsers.clear();\n            this.showBulkActions = false;\n            this.applyAdvancedFilters();\n            this.toastService.showSuccess(`${updatedCount} users updated successfully`);\n          }\n        },\n        error: err => {\n          this.toastService.showError(`Failed to update some users: ${err.error?.message}`);\n        }\n      });\n    });\n  }\n  // Export functionality\n  exportUsers(format) {\n    const dataToExport = this.filteredUsers.map(user => ({\n      fullName: user.fullName,\n      email: user.email,\n      role: user.role,\n      isActive: user.isActive,\n      verified: user.verified,\n      createdAt: user.createdAt,\n      group: user.group?.name || 'No Group'\n    }));\n    if (format === 'csv') {\n      this.exportToCSV(dataToExport);\n    } else {\n      this.exportToJSON(dataToExport);\n    }\n  }\n  exportToCSV(data) {\n    const headers = Object.keys(data[0]);\n    const csvContent = [headers.join(','), ...data.map(row => headers.map(header => `\"${row[header]}\"`).join(','))].join('\\n');\n    this.downloadFile(csvContent, 'users.csv', 'text/csv');\n  }\n  exportToJSON(data) {\n    const jsonContent = JSON.stringify(data, null, 2);\n    this.downloadFile(jsonContent, 'users.json', 'application/json');\n  }\n  downloadFile(content, filename, contentType) {\n    const blob = new Blob([content], {\n      type: contentType\n    });\n    const url = window.URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = filename;\n    link.click();\n    window.URL.revokeObjectURL(url);\n  }\n  // Utility methods\n  toggleAdvancedSearch() {\n    this.showAdvancedSearch = !this.showAdvancedSearch;\n  }\n  clearAdvancedFilters() {\n    this.advancedFilters = {\n      dateFrom: '',\n      dateTo: '',\n      verified: '',\n      hasGroup: ''\n    };\n    this.selectedRole = '';\n    this.selectedStatus = '';\n    this.applyAdvancedFilters();\n  }\n  setSortBy(field) {\n    if (this.sortBy === field) {\n      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';\n    } else {\n      this.sortBy = field;\n      this.sortOrder = 'asc';\n    }\n    this.applyAdvancedFilters();\n  }\n};\nDashboardComponent = __decorate([Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.css']\n})], DashboardComponent);", "map": {"version": 3, "names": ["Component", "DashboardComponent", "constructor", "authService", "router", "toastService", "users", "error", "message", "roles", "loading", "currentUser", "searchTerm", "filteredUsers", "showCreateUserModal", "creatingUser", "newUser", "fullName", "email", "role", "systemStats", "totalUsers", "activeUsers", "newUsersToday", "newUsersThisWeek", "totalProjects", "activeProjects", "totalTeams", "systemUptime", "recentActivities", "selectedR<PERSON>", "selectedStatus", "sortBy", "sortOrder", "selectedUsers", "Set", "showBulkActions", "showAdvancedSearch", "advancedFilters", "dateFrom", "dateTo", "verified", "hasGroup", "ngOnInit", "loadUserData", "token", "localStorage", "getItem", "userStr", "navigate", "JSON", "parse", "getAllUsers", "subscribe", "next", "res", "updateSystemStats", "loadRecentActivities", "err", "searchUsers", "applyAdvancedFilters", "clearSearch", "onRoleChange", "userId", "newRole", "updateUserRole", "userIndex", "findIndex", "u", "_id", "filteredIndex", "setTimeout", "onDeleteUser", "confirmDelete", "confirm", "deleteUser", "filter", "toggleUserActivation", "currentStatus", "newStatus", "action", "user", "find", "userName", "firstName", "confirmAction", "statusText", "successMessage", "showSuccess", "isActive", "applyFilters", "errorMessage", "showError", "getStudentCount", "length", "getTeacher<PERSON>ount", "getAdminCount", "getActiveCount", "getInactiveCount", "logout", "showUserDetails", "openCreateUserModal", "resetNewUserForm", "closeCreateUserModal", "onCreateUser", "form", "invalid", "createUser", "push", "now", "Date", "today", "getFullYear", "getMonth", "getDate", "weekAgo", "getTime", "userDate", "createdAt", "id", "type", "timestamp", "icon", "color", "filtered", "isVerified", "fromDate", "toDate", "trim", "term", "toLowerCase", "includes", "sort", "a", "b", "aValue", "bValue", "toggleUserSelection", "has", "delete", "add", "size", "selectAllUsers", "clear", "for<PERSON>ach", "bulkDeleteUsers", "userIds", "Array", "from", "deletedCount", "bulkChangeRole", "confirmChange", "updatedCount", "exportUsers", "format", "dataToExport", "map", "group", "name", "exportToCSV", "exportToJSON", "data", "headers", "Object", "keys", "csv<PERSON><PERSON>nt", "join", "row", "header", "downloadFile", "json<PERSON><PERSON><PERSON>", "stringify", "content", "filename", "contentType", "blob", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "click", "revokeObjectURL", "toggleAdvancedSearch", "clearAdvancedFilters", "setSortBy", "field", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\dashboard\\dashboard.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { AuthService } from 'src/app/services/auth.service';\r\nimport { ToastService } from 'src/app/services/toast.service';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-dashboard',\r\n  templateUrl: './dashboard.component.html',\r\n  styleUrls: ['./dashboard.component.css'],\r\n})\r\nexport class DashboardComponent implements OnInit {\r\n  users: any[] = [];\r\n  error = '';\r\n  message = '';\r\n  roles = ['student', 'teacher', 'admin'];\r\n  loading = true;\r\n  currentUser: any = null;\r\n  searchTerm = '';\r\n  filteredUsers: any[] = [];\r\n\r\n  // Create user modal properties\r\n  showCreateUserModal = false;\r\n  creatingUser = false;\r\n  newUser = {\r\n    fullName: '',\r\n    email: '',\r\n    role: 'student'\r\n  };\r\n\r\n  // Enhanced dashboard data\r\n  systemStats = {\r\n    totalUsers: 0,\r\n    activeUsers: 0,\r\n    newUsersToday: 0,\r\n    newUsersThisWeek: 0,\r\n    totalProjects: 0,\r\n    activeProjects: 0,\r\n    totalTeams: 0,\r\n    systemUptime: '99.9%'\r\n  };\r\n\r\n  // Activity logs\r\n  recentActivities: any[] = [];\r\n\r\n  // Filters and sorting\r\n  selectedRole = '';\r\n  selectedStatus = '';\r\n  sortBy = 'fullName';\r\n  sortOrder = 'asc';\r\n\r\n  // Bulk operations\r\n  selectedUsers: Set<string> = new Set();\r\n  showBulkActions = false;\r\n\r\n  // Advanced search\r\n  showAdvancedSearch = false;\r\n  advancedFilters = {\r\n    dateFrom: '',\r\n    dateTo: '',\r\n    verified: '',\r\n    hasGroup: ''\r\n  };\r\n\r\n  constructor(\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    private toastService: ToastService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadUserData();\r\n  }\r\n\r\n  loadUserData(): void {\r\n    this.loading = true;\r\n    const token = localStorage.getItem('token');\r\n    const userStr = localStorage.getItem('user');\r\n\r\n    if (!token || !userStr) {\r\n      this.router.navigate(['/admin/login']);\r\n      return;\r\n    }\r\n\r\n    this.currentUser = JSON.parse(userStr);\r\n\r\n    // Check if user is admin\r\n    if (this.currentUser.role !== 'admin') {\r\n      this.router.navigate(['/']);\r\n      return;\r\n    }\r\n\r\n    this.authService.getAllUsers(token).subscribe({\r\n      next: (res: any) => {\r\n        this.users = res;\r\n        this.filteredUsers = [...this.users];\r\n        this.updateSystemStats();\r\n        this.loadRecentActivities();\r\n        this.loading = false;\r\n      },\r\n      error: (err) => {\r\n        this.error = err.error?.message || 'Failed to fetch users';\r\n        this.loading = false;\r\n      },\r\n    });\r\n  }\r\n  searchUsers(): void {\r\n    this.applyAdvancedFilters();\r\n  }\r\n\r\n  clearSearch(): void {\r\n    this.searchTerm = '';\r\n    this.applyAdvancedFilters();\r\n  }\r\n\r\n  onRoleChange(userId: string, newRole: string) {\r\n    const token = localStorage.getItem('token');\r\n    this.authService.updateUserRole(userId, newRole, token!).subscribe({\r\n      next: (res: any) => {\r\n        this.message = res.message;\r\n        this.error = '';\r\n\r\n        // Update the user in the local arrays\r\n        const userIndex = this.users.findIndex((u) => u._id === userId);\r\n        if (userIndex !== -1) {\r\n          this.users[userIndex].role = newRole;\r\n        }\r\n\r\n        const filteredIndex = this.filteredUsers.findIndex(\r\n          (u) => u._id === userId\r\n        );\r\n        if (filteredIndex !== -1) {\r\n          this.filteredUsers[filteredIndex].role = newRole;\r\n        }\r\n\r\n        // Auto-hide message after 3 seconds\r\n        setTimeout(() => {\r\n          this.message = '';\r\n        }, 3000);\r\n      },\r\n      error: (err) => {\r\n        this.error = err.error?.message || 'Failed to update role';\r\n        this.message = '';\r\n\r\n        // Auto-hide error after 3 seconds\r\n        setTimeout(() => {\r\n          this.error = '';\r\n        }, 3000);\r\n      },\r\n    });\r\n  }\r\n  onDeleteUser(userId: string) {\r\n    const confirmDelete = confirm('Are you sure you want to delete this user?');\r\n    if (!confirmDelete) return;\r\n\r\n    const token = localStorage.getItem('token');\r\n    this.authService.deleteUser(userId, token!).subscribe({\r\n      next: (res: any) => {\r\n        this.message = res.message;\r\n        this.error = '';\r\n\r\n        // Remove user from both arrays\r\n        this.users = this.users.filter((u) => u._id !== userId);\r\n        this.filteredUsers = this.filteredUsers.filter((u) => u._id !== userId);\r\n\r\n        // Auto-hide message after 3 seconds\r\n        setTimeout(() => {\r\n          this.message = '';\r\n        }, 3000);\r\n      },\r\n      error: (err) => {\r\n        this.error = err.error?.message || 'Failed to delete user';\r\n        this.message = '';\r\n\r\n        // Auto-hide error after 3 seconds\r\n        setTimeout(() => {\r\n          this.error = '';\r\n        }, 3000);\r\n      },\r\n    });\r\n  }\r\n  toggleUserActivation(userId: string, currentStatus: boolean) {\r\n    const newStatus = !currentStatus;\r\n    const action = newStatus ? 'activate' : 'deactivate';\r\n\r\n    // Find the user to get their name for better messaging\r\n    const user = this.users.find(u => u._id === userId);\r\n    const userName = user?.fullName || user?.firstName || 'User';\r\n\r\n    const confirmAction = confirm(\r\n      `Are you sure you want to ${action} ${userName}?`\r\n    );\r\n    if (!confirmAction) return;\r\n\r\n    const token = localStorage.getItem('token');\r\n    this.authService.toggleUserActivation(userId, newStatus, token!).subscribe({\r\n      next: (res: any) => {\r\n        const statusText = newStatus ? 'activated' : 'deactivated';\r\n        const successMessage = `${userName} has been ${statusText} successfully`;\r\n\r\n        // Show success toast\r\n        this.toastService.showSuccess(successMessage);\r\n\r\n        // Clear any existing messages\r\n        this.message = '';\r\n        this.error = '';\r\n\r\n        // Update user in both arrays\r\n        const userIndex = this.users.findIndex((u) => u._id === userId);\r\n        if (userIndex !== -1) {\r\n          this.users[userIndex].isActive = newStatus;\r\n        }\r\n\r\n        const filteredIndex = this.filteredUsers.findIndex(\r\n          (u) => u._id === userId\r\n        );\r\n        if (filteredIndex !== -1) {\r\n          this.filteredUsers[filteredIndex].isActive = newStatus;\r\n        }\r\n\r\n        // Apply filters to refresh the view\r\n        this.applyFilters();\r\n      },\r\n      error: (err) => {\r\n        const statusText = newStatus ? 'activate' : 'deactivate';\r\n        const errorMessage = err.error?.message || `Failed to ${statusText} ${userName}`;\r\n\r\n        // Show error toast\r\n        this.toastService.showError(errorMessage);\r\n\r\n        // Clear any existing messages\r\n        this.message = '';\r\n        this.error = '';\r\n      },\r\n    });\r\n  }\r\n  getStudentCount(): number {\r\n    return this.users.filter((u) => u.role === 'student').length;\r\n  }\r\n  getTeacherCount(): number {\r\n    return this.users.filter((u) => u.role === 'teacher').length;\r\n  }\r\n  getAdminCount(): number {\r\n    return this.users.filter((u) => u.role === 'admin').length;\r\n  }\r\n  getActiveCount(): number {\r\n    return this.users.filter((u) => u.isActive !== false).length;\r\n  }\r\n  getInactiveCount(): number {\r\n    return this.users.filter((u) => u.isActive === false).length;\r\n  }\r\n\r\n  logout() {\r\n    this.authService.logout();\r\n    this.router.navigate(['/admin/login']);\r\n  }\r\n\r\n  showUserDetails(userId: string) {\r\n    this.router.navigate(['/admin/userdetails', userId]);\r\n  }\r\n\r\n  applyFilters() {\r\n    this.applyAdvancedFilters();\r\n  }\r\n\r\n  // Create user modal methods\r\n  openCreateUserModal() {\r\n    this.showCreateUserModal = true;\r\n    this.resetNewUserForm();\r\n  }\r\n\r\n  closeCreateUserModal() {\r\n    this.showCreateUserModal = false;\r\n    this.resetNewUserForm();\r\n  }\r\n\r\n  resetNewUserForm() {\r\n    this.newUser = {\r\n      fullName: '',\r\n      email: '',\r\n      role: 'student'\r\n    };\r\n    this.creatingUser = false;\r\n  }\r\n\r\n  onCreateUser(form: any) {\r\n    if (form.invalid || this.creatingUser) {\r\n      return;\r\n    }\r\n\r\n    this.creatingUser = true;\r\n    this.error = '';\r\n    this.message = '';\r\n\r\n    const token = localStorage.getItem('token');\r\n\r\n    this.authService.createUser(this.newUser, token!).subscribe({\r\n      next: (res: any) => {\r\n        // Show success message\r\n        this.message = res.message;\r\n        this.error = '';\r\n\r\n        // Add new user to the lists\r\n        this.users.push(res.user);\r\n        this.filteredUsers = [...this.users];\r\n        this.applyFilters();\r\n\r\n        // Show success toast with credentials info\r\n        this.toastService.showSuccess(\r\n          `User created successfully! Login credentials sent to ${res.user.email}`\r\n        );\r\n\r\n        // Close modal and reset form\r\n        this.closeCreateUserModal();\r\n\r\n        // Auto-hide message after 5 seconds\r\n        setTimeout(() => {\r\n          this.message = '';\r\n        }, 5000);\r\n      },\r\n      error: (err) => {\r\n        this.error = err.error?.message || 'Failed to create user';\r\n        this.message = '';\r\n        this.creatingUser = false;\r\n\r\n        // Show error toast\r\n        this.toastService.showError(this.error);\r\n\r\n        // Auto-hide error after 3 seconds\r\n        setTimeout(() => {\r\n          this.error = '';\r\n        }, 3000);\r\n      },\r\n    });\r\n  }\r\n\r\n  // Enhanced dashboard methods\r\n  updateSystemStats() {\r\n    const now = new Date();\r\n    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\r\n    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);\r\n\r\n    this.systemStats = {\r\n      totalUsers: this.users.length,\r\n      activeUsers: this.users.filter(u => u.isActive !== false).length,\r\n      newUsersToday: this.users.filter(u => {\r\n        const userDate = new Date(u.createdAt);\r\n        return userDate >= today;\r\n      }).length,\r\n      newUsersThisWeek: this.users.filter(u => {\r\n        const userDate = new Date(u.createdAt);\r\n        return userDate >= weekAgo;\r\n      }).length,\r\n      totalProjects: 0, // Will be updated when project data is available\r\n      activeProjects: 0,\r\n      totalTeams: 0,\r\n      systemUptime: '99.9%'\r\n    };\r\n  }\r\n\r\n  loadRecentActivities() {\r\n    // Mock recent activities - in real app, this would come from an API\r\n    this.recentActivities = [\r\n      {\r\n        id: 1,\r\n        type: 'user_created',\r\n        message: 'New user registered',\r\n        user: 'John Doe',\r\n        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago\r\n        icon: 'fas fa-user-plus',\r\n        color: 'text-green-600'\r\n      },\r\n      {\r\n        id: 2,\r\n        type: 'user_login',\r\n        message: 'User logged in',\r\n        user: 'Jane Smith',\r\n        timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago\r\n        icon: 'fas fa-sign-in-alt',\r\n        color: 'text-blue-600'\r\n      },\r\n      {\r\n        id: 3,\r\n        type: 'role_changed',\r\n        message: 'User role updated to Teacher',\r\n        user: 'Mike Johnson',\r\n        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago\r\n        icon: 'fas fa-user-tag',\r\n        color: 'text-purple-600'\r\n      }\r\n    ];\r\n  }\r\n\r\n  // Advanced filtering and sorting\r\n  applyAdvancedFilters() {\r\n    let filtered = [...this.users];\r\n\r\n    // Apply role filter\r\n    if (this.selectedRole) {\r\n      filtered = filtered.filter(user => user.role === this.selectedRole);\r\n    }\r\n\r\n    // Apply status filter\r\n    if (this.selectedStatus) {\r\n      if (this.selectedStatus === 'active') {\r\n        filtered = filtered.filter(user => user.isActive !== false);\r\n      } else if (this.selectedStatus === 'inactive') {\r\n        filtered = filtered.filter(user => user.isActive === false);\r\n      }\r\n    }\r\n\r\n    // Apply verification filter\r\n    if (this.advancedFilters.verified) {\r\n      const isVerified = this.advancedFilters.verified === 'true';\r\n      filtered = filtered.filter(user => user.verified === isVerified);\r\n    }\r\n\r\n    // Apply date range filter\r\n    if (this.advancedFilters.dateFrom) {\r\n      const fromDate = new Date(this.advancedFilters.dateFrom);\r\n      filtered = filtered.filter(user => new Date(user.createdAt) >= fromDate);\r\n    }\r\n\r\n    if (this.advancedFilters.dateTo) {\r\n      const toDate = new Date(this.advancedFilters.dateTo);\r\n      filtered = filtered.filter(user => new Date(user.createdAt) <= toDate);\r\n    }\r\n\r\n    // Apply search term\r\n    if (this.searchTerm.trim()) {\r\n      const term = this.searchTerm.toLowerCase().trim();\r\n      filtered = filtered.filter(user =>\r\n        user.fullName.toLowerCase().includes(term) ||\r\n        user.email.toLowerCase().includes(term) ||\r\n        user.role.toLowerCase().includes(term)\r\n      );\r\n    }\r\n\r\n    // Apply sorting\r\n    filtered.sort((a, b) => {\r\n      let aValue = a[this.sortBy];\r\n      let bValue = b[this.sortBy];\r\n\r\n      if (typeof aValue === 'string') {\r\n        aValue = aValue.toLowerCase();\r\n        bValue = bValue.toLowerCase();\r\n      }\r\n\r\n      if (this.sortOrder === 'asc') {\r\n        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\r\n      } else {\r\n        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\r\n      }\r\n    });\r\n\r\n    this.filteredUsers = filtered;\r\n  }\r\n\r\n  // Bulk operations\r\n  toggleUserSelection(userId: string) {\r\n    if (this.selectedUsers.has(userId)) {\r\n      this.selectedUsers.delete(userId);\r\n    } else {\r\n      this.selectedUsers.add(userId);\r\n    }\r\n    this.showBulkActions = this.selectedUsers.size > 0;\r\n  }\r\n\r\n  selectAllUsers() {\r\n    if (this.selectedUsers.size === this.filteredUsers.length) {\r\n      this.selectedUsers.clear();\r\n    } else {\r\n      this.filteredUsers.forEach(user => this.selectedUsers.add(user._id));\r\n    }\r\n    this.showBulkActions = this.selectedUsers.size > 0;\r\n  }\r\n\r\n  bulkDeleteUsers() {\r\n    if (this.selectedUsers.size === 0) return;\r\n\r\n    const confirmDelete = confirm(`Are you sure you want to delete ${this.selectedUsers.size} users?`);\r\n    if (!confirmDelete) return;\r\n\r\n    const token = localStorage.getItem('token');\r\n    const userIds = Array.from(this.selectedUsers);\r\n\r\n    // For now, delete users one by one (in real app, implement bulk delete API)\r\n    let deletedCount = 0;\r\n    userIds.forEach(userId => {\r\n      this.authService.deleteUser(userId, token!).subscribe({\r\n        next: () => {\r\n          deletedCount++;\r\n          this.users = this.users.filter(u => u._id !== userId);\r\n          if (deletedCount === userIds.length) {\r\n            this.selectedUsers.clear();\r\n            this.showBulkActions = false;\r\n            this.applyAdvancedFilters();\r\n            this.toastService.showSuccess(`${deletedCount} users deleted successfully`);\r\n          }\r\n        },\r\n        error: (err) => {\r\n          this.toastService.showError(`Failed to delete some users: ${err.error?.message}`);\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n  bulkChangeRole(newRole: string) {\r\n    if (this.selectedUsers.size === 0) return;\r\n\r\n    const confirmChange = confirm(`Are you sure you want to change the role of ${this.selectedUsers.size} users to ${newRole}?`);\r\n    if (!confirmChange) return;\r\n\r\n    const token = localStorage.getItem('token');\r\n    const userIds = Array.from(this.selectedUsers);\r\n\r\n    let updatedCount = 0;\r\n    userIds.forEach(userId => {\r\n      this.authService.updateUserRole(userId, newRole, token!).subscribe({\r\n        next: () => {\r\n          updatedCount++;\r\n          const userIndex = this.users.findIndex(u => u._id === userId);\r\n          if (userIndex !== -1) {\r\n            this.users[userIndex].role = newRole;\r\n          }\r\n          if (updatedCount === userIds.length) {\r\n            this.selectedUsers.clear();\r\n            this.showBulkActions = false;\r\n            this.applyAdvancedFilters();\r\n            this.toastService.showSuccess(`${updatedCount} users updated successfully`);\r\n          }\r\n        },\r\n        error: (err) => {\r\n          this.toastService.showError(`Failed to update some users: ${err.error?.message}`);\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n  // Export functionality\r\n  exportUsers(format: 'csv' | 'json') {\r\n    const dataToExport = this.filteredUsers.map(user => ({\r\n      fullName: user.fullName,\r\n      email: user.email,\r\n      role: user.role,\r\n      isActive: user.isActive,\r\n      verified: user.verified,\r\n      createdAt: user.createdAt,\r\n      group: user.group?.name || 'No Group'\r\n    }));\r\n\r\n    if (format === 'csv') {\r\n      this.exportToCSV(dataToExport);\r\n    } else {\r\n      this.exportToJSON(dataToExport);\r\n    }\r\n  }\r\n\r\n  private exportToCSV(data: any[]) {\r\n    const headers = Object.keys(data[0]);\r\n    const csvContent = [\r\n      headers.join(','),\r\n      ...data.map(row => headers.map(header => `\"${row[header]}\"`).join(','))\r\n    ].join('\\n');\r\n\r\n    this.downloadFile(csvContent, 'users.csv', 'text/csv');\r\n  }\r\n\r\n  private exportToJSON(data: any[]) {\r\n    const jsonContent = JSON.stringify(data, null, 2);\r\n    this.downloadFile(jsonContent, 'users.json', 'application/json');\r\n  }\r\n\r\n  private downloadFile(content: string, filename: string, contentType: string) {\r\n    const blob = new Blob([content], { type: contentType });\r\n    const url = window.URL.createObjectURL(blob);\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = filename;\r\n    link.click();\r\n    window.URL.revokeObjectURL(url);\r\n  }\r\n\r\n  // Utility methods\r\n  toggleAdvancedSearch() {\r\n    this.showAdvancedSearch = !this.showAdvancedSearch;\r\n  }\r\n\r\n  clearAdvancedFilters() {\r\n    this.advancedFilters = {\r\n      dateFrom: '',\r\n      dateTo: '',\r\n      verified: '',\r\n      hasGroup: ''\r\n    };\r\n    this.selectedRole = '';\r\n    this.selectedStatus = '';\r\n    this.applyAdvancedFilters();\r\n  }\r\n\r\n  setSortBy(field: string) {\r\n    if (this.sortBy === field) {\r\n      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';\r\n    } else {\r\n      this.sortBy = field;\r\n      this.sortOrder = 'asc';\r\n    }\r\n    this.applyAdvancedFilters();\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AAU1C,WAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAqD7BC,YACUC,WAAwB,EACxBC,MAAc,EACdC,YAA0B;IAF1B,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IAvDtB,KAAAC,KAAK,GAAU,EAAE;IACjB,KAAAC,KAAK,GAAG,EAAE;IACV,KAAAC,OAAO,GAAG,EAAE;IACZ,KAAAC,KAAK,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC;IACvC,KAAAC,OAAO,GAAG,IAAI;IACd,KAAAC,WAAW,GAAQ,IAAI;IACvB,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,aAAa,GAAU,EAAE;IAEzB;IACA,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,OAAO,GAAG;MACRC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE;KACP;IAED;IACA,KAAAC,WAAW,GAAG;MACZC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;MACdC,aAAa,EAAE,CAAC;MAChBC,gBAAgB,EAAE,CAAC;MACnBC,aAAa,EAAE,CAAC;MAChBC,cAAc,EAAE,CAAC;MACjBC,UAAU,EAAE,CAAC;MACbC,YAAY,EAAE;KACf;IAED;IACA,KAAAC,gBAAgB,GAAU,EAAE;IAE5B;IACA,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,MAAM,GAAG,UAAU;IACnB,KAAAC,SAAS,GAAG,KAAK;IAEjB;IACA,KAAAC,aAAa,GAAgB,IAAIC,GAAG,EAAE;IACtC,KAAAC,eAAe,GAAG,KAAK;IAEvB;IACA,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,eAAe,GAAG;MAChBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;KACX;EAME;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,IAAI,CAAClC,OAAO,GAAG,IAAI;IACnB,MAAMmC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAE5C,IAAI,CAACF,KAAK,IAAI,CAACG,OAAO,EAAE;MACtB,IAAI,CAAC5C,MAAM,CAAC6C,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;MACtC;;IAGF,IAAI,CAACtC,WAAW,GAAGuC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;IAEtC;IACA,IAAI,IAAI,CAACrC,WAAW,CAACQ,IAAI,KAAK,OAAO,EAAE;MACrC,IAAI,CAACf,MAAM,CAAC6C,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MAC3B;;IAGF,IAAI,CAAC9C,WAAW,CAACiD,WAAW,CAACP,KAAK,CAAC,CAACQ,SAAS,CAAC;MAC5CC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACjD,KAAK,GAAGiD,GAAG;QAChB,IAAI,CAAC1C,aAAa,GAAG,CAAC,GAAG,IAAI,CAACP,KAAK,CAAC;QACpC,IAAI,CAACkD,iBAAiB,EAAE;QACxB,IAAI,CAACC,oBAAoB,EAAE;QAC3B,IAAI,CAAC/C,OAAO,GAAG,KAAK;MACtB,CAAC;MACDH,KAAK,EAAGmD,GAAG,IAAI;QACb,IAAI,CAACnD,KAAK,GAAGmD,GAAG,CAACnD,KAAK,EAAEC,OAAO,IAAI,uBAAuB;QAC1D,IAAI,CAACE,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EACAiD,WAAWA,CAAA;IACT,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACjD,UAAU,GAAG,EAAE;IACpB,IAAI,CAACgD,oBAAoB,EAAE;EAC7B;EAEAE,YAAYA,CAACC,MAAc,EAAEC,OAAe;IAC1C,MAAMnB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAAC5C,WAAW,CAAC8D,cAAc,CAACF,MAAM,EAAEC,OAAO,EAAEnB,KAAM,CAAC,CAACQ,SAAS,CAAC;MACjEC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAC/C,OAAO,GAAG+C,GAAG,CAAC/C,OAAO;QAC1B,IAAI,CAACD,KAAK,GAAG,EAAE;QAEf;QACA,MAAM2D,SAAS,GAAG,IAAI,CAAC5D,KAAK,CAAC6D,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAACC,GAAG,KAAKN,MAAM,CAAC;QAC/D,IAAIG,SAAS,KAAK,CAAC,CAAC,EAAE;UACpB,IAAI,CAAC5D,KAAK,CAAC4D,SAAS,CAAC,CAAC/C,IAAI,GAAG6C,OAAO;;QAGtC,MAAMM,aAAa,GAAG,IAAI,CAACzD,aAAa,CAACsD,SAAS,CAC/CC,CAAC,IAAKA,CAAC,CAACC,GAAG,KAAKN,MAAM,CACxB;QACD,IAAIO,aAAa,KAAK,CAAC,CAAC,EAAE;UACxB,IAAI,CAACzD,aAAa,CAACyD,aAAa,CAAC,CAACnD,IAAI,GAAG6C,OAAO;;QAGlD;QACAO,UAAU,CAAC,MAAK;UACd,IAAI,CAAC/D,OAAO,GAAG,EAAE;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDD,KAAK,EAAGmD,GAAG,IAAI;QACb,IAAI,CAACnD,KAAK,GAAGmD,GAAG,CAACnD,KAAK,EAAEC,OAAO,IAAI,uBAAuB;QAC1D,IAAI,CAACA,OAAO,GAAG,EAAE;QAEjB;QACA+D,UAAU,CAAC,MAAK;UACd,IAAI,CAAChE,KAAK,GAAG,EAAE;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACJ;EACAiE,YAAYA,CAACT,MAAc;IACzB,MAAMU,aAAa,GAAGC,OAAO,CAAC,4CAA4C,CAAC;IAC3E,IAAI,CAACD,aAAa,EAAE;IAEpB,MAAM5B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAAC5C,WAAW,CAACwE,UAAU,CAACZ,MAAM,EAAElB,KAAM,CAAC,CAACQ,SAAS,CAAC;MACpDC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAC/C,OAAO,GAAG+C,GAAG,CAAC/C,OAAO;QAC1B,IAAI,CAACD,KAAK,GAAG,EAAE;QAEf;QACA,IAAI,CAACD,KAAK,GAAG,IAAI,CAACA,KAAK,CAACsE,MAAM,CAAER,CAAC,IAAKA,CAAC,CAACC,GAAG,KAAKN,MAAM,CAAC;QACvD,IAAI,CAAClD,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC+D,MAAM,CAAER,CAAC,IAAKA,CAAC,CAACC,GAAG,KAAKN,MAAM,CAAC;QAEvE;QACAQ,UAAU,CAAC,MAAK;UACd,IAAI,CAAC/D,OAAO,GAAG,EAAE;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDD,KAAK,EAAGmD,GAAG,IAAI;QACb,IAAI,CAACnD,KAAK,GAAGmD,GAAG,CAACnD,KAAK,EAAEC,OAAO,IAAI,uBAAuB;QAC1D,IAAI,CAACA,OAAO,GAAG,EAAE;QAEjB;QACA+D,UAAU,CAAC,MAAK;UACd,IAAI,CAAChE,KAAK,GAAG,EAAE;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACJ;EACAsE,oBAAoBA,CAACd,MAAc,EAAEe,aAAsB;IACzD,MAAMC,SAAS,GAAG,CAACD,aAAa;IAChC,MAAME,MAAM,GAAGD,SAAS,GAAG,UAAU,GAAG,YAAY;IAEpD;IACA,MAAME,IAAI,GAAG,IAAI,CAAC3E,KAAK,CAAC4E,IAAI,CAACd,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKN,MAAM,CAAC;IACnD,MAAMoB,QAAQ,GAAGF,IAAI,EAAEhE,QAAQ,IAAIgE,IAAI,EAAEG,SAAS,IAAI,MAAM;IAE5D,MAAMC,aAAa,GAAGX,OAAO,CAC3B,4BAA4BM,MAAM,IAAIG,QAAQ,GAAG,CAClD;IACD,IAAI,CAACE,aAAa,EAAE;IAEpB,MAAMxC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAAC5C,WAAW,CAAC0E,oBAAoB,CAACd,MAAM,EAAEgB,SAAS,EAAElC,KAAM,CAAC,CAACQ,SAAS,CAAC;MACzEC,IAAI,EAAGC,GAAQ,IAAI;QACjB,MAAM+B,UAAU,GAAGP,SAAS,GAAG,WAAW,GAAG,aAAa;QAC1D,MAAMQ,cAAc,GAAG,GAAGJ,QAAQ,aAAaG,UAAU,eAAe;QAExE;QACA,IAAI,CAACjF,YAAY,CAACmF,WAAW,CAACD,cAAc,CAAC;QAE7C;QACA,IAAI,CAAC/E,OAAO,GAAG,EAAE;QACjB,IAAI,CAACD,KAAK,GAAG,EAAE;QAEf;QACA,MAAM2D,SAAS,GAAG,IAAI,CAAC5D,KAAK,CAAC6D,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAACC,GAAG,KAAKN,MAAM,CAAC;QAC/D,IAAIG,SAAS,KAAK,CAAC,CAAC,EAAE;UACpB,IAAI,CAAC5D,KAAK,CAAC4D,SAAS,CAAC,CAACuB,QAAQ,GAAGV,SAAS;;QAG5C,MAAMT,aAAa,GAAG,IAAI,CAACzD,aAAa,CAACsD,SAAS,CAC/CC,CAAC,IAAKA,CAAC,CAACC,GAAG,KAAKN,MAAM,CACxB;QACD,IAAIO,aAAa,KAAK,CAAC,CAAC,EAAE;UACxB,IAAI,CAACzD,aAAa,CAACyD,aAAa,CAAC,CAACmB,QAAQ,GAAGV,SAAS;;QAGxD;QACA,IAAI,CAACW,YAAY,EAAE;MACrB,CAAC;MACDnF,KAAK,EAAGmD,GAAG,IAAI;QACb,MAAM4B,UAAU,GAAGP,SAAS,GAAG,UAAU,GAAG,YAAY;QACxD,MAAMY,YAAY,GAAGjC,GAAG,CAACnD,KAAK,EAAEC,OAAO,IAAI,aAAa8E,UAAU,IAAIH,QAAQ,EAAE;QAEhF;QACA,IAAI,CAAC9E,YAAY,CAACuF,SAAS,CAACD,YAAY,CAAC;QAEzC;QACA,IAAI,CAACnF,OAAO,GAAG,EAAE;QACjB,IAAI,CAACD,KAAK,GAAG,EAAE;MACjB;KACD,CAAC;EACJ;EACAsF,eAAeA,CAAA;IACb,OAAO,IAAI,CAACvF,KAAK,CAACsE,MAAM,CAAER,CAAC,IAAKA,CAAC,CAACjD,IAAI,KAAK,SAAS,CAAC,CAAC2E,MAAM;EAC9D;EACAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACzF,KAAK,CAACsE,MAAM,CAAER,CAAC,IAAKA,CAAC,CAACjD,IAAI,KAAK,SAAS,CAAC,CAAC2E,MAAM;EAC9D;EACAE,aAAaA,CAAA;IACX,OAAO,IAAI,CAAC1F,KAAK,CAACsE,MAAM,CAAER,CAAC,IAAKA,CAAC,CAACjD,IAAI,KAAK,OAAO,CAAC,CAAC2E,MAAM;EAC5D;EACAG,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC3F,KAAK,CAACsE,MAAM,CAAER,CAAC,IAAKA,CAAC,CAACqB,QAAQ,KAAK,KAAK,CAAC,CAACK,MAAM;EAC9D;EACAI,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC5F,KAAK,CAACsE,MAAM,CAAER,CAAC,IAAKA,CAAC,CAACqB,QAAQ,KAAK,KAAK,CAAC,CAACK,MAAM;EAC9D;EAEAK,MAAMA,CAAA;IACJ,IAAI,CAAChG,WAAW,CAACgG,MAAM,EAAE;IACzB,IAAI,CAAC/F,MAAM,CAAC6C,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;EACxC;EAEAmD,eAAeA,CAACrC,MAAc;IAC5B,IAAI,CAAC3D,MAAM,CAAC6C,QAAQ,CAAC,CAAC,oBAAoB,EAAEc,MAAM,CAAC,CAAC;EACtD;EAEA2B,YAAYA,CAAA;IACV,IAAI,CAAC9B,oBAAoB,EAAE;EAC7B;EAEA;EACAyC,mBAAmBA,CAAA;IACjB,IAAI,CAACvF,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACwF,gBAAgB,EAAE;EACzB;EAEAC,oBAAoBA,CAAA;IAClB,IAAI,CAACzF,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACwF,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACtF,OAAO,GAAG;MACbC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE;KACP;IACD,IAAI,CAACJ,YAAY,GAAG,KAAK;EAC3B;EAEAyF,YAAYA,CAACC,IAAS;IACpB,IAAIA,IAAI,CAACC,OAAO,IAAI,IAAI,CAAC3F,YAAY,EAAE;MACrC;;IAGF,IAAI,CAACA,YAAY,GAAG,IAAI;IACxB,IAAI,CAACR,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,OAAO,GAAG,EAAE;IAEjB,MAAMqC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI,CAAC5C,WAAW,CAACwG,UAAU,CAAC,IAAI,CAAC3F,OAAO,EAAE6B,KAAM,CAAC,CAACQ,SAAS,CAAC;MAC1DC,IAAI,EAAGC,GAAQ,IAAI;QACjB;QACA,IAAI,CAAC/C,OAAO,GAAG+C,GAAG,CAAC/C,OAAO;QAC1B,IAAI,CAACD,KAAK,GAAG,EAAE;QAEf;QACA,IAAI,CAACD,KAAK,CAACsG,IAAI,CAACrD,GAAG,CAAC0B,IAAI,CAAC;QACzB,IAAI,CAACpE,aAAa,GAAG,CAAC,GAAG,IAAI,CAACP,KAAK,CAAC;QACpC,IAAI,CAACoF,YAAY,EAAE;QAEnB;QACA,IAAI,CAACrF,YAAY,CAACmF,WAAW,CAC3B,wDAAwDjC,GAAG,CAAC0B,IAAI,CAAC/D,KAAK,EAAE,CACzE;QAED;QACA,IAAI,CAACqF,oBAAoB,EAAE;QAE3B;QACAhC,UAAU,CAAC,MAAK;UACd,IAAI,CAAC/D,OAAO,GAAG,EAAE;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDD,KAAK,EAAGmD,GAAG,IAAI;QACb,IAAI,CAACnD,KAAK,GAAGmD,GAAG,CAACnD,KAAK,EAAEC,OAAO,IAAI,uBAAuB;QAC1D,IAAI,CAACA,OAAO,GAAG,EAAE;QACjB,IAAI,CAACO,YAAY,GAAG,KAAK;QAEzB;QACA,IAAI,CAACV,YAAY,CAACuF,SAAS,CAAC,IAAI,CAACrF,KAAK,CAAC;QAEvC;QACAgE,UAAU,CAAC,MAAK;UACd,IAAI,CAAChE,KAAK,GAAG,EAAE;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACJ;EAEA;EACAiD,iBAAiBA,CAAA;IACf,MAAMqD,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,KAAK,GAAG,IAAID,IAAI,CAACD,GAAG,CAACG,WAAW,EAAE,EAAEH,GAAG,CAACI,QAAQ,EAAE,EAAEJ,GAAG,CAACK,OAAO,EAAE,CAAC;IACxE,MAAMC,OAAO,GAAG,IAAIL,IAAI,CAACC,KAAK,CAACK,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAEnE,IAAI,CAAChG,WAAW,GAAG;MACjBC,UAAU,EAAE,IAAI,CAACf,KAAK,CAACwF,MAAM;MAC7BxE,WAAW,EAAE,IAAI,CAAChB,KAAK,CAACsE,MAAM,CAACR,CAAC,IAAIA,CAAC,CAACqB,QAAQ,KAAK,KAAK,CAAC,CAACK,MAAM;MAChEvE,aAAa,EAAE,IAAI,CAACjB,KAAK,CAACsE,MAAM,CAACR,CAAC,IAAG;QACnC,MAAMiD,QAAQ,GAAG,IAAIP,IAAI,CAAC1C,CAAC,CAACkD,SAAS,CAAC;QACtC,OAAOD,QAAQ,IAAIN,KAAK;MAC1B,CAAC,CAAC,CAACjB,MAAM;MACTtE,gBAAgB,EAAE,IAAI,CAAClB,KAAK,CAACsE,MAAM,CAACR,CAAC,IAAG;QACtC,MAAMiD,QAAQ,GAAG,IAAIP,IAAI,CAAC1C,CAAC,CAACkD,SAAS,CAAC;QACtC,OAAOD,QAAQ,IAAIF,OAAO;MAC5B,CAAC,CAAC,CAACrB,MAAM;MACTrE,aAAa,EAAE,CAAC;MAChBC,cAAc,EAAE,CAAC;MACjBC,UAAU,EAAE,CAAC;MACbC,YAAY,EAAE;KACf;EACH;EAEA6B,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAAC5B,gBAAgB,GAAG,CACtB;MACE0F,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,cAAc;MACpBhH,OAAO,EAAE,qBAAqB;MAC9ByE,IAAI,EAAE,UAAU;MAChBwC,SAAS,EAAE,IAAIX,IAAI,CAACA,IAAI,CAACD,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAChDa,IAAI,EAAE,kBAAkB;MACxBC,KAAK,EAAE;KACR,EACD;MACEJ,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,YAAY;MAClBhH,OAAO,EAAE,gBAAgB;MACzByE,IAAI,EAAE,YAAY;MAClBwC,SAAS,EAAE,IAAIX,IAAI,CAACA,IAAI,CAACD,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAChDa,IAAI,EAAE,oBAAoB;MAC1BC,KAAK,EAAE;KACR,EACD;MACEJ,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,cAAc;MACpBhH,OAAO,EAAE,8BAA8B;MACvCyE,IAAI,EAAE,cAAc;MACpBwC,SAAS,EAAE,IAAIX,IAAI,CAACA,IAAI,CAACD,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;MACpDa,IAAI,EAAE,iBAAiB;MACvBC,KAAK,EAAE;KACR,CACF;EACH;EAEA;EACA/D,oBAAoBA,CAAA;IAClB,IAAIgE,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACtH,KAAK,CAAC;IAE9B;IACA,IAAI,IAAI,CAACwB,YAAY,EAAE;MACrB8F,QAAQ,GAAGA,QAAQ,CAAChD,MAAM,CAACK,IAAI,IAAIA,IAAI,CAAC9D,IAAI,KAAK,IAAI,CAACW,YAAY,CAAC;;IAGrE;IACA,IAAI,IAAI,CAACC,cAAc,EAAE;MACvB,IAAI,IAAI,CAACA,cAAc,KAAK,QAAQ,EAAE;QACpC6F,QAAQ,GAAGA,QAAQ,CAAChD,MAAM,CAACK,IAAI,IAAIA,IAAI,CAACQ,QAAQ,KAAK,KAAK,CAAC;OAC5D,MAAM,IAAI,IAAI,CAAC1D,cAAc,KAAK,UAAU,EAAE;QAC7C6F,QAAQ,GAAGA,QAAQ,CAAChD,MAAM,CAACK,IAAI,IAAIA,IAAI,CAACQ,QAAQ,KAAK,KAAK,CAAC;;;IAI/D;IACA,IAAI,IAAI,CAACnD,eAAe,CAACG,QAAQ,EAAE;MACjC,MAAMoF,UAAU,GAAG,IAAI,CAACvF,eAAe,CAACG,QAAQ,KAAK,MAAM;MAC3DmF,QAAQ,GAAGA,QAAQ,CAAChD,MAAM,CAACK,IAAI,IAAIA,IAAI,CAACxC,QAAQ,KAAKoF,UAAU,CAAC;;IAGlE;IACA,IAAI,IAAI,CAACvF,eAAe,CAACC,QAAQ,EAAE;MACjC,MAAMuF,QAAQ,GAAG,IAAIhB,IAAI,CAAC,IAAI,CAACxE,eAAe,CAACC,QAAQ,CAAC;MACxDqF,QAAQ,GAAGA,QAAQ,CAAChD,MAAM,CAACK,IAAI,IAAI,IAAI6B,IAAI,CAAC7B,IAAI,CAACqC,SAAS,CAAC,IAAIQ,QAAQ,CAAC;;IAG1E,IAAI,IAAI,CAACxF,eAAe,CAACE,MAAM,EAAE;MAC/B,MAAMuF,MAAM,GAAG,IAAIjB,IAAI,CAAC,IAAI,CAACxE,eAAe,CAACE,MAAM,CAAC;MACpDoF,QAAQ,GAAGA,QAAQ,CAAChD,MAAM,CAACK,IAAI,IAAI,IAAI6B,IAAI,CAAC7B,IAAI,CAACqC,SAAS,CAAC,IAAIS,MAAM,CAAC;;IAGxE;IACA,IAAI,IAAI,CAACnH,UAAU,CAACoH,IAAI,EAAE,EAAE;MAC1B,MAAMC,IAAI,GAAG,IAAI,CAACrH,UAAU,CAACsH,WAAW,EAAE,CAACF,IAAI,EAAE;MACjDJ,QAAQ,GAAGA,QAAQ,CAAChD,MAAM,CAACK,IAAI,IAC7BA,IAAI,CAAChE,QAAQ,CAACiH,WAAW,EAAE,CAACC,QAAQ,CAACF,IAAI,CAAC,IAC1ChD,IAAI,CAAC/D,KAAK,CAACgH,WAAW,EAAE,CAACC,QAAQ,CAACF,IAAI,CAAC,IACvChD,IAAI,CAAC9D,IAAI,CAAC+G,WAAW,EAAE,CAACC,QAAQ,CAACF,IAAI,CAAC,CACvC;;IAGH;IACAL,QAAQ,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACrB,IAAIC,MAAM,GAAGF,CAAC,CAAC,IAAI,CAACrG,MAAM,CAAC;MAC3B,IAAIwG,MAAM,GAAGF,CAAC,CAAC,IAAI,CAACtG,MAAM,CAAC;MAE3B,IAAI,OAAOuG,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,GAAGA,MAAM,CAACL,WAAW,EAAE;QAC7BM,MAAM,GAAGA,MAAM,CAACN,WAAW,EAAE;;MAG/B,IAAI,IAAI,CAACjG,SAAS,KAAK,KAAK,EAAE;QAC5B,OAAOsG,MAAM,GAAGC,MAAM,GAAG,CAAC,CAAC,GAAGD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC;OACtD,MAAM;QACL,OAAOD,MAAM,GAAGC,MAAM,GAAG,CAAC,CAAC,GAAGD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC;;IAEzD,CAAC,CAAC;IAEF,IAAI,CAAC3H,aAAa,GAAG+G,QAAQ;EAC/B;EAEA;EACAa,mBAAmBA,CAAC1E,MAAc;IAChC,IAAI,IAAI,CAAC7B,aAAa,CAACwG,GAAG,CAAC3E,MAAM,CAAC,EAAE;MAClC,IAAI,CAAC7B,aAAa,CAACyG,MAAM,CAAC5E,MAAM,CAAC;KAClC,MAAM;MACL,IAAI,CAAC7B,aAAa,CAAC0G,GAAG,CAAC7E,MAAM,CAAC;;IAEhC,IAAI,CAAC3B,eAAe,GAAG,IAAI,CAACF,aAAa,CAAC2G,IAAI,GAAG,CAAC;EACpD;EAEAC,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC5G,aAAa,CAAC2G,IAAI,KAAK,IAAI,CAAChI,aAAa,CAACiF,MAAM,EAAE;MACzD,IAAI,CAAC5D,aAAa,CAAC6G,KAAK,EAAE;KAC3B,MAAM;MACL,IAAI,CAAClI,aAAa,CAACmI,OAAO,CAAC/D,IAAI,IAAI,IAAI,CAAC/C,aAAa,CAAC0G,GAAG,CAAC3D,IAAI,CAACZ,GAAG,CAAC,CAAC;;IAEtE,IAAI,CAACjC,eAAe,GAAG,IAAI,CAACF,aAAa,CAAC2G,IAAI,GAAG,CAAC;EACpD;EAEAI,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC/G,aAAa,CAAC2G,IAAI,KAAK,CAAC,EAAE;IAEnC,MAAMpE,aAAa,GAAGC,OAAO,CAAC,mCAAmC,IAAI,CAACxC,aAAa,CAAC2G,IAAI,SAAS,CAAC;IAClG,IAAI,CAACpE,aAAa,EAAE;IAEpB,MAAM5B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMmG,OAAO,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAClH,aAAa,CAAC;IAE9C;IACA,IAAImH,YAAY,GAAG,CAAC;IACpBH,OAAO,CAACF,OAAO,CAACjF,MAAM,IAAG;MACvB,IAAI,CAAC5D,WAAW,CAACwE,UAAU,CAACZ,MAAM,EAAElB,KAAM,CAAC,CAACQ,SAAS,CAAC;QACpDC,IAAI,EAAEA,CAAA,KAAK;UACT+F,YAAY,EAAE;UACd,IAAI,CAAC/I,KAAK,GAAG,IAAI,CAACA,KAAK,CAACsE,MAAM,CAACR,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKN,MAAM,CAAC;UACrD,IAAIsF,YAAY,KAAKH,OAAO,CAACpD,MAAM,EAAE;YACnC,IAAI,CAAC5D,aAAa,CAAC6G,KAAK,EAAE;YAC1B,IAAI,CAAC3G,eAAe,GAAG,KAAK;YAC5B,IAAI,CAACwB,oBAAoB,EAAE;YAC3B,IAAI,CAACvD,YAAY,CAACmF,WAAW,CAAC,GAAG6D,YAAY,6BAA6B,CAAC;;QAE/E,CAAC;QACD9I,KAAK,EAAGmD,GAAG,IAAI;UACb,IAAI,CAACrD,YAAY,CAACuF,SAAS,CAAC,gCAAgClC,GAAG,CAACnD,KAAK,EAAEC,OAAO,EAAE,CAAC;QACnF;OACD,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA8I,cAAcA,CAACtF,OAAe;IAC5B,IAAI,IAAI,CAAC9B,aAAa,CAAC2G,IAAI,KAAK,CAAC,EAAE;IAEnC,MAAMU,aAAa,GAAG7E,OAAO,CAAC,+CAA+C,IAAI,CAACxC,aAAa,CAAC2G,IAAI,aAAa7E,OAAO,GAAG,CAAC;IAC5H,IAAI,CAACuF,aAAa,EAAE;IAEpB,MAAM1G,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMmG,OAAO,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAClH,aAAa,CAAC;IAE9C,IAAIsH,YAAY,GAAG,CAAC;IACpBN,OAAO,CAACF,OAAO,CAACjF,MAAM,IAAG;MACvB,IAAI,CAAC5D,WAAW,CAAC8D,cAAc,CAACF,MAAM,EAAEC,OAAO,EAAEnB,KAAM,CAAC,CAACQ,SAAS,CAAC;QACjEC,IAAI,EAAEA,CAAA,KAAK;UACTkG,YAAY,EAAE;UACd,MAAMtF,SAAS,GAAG,IAAI,CAAC5D,KAAK,CAAC6D,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKN,MAAM,CAAC;UAC7D,IAAIG,SAAS,KAAK,CAAC,CAAC,EAAE;YACpB,IAAI,CAAC5D,KAAK,CAAC4D,SAAS,CAAC,CAAC/C,IAAI,GAAG6C,OAAO;;UAEtC,IAAIwF,YAAY,KAAKN,OAAO,CAACpD,MAAM,EAAE;YACnC,IAAI,CAAC5D,aAAa,CAAC6G,KAAK,EAAE;YAC1B,IAAI,CAAC3G,eAAe,GAAG,KAAK;YAC5B,IAAI,CAACwB,oBAAoB,EAAE;YAC3B,IAAI,CAACvD,YAAY,CAACmF,WAAW,CAAC,GAAGgE,YAAY,6BAA6B,CAAC;;QAE/E,CAAC;QACDjJ,KAAK,EAAGmD,GAAG,IAAI;UACb,IAAI,CAACrD,YAAY,CAACuF,SAAS,CAAC,gCAAgClC,GAAG,CAACnD,KAAK,EAAEC,OAAO,EAAE,CAAC;QACnF;OACD,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;EACAiJ,WAAWA,CAACC,MAAsB;IAChC,MAAMC,YAAY,GAAG,IAAI,CAAC9I,aAAa,CAAC+I,GAAG,CAAC3E,IAAI,KAAK;MACnDhE,QAAQ,EAAEgE,IAAI,CAAChE,QAAQ;MACvBC,KAAK,EAAE+D,IAAI,CAAC/D,KAAK;MACjBC,IAAI,EAAE8D,IAAI,CAAC9D,IAAI;MACfsE,QAAQ,EAAER,IAAI,CAACQ,QAAQ;MACvBhD,QAAQ,EAAEwC,IAAI,CAACxC,QAAQ;MACvB6E,SAAS,EAAErC,IAAI,CAACqC,SAAS;MACzBuC,KAAK,EAAE5E,IAAI,CAAC4E,KAAK,EAAEC,IAAI,IAAI;KAC5B,CAAC,CAAC;IAEH,IAAIJ,MAAM,KAAK,KAAK,EAAE;MACpB,IAAI,CAACK,WAAW,CAACJ,YAAY,CAAC;KAC/B,MAAM;MACL,IAAI,CAACK,YAAY,CAACL,YAAY,CAAC;;EAEnC;EAEQI,WAAWA,CAACE,IAAW;IAC7B,MAAMC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,CAAC,CAAC,CAAC;IACpC,MAAMI,UAAU,GAAG,CACjBH,OAAO,CAACI,IAAI,CAAC,GAAG,CAAC,EACjB,GAAGL,IAAI,CAACL,GAAG,CAACW,GAAG,IAAIL,OAAO,CAACN,GAAG,CAACY,MAAM,IAAI,IAAID,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC,CACxE,CAACA,IAAI,CAAC,IAAI,CAAC;IAEZ,IAAI,CAACG,YAAY,CAACJ,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC;EACxD;EAEQL,YAAYA,CAACC,IAAW;IAC9B,MAAMS,WAAW,GAAGxH,IAAI,CAACyH,SAAS,CAACV,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;IACjD,IAAI,CAACQ,YAAY,CAACC,WAAW,EAAE,YAAY,EAAE,kBAAkB,CAAC;EAClE;EAEQD,YAAYA,CAACG,OAAe,EAAEC,QAAgB,EAAEC,WAAmB;IACzE,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACJ,OAAO,CAAC,EAAE;MAAEpD,IAAI,EAAEsD;IAAW,CAAE,CAAC;IACvD,MAAMG,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;IACfI,IAAI,CAACI,QAAQ,GAAGZ,QAAQ;IACxBQ,IAAI,CAACK,KAAK,EAAE;IACZR,MAAM,CAACC,GAAG,CAACQ,eAAe,CAACV,GAAG,CAAC;EACjC;EAEA;EACAW,oBAAoBA,CAAA;IAClB,IAAI,CAACvJ,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEAwJ,oBAAoBA,CAAA;IAClB,IAAI,CAACvJ,eAAe,GAAG;MACrBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;KACX;IACD,IAAI,CAACZ,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC6B,oBAAoB,EAAE;EAC7B;EAEAkI,SAASA,CAACC,KAAa;IACrB,IAAI,IAAI,CAAC/J,MAAM,KAAK+J,KAAK,EAAE;MACzB,IAAI,CAAC9J,SAAS,GAAG,IAAI,CAACA,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;KAC3D,MAAM;MACL,IAAI,CAACD,MAAM,GAAG+J,KAAK;MACnB,IAAI,CAAC9J,SAAS,GAAG,KAAK;;IAExB,IAAI,CAAC2B,oBAAoB,EAAE;EAC7B;CACD;AAtlBY3D,kBAAkB,GAAA+L,UAAA,EAL9BhM,SAAS,CAAC;EACTiM,QAAQ,EAAE,eAAe;EACzBC,WAAW,EAAE,4BAA4B;EACzCC,SAAS,EAAE,CAAC,2BAA2B;CACxC,CAAC,C,EACWlM,kBAAkB,CAslB9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}