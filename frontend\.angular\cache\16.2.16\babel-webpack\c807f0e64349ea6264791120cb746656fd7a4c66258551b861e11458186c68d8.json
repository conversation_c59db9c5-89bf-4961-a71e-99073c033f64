{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/toast.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nfunction AdminSettingsComponent_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function AdminSettingsComponent_button_23_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r7);\n      const tab_r5 = restoredCtx.$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.setActiveTab(tab_r5.id));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r5 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"border-[#4f5fad]\", ctx_r0.activeTab === tab_r5.id)(\"text-[#4f5fad]\", ctx_r0.activeTab === tab_r5.id)(\"border-transparent\", ctx_r0.activeTab !== tab_r5.id)(\"text-[#6d6870]\", ctx_r0.activeTab !== tab_r5.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(tab_r5.icon + \" mr-2\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tab_r5.label, \" \");\n  }\n}\nfunction AdminSettingsComponent_div_25_i_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 47);\n  }\n}\nfunction AdminSettingsComponent_div_25_i_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 48);\n  }\n}\nfunction AdminSettingsComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"div\", 24)(3, \"h3\", 25);\n    i0.ɵɵtext(4, \"Basic Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"label\", 26);\n    i0.ɵɵtext(7, \"Site Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"input\", 27);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_25_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.systemSettings.siteName = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\")(10, \"label\", 26);\n    i0.ɵɵtext(11, \"Site Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"textarea\", 28);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_25_Template_textarea_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.systemSettings.siteDescription = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\")(14, \"label\", 26);\n    i0.ɵɵtext(15, \"Default User Role\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"select\", 29);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_25_Template_select_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.systemSettings.defaultUserRole = $event);\n    });\n    i0.ɵɵelementStart(17, \"option\", 30);\n    i0.ɵɵtext(18, \"Student\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"option\", 31);\n    i0.ɵɵtext(20, \"Teacher\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(21, \"div\", 24)(22, \"h3\", 25);\n    i0.ɵɵtext(23, \"System Controls\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 32)(25, \"div\", 33)(26, \"span\", 34);\n    i0.ɵɵtext(27, \"Maintenance Mode\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"label\", 35)(29, \"input\", 36);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_25_Template_input_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.systemSettings.maintenanceMode = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"div\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 33)(32, \"span\", 34);\n    i0.ɵɵtext(33, \"Allow Registration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"label\", 35)(35, \"input\", 36);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_25_Template_input_ngModelChange_35_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.systemSettings.allowRegistration = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(36, \"div\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 33)(38, \"span\", 34);\n    i0.ɵɵtext(39, \"Email Verification Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"label\", 35)(41, \"input\", 36);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_25_Template_input_ngModelChange_41_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.systemSettings.emailVerificationRequired = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(42, \"div\", 37);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(43, \"div\", 38)(44, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function AdminSettingsComponent_div_25_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.clearCache());\n    });\n    i0.ɵɵelement(45, \"i\", 40);\n    i0.ɵɵtext(46, \"Clear System Cache \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function AdminSettingsComponent_div_25_Template_button_click_47_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.restartSystem());\n    });\n    i0.ɵɵelement(48, \"i\", 42);\n    i0.ɵɵtext(49, \"Restart System \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(50, \"div\", 43)(51, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function AdminSettingsComponent_div_25_Template_button_click_51_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.saveSettings(\"System\"));\n    });\n    i0.ɵɵtemplate(52, AdminSettingsComponent_div_25_i_52_Template, 1, 0, \"i\", 45);\n    i0.ɵɵtemplate(53, AdminSettingsComponent_div_25_i_53_Template, 1, 0, \"i\", 46);\n    i0.ɵɵtext(54);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.systemSettings.siteName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.systemSettings.siteDescription);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.systemSettings.defaultUserRole);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.systemSettings.maintenanceMode);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.systemSettings.allowRegistration);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.systemSettings.emailVerificationRequired);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.saving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.saving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.saving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.saving ? \"Saving...\" : \"Save System Settings\", \" \");\n  }\n}\nfunction AdminSettingsComponent_div_26_i_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 47);\n  }\n}\nfunction AdminSettingsComponent_div_26_i_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 48);\n  }\n}\nfunction AdminSettingsComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"div\", 24)(3, \"h3\", 25);\n    i0.ɵɵtext(4, \"SMTP Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"label\", 26);\n    i0.ɵɵtext(7, \"SMTP Host\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"input\", 49);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_26_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.emailSettings.smtpHost = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\")(10, \"label\", 26);\n    i0.ɵɵtext(11, \"SMTP Port\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 50);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_26_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.emailSettings.smtpPort = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\")(14, \"label\", 26);\n    i0.ɵɵtext(15, \"Username\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"input\", 27);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_26_Template_input_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.emailSettings.smtpUsername = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\")(18, \"label\", 26);\n    i0.ɵɵtext(19, \"Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"input\", 51);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_26_Template_input_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.emailSettings.smtpPassword = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 24)(22, \"h3\", 25);\n    i0.ɵɵtext(23, \"Email Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\")(25, \"label\", 26);\n    i0.ɵɵtext(26, \"From Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"input\", 52);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_26_Template_input_ngModelChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.emailSettings.fromEmail = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\")(29, \"label\", 26);\n    i0.ɵɵtext(30, \"From Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"input\", 27);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_26_Template_input_ngModelChange_31_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.emailSettings.fromName = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 33)(33, \"span\", 34);\n    i0.ɵɵtext(34, \"Use Secure Connection\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"label\", 35)(36, \"input\", 36);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_26_Template_input_ngModelChange_36_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.emailSettings.smtpSecure = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(37, \"div\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 38)(39, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function AdminSettingsComponent_div_26_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.testEmailConnection());\n    });\n    i0.ɵɵelement(40, \"i\", 53);\n    i0.ɵɵtext(41, \"Test Connection \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function AdminSettingsComponent_div_26_Template_button_click_42_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.sendTestEmail());\n    });\n    i0.ɵɵelement(43, \"i\", 55);\n    i0.ɵɵtext(44, \"Send Test Email \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(45, \"div\", 43)(46, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function AdminSettingsComponent_div_26_Template_button_click_46_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.saveSettings(\"Email\"));\n    });\n    i0.ɵɵtemplate(47, AdminSettingsComponent_div_26_i_47_Template, 1, 0, \"i\", 45);\n    i0.ɵɵtemplate(48, AdminSettingsComponent_div_26_i_48_Template, 1, 0, \"i\", 46);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.emailSettings.smtpHost);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.emailSettings.smtpPort);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.emailSettings.smtpUsername);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.emailSettings.smtpPassword);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.emailSettings.fromEmail);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.emailSettings.fromName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.emailSettings.smtpSecure);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.saving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.saving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.saving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.saving ? \"Saving...\" : \"Save Email Settings\", \" \");\n  }\n}\nfunction AdminSettingsComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 56);\n    i0.ɵɵelement(2, \"i\", 57);\n    i0.ɵɵelementStart(3, \"h3\", 58);\n    i0.ɵɵtext(4, \"Security Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 7);\n    i0.ɵɵtext(6, \"Configure security policies and access controls\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AdminSettingsComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 56);\n    i0.ɵɵelement(2, \"i\", 59);\n    i0.ɵɵelementStart(3, \"h3\", 58);\n    i0.ɵɵtext(4, \"Backup & Restore\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 7);\n    i0.ɵɵtext(6, \"Manage system backups and data recovery\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nconst _c0 = function () {\n  return {\n    id: \"system\",\n    label: \"System\",\n    icon: \"fas fa-cog\"\n  };\n};\nconst _c1 = function () {\n  return {\n    id: \"email\",\n    label: \"Email\",\n    icon: \"fas fa-envelope\"\n  };\n};\nconst _c2 = function () {\n  return {\n    id: \"security\",\n    label: \"Security\",\n    icon: \"fas fa-shield-alt\"\n  };\n};\nconst _c3 = function () {\n  return {\n    id: \"backup\",\n    label: \"Backup\",\n    icon: \"fas fa-database\"\n  };\n};\nconst _c4 = function (a0, a1, a2, a3) {\n  return [a0, a1, a2, a3];\n};\nexport class AdminSettingsComponent {\n  constructor(toastService) {\n    this.toastService = toastService;\n    // System Settings\n    this.systemSettings = {\n      siteName: 'DevBridge Admin',\n      siteDescription: 'Project Management System',\n      maintenanceMode: false,\n      allowRegistration: true,\n      emailVerificationRequired: true,\n      maxFileUploadSize: 10,\n      sessionTimeout: 30,\n      defaultUserRole: 'student',\n      passwordMinLength: 8,\n      passwordRequireSpecialChars: true\n    };\n    // Email Settings\n    this.emailSettings = {\n      smtpHost: '',\n      smtpPort: 587,\n      smtpUsername: '',\n      smtpPassword: '',\n      smtpSecure: true,\n      fromEmail: '',\n      fromName: 'DevBridge Team'\n    };\n    // Security Settings\n    this.securitySettings = {\n      enableTwoFactor: false,\n      maxLoginAttempts: 5,\n      lockoutDuration: 15,\n      passwordExpiry: 90,\n      enableAuditLog: true,\n      enableIpWhitelist: false,\n      allowedIps: []\n    };\n    // Backup Settings\n    this.backupSettings = {\n      autoBackup: true,\n      backupFrequency: 'daily',\n      backupRetention: 30,\n      backupLocation: 'cloud',\n      lastBackup: new Date()\n    };\n    // UI State\n    this.activeTab = 'system';\n    this.loading = false;\n    this.saving = false;\n  }\n  ngOnInit() {\n    this.loadSettings();\n  }\n  loadSettings() {\n    this.loading = true;\n    // In a real app, load settings from API\n    setTimeout(() => {\n      this.loading = false;\n    }, 1000);\n  }\n  saveSettings(settingsType) {\n    this.saving = true;\n    // Simulate API call\n    setTimeout(() => {\n      this.saving = false;\n      this.toastService.showSuccess(`${settingsType} settings saved successfully!`);\n    }, 1500);\n  }\n  setActiveTab(tab) {\n    this.activeTab = tab;\n  }\n  // System Actions\n  toggleMaintenanceMode() {\n    this.systemSettings.maintenanceMode = !this.systemSettings.maintenanceMode;\n    const status = this.systemSettings.maintenanceMode ? 'enabled' : 'disabled';\n    this.toastService.showInfo(`Maintenance mode ${status}`);\n  }\n  clearCache() {\n    this.toastService.showSuccess('System cache cleared successfully!');\n  }\n  restartSystem() {\n    if (confirm('Are you sure you want to restart the system? This will temporarily interrupt service.')) {\n      this.toastService.showInfo('System restart initiated...');\n    }\n  }\n  // Backup Actions\n  createBackup() {\n    this.toastService.showInfo('Creating backup... This may take a few minutes.');\n    setTimeout(() => {\n      this.backupSettings.lastBackup = new Date();\n      this.toastService.showSuccess('Backup created successfully!');\n    }, 3000);\n  }\n  restoreBackup() {\n    if (confirm('Are you sure you want to restore from backup? This will overwrite current data.')) {\n      this.toastService.showInfo('Restoring from backup...');\n    }\n  }\n  // Security Actions\n  generateApiKey() {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n    let result = '';\n    for (let i = 0; i < 32; i++) {\n      result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n  }\n  addIpToWhitelist(ip) {\n    if (ip && !this.securitySettings.allowedIps.includes(ip)) {\n      this.securitySettings.allowedIps.push(ip);\n      this.toastService.showSuccess(`IP ${ip} added to whitelist`);\n    }\n  }\n  removeIpFromWhitelist(ip) {\n    const index = this.securitySettings.allowedIps.indexOf(ip);\n    if (index > -1) {\n      this.securitySettings.allowedIps.splice(index, 1);\n      this.toastService.showSuccess(`IP ${ip} removed from whitelist`);\n    }\n  }\n  // Email Actions\n  testEmailConnection() {\n    this.toastService.showInfo('Testing email connection...');\n    setTimeout(() => {\n      this.toastService.showSuccess('Email connection test successful!');\n    }, 2000);\n  }\n  sendTestEmail() {\n    this.toastService.showInfo('Sending test email...');\n    setTimeout(() => {\n      this.toastService.showSuccess('Test email sent successfully!');\n    }, 2000);\n  }\n  // Export/Import Settings\n  exportSettings() {\n    const settings = {\n      system: this.systemSettings,\n      email: this.emailSettings,\n      security: this.securitySettings,\n      backup: this.backupSettings\n    };\n    const dataStr = JSON.stringify(settings, null, 2);\n    const dataBlob = new Blob([dataStr], {\n      type: 'application/json'\n    });\n    const url = URL.createObjectURL(dataBlob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = 'admin-settings.json';\n    link.click();\n    URL.revokeObjectURL(url);\n    this.toastService.showSuccess('Settings exported successfully!');\n  }\n  importSettings(event) {\n    const file = event.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = e => {\n        try {\n          const settings = JSON.parse(e.target?.result);\n          if (settings.system) this.systemSettings = {\n            ...this.systemSettings,\n            ...settings.system\n          };\n          if (settings.email) this.emailSettings = {\n            ...this.emailSettings,\n            ...settings.email\n          };\n          if (settings.security) this.securitySettings = {\n            ...this.securitySettings,\n            ...settings.security\n          };\n          if (settings.backup) this.backupSettings = {\n            ...this.backupSettings,\n            ...settings.backup\n          };\n          this.toastService.showSuccess('Settings imported successfully!');\n        } catch (error) {\n          this.toastService.showError('Invalid settings file format');\n        }\n      };\n      reader.readAsText(file);\n    }\n  }\n  static {\n    this.ɵfac = function AdminSettingsComponent_Factory(t) {\n      return new (t || AdminSettingsComponent)(i0.ɵɵdirectiveInject(i1.ToastService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminSettingsComponent,\n      selectors: [[\"app-admin-settings\"]],\n      decls: 29,\n      vars: 14,\n      consts: [[1, \"container-fluid\", \"p-4\", \"md:p-6\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"min-h-screen\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"relative\", \"z-10\"], [1, \"flex\", \"flex-col\", \"md:flex-row\", \"md:items-center\", \"md:justify-between\", \"mb-8\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"mb-2\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"flex\", \"items-center\", \"gap-3\", \"mt-4\", \"md:mt-0\"], [1, \"inline-flex\", \"items-center\", \"px-3\", \"py-2\", \"text-sm\", \"font-medium\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:bg-[#f8fafc]\", \"dark:hover:bg-[#1a1a1a]\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-download\", \"mr-2\"], [1, \"inline-flex\", \"items-center\", \"px-3\", \"py-2\", \"text-sm\", \"font-medium\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:bg-[#f8fafc]\", \"dark:hover:bg-[#1a1a1a]\", \"transition-colors\", \"cursor-pointer\"], [1, \"fas\", \"fa-upload\", \"mr-2\"], [\"type\", \"file\", \"accept\", \".json\", 1, \"hidden\", 3, \"change\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"overflow-hidden\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-0.5\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"border-b\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\"], [\"aria-label\", \"Tabs\", 1, \"flex\", \"space-x-8\", \"px-6\"], [\"class\", \"whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors hover:text-[#4f5fad] dark:hover:text-[#6d78c9] flex items-center\", 3, \"border-[#4f5fad]\", \"text-[#4f5fad]\", \"border-transparent\", \"text-[#6d6870]\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-6\"], [\"class\", \"space-y-6\", 4, \"ngIf\"], [1, \"whitespace-nowrap\", \"py-4\", \"px-1\", \"border-b-2\", \"font-medium\", \"text-sm\", \"transition-colors\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"flex\", \"items-center\", 3, \"click\"], [1, \"space-y-6\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [1, \"space-y-4\"], [1, \"text-lg\", \"font-semibold\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-2\"], [\"type\", \"text\", 1, \"w-full\", \"px-3\", \"py-2\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"transition-all\", 3, \"ngModel\", \"ngModelChange\"], [\"rows\", \"3\", 1, \"w-full\", \"px-3\", \"py-2\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"transition-all\", 3, \"ngModel\", \"ngModelChange\"], [1, \"w-full\", \"px-3\", \"py-2\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"transition-all\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"student\"], [\"value\", \"teacher\"], [1, \"space-y-3\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"relative\", \"inline-flex\", \"items-center\", \"cursor-pointer\"], [\"type\", \"checkbox\", 1, \"sr-only\", \"peer\", 3, \"ngModel\", \"ngModelChange\"], [1, \"w-11\", \"h-6\", \"bg-gray-200\", \"peer-focus:outline-none\", \"peer-focus:ring-4\", \"peer-focus:ring-[#4f5fad]/20\", \"dark:peer-focus:ring-[#6d78c9]/20\", \"rounded-full\", \"peer\", \"dark:bg-gray-700\", \"peer-checked:after:translate-x-full\", \"peer-checked:after:border-white\", \"after:content-['']\", \"after:absolute\", \"after:top-[2px]\", \"after:left-[2px]\", \"after:bg-white\", \"after:border-gray-300\", \"after:border\", \"after:rounded-full\", \"after:h-5\", \"after:w-5\", \"after:transition-all\", \"dark:border-gray-600\", \"peer-checked:bg-[#4f5fad]\", \"dark:peer-checked:bg-[#6d78c9]\"], [1, \"pt-4\", \"space-y-2\"], [1, \"w-full\", \"px-3\", \"py-2\", \"text-sm\", \"font-medium\", \"rounded-lg\", \"bg-[#4f5fad]/10\", \"text-[#4f5fad]\", \"hover:bg-[#4f5fad]/20\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-broom\", \"mr-2\"], [1, \"w-full\", \"px-3\", \"py-2\", \"text-sm\", \"font-medium\", \"rounded-lg\", \"bg-[#ff6b69]/10\", \"text-[#ff6b69]\", \"hover:bg-[#ff6b69]/20\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-power-off\", \"mr-2\"], [1, \"flex\", \"justify-end\", \"pt-6\", \"border-t\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\"], [1, \"px-6\", \"py-2.5\", \"text-sm\", \"font-medium\", \"text-white\", \"rounded-lg\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#4f5fad]\", \"dark:to-[#6d78c9]\", \"hover:shadow-lg\", \"transition-all\", \"disabled:opacity-50\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-save mr-2\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin mr-2\", 4, \"ngIf\"], [1, \"fas\", \"fa-save\", \"mr-2\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-2\"], [\"type\", \"text\", \"placeholder\", \"smtp.gmail.com\", 1, \"w-full\", \"px-3\", \"py-2\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"transition-all\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"number\", 1, \"w-full\", \"px-3\", \"py-2\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"transition-all\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"password\", 1, \"w-full\", \"px-3\", \"py-2\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"transition-all\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"email\", \"placeholder\", \"<EMAIL>\", 1, \"w-full\", \"px-3\", \"py-2\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"transition-all\", 3, \"ngModel\", \"ngModelChange\"], [1, \"fas\", \"fa-plug\", \"mr-2\"], [1, \"w-full\", \"px-3\", \"py-2\", \"text-sm\", \"font-medium\", \"rounded-lg\", \"bg-[#00d4aa]/10\", \"text-[#00d4aa]\", \"hover:bg-[#00d4aa]/20\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-paper-plane\", \"mr-2\"], [1, \"text-center\", \"py-12\"], [1, \"fas\", \"fa-shield-alt\", \"text-4xl\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-4\"], [1, \"text-lg\", \"font-semibold\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [1, \"fas\", \"fa-database\", \"text-4xl\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-4\"]],\n      template: function AdminSettingsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\")(7, \"h1\", 6);\n          i0.ɵɵtext(8, \" System Settings \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p\", 7);\n          i0.ɵɵtext(10, \" Configure system-wide settings and preferences \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 8)(12, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function AdminSettingsComponent_Template_button_click_12_listener() {\n            return ctx.exportSettings();\n          });\n          i0.ɵɵelement(13, \"i\", 10);\n          i0.ɵɵtext(14, \"Export Settings \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"label\", 11);\n          i0.ɵɵelement(16, \"i\", 12);\n          i0.ɵɵtext(17, \"Import Settings \");\n          i0.ɵɵelementStart(18, \"input\", 13);\n          i0.ɵɵlistener(\"change\", function AdminSettingsComponent_Template_input_change_18_listener($event) {\n            return ctx.importSettings($event);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(19, \"div\", 14);\n          i0.ɵɵelement(20, \"div\", 15);\n          i0.ɵɵelementStart(21, \"div\", 16)(22, \"nav\", 17);\n          i0.ɵɵtemplate(23, AdminSettingsComponent_button_23_Template, 3, 11, \"button\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 19);\n          i0.ɵɵtemplate(25, AdminSettingsComponent_div_25_Template, 55, 10, \"div\", 20);\n          i0.ɵɵtemplate(26, AdminSettingsComponent_div_26_Template, 50, 11, \"div\", 20);\n          i0.ɵɵtemplate(27, AdminSettingsComponent_div_27_Template, 7, 0, \"div\", 20);\n          i0.ɵɵtemplate(28, AdminSettingsComponent_div_28_Template, 7, 0, \"div\", 20);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(23);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction4(9, _c4, i0.ɵɵpureFunction0(5, _c0), i0.ɵɵpureFunction0(6, _c1), i0.ɵɵpureFunction0(7, _c2), i0.ɵɵpureFunction0(8, _c3)));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"system\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"email\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"security\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"backup\");\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.NumberValueAccessor, i3.CheckboxControlValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgModel],\n      styles: [\"\\n\\n\\n.settings-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #edf1f4 0%, #f8fafc 100%);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .settings-container[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #121212 0%, #1a1a1a 100%);\\n}\\n\\n\\n\\n.tab-nav[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #edf1f4;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .tab-nav[_ngcontent-%COMP%] {\\n  border-bottom-color: #2a2a2a;\\n}\\n\\n.tab-button[_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: 1rem 0.25rem;\\n  border-bottom: 2px solid transparent;\\n  font-weight: 500;\\n  font-size: 0.875rem;\\n  transition: all 0.2s ease;\\n  color: #6d6870;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%] {\\n  color: #a0a0a0;\\n}\\n\\n.tab-button[_ngcontent-%COMP%]:hover {\\n  color: #4f5fad;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]:hover {\\n  color: #6d78c9;\\n}\\n\\n.tab-button.active[_ngcontent-%COMP%] {\\n  color: #4f5fad;\\n  border-bottom-color: #4f5fad;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .tab-button.active[_ngcontent-%COMP%] {\\n  color: #6d78c9;\\n  border-bottom-color: #6d78c9;\\n}\\n\\n\\n\\n.form-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.75rem;\\n  border: 1px solid #bdc6cc;\\n  border-radius: 0.5rem;\\n  background-color: white;\\n  color: #6d6870;\\n  transition: all 0.2s ease;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .form-input[_ngcontent-%COMP%] {\\n  border-color: #2a2a2a;\\n  background-color: #1a1a1a;\\n  color: #e0e0e0;\\n}\\n\\n.form-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #4f5fad;\\n  box-shadow: 0 0 0 2px rgba(79, 95, 173, 0.2);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .form-input[_ngcontent-%COMP%]:focus {\\n  border-color: #6d78c9;\\n  box-shadow: 0 0 0 2px rgba(109, 120, 201, 0.2);\\n}\\n\\n\\n\\n.toggle-switch[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-flex;\\n  align-items: center;\\n  cursor: pointer;\\n}\\n\\n.toggle-input[_ngcontent-%COMP%] {\\n  position: absolute;\\n  opacity: 0;\\n  pointer-events: none;\\n}\\n\\n.toggle-slider[_ngcontent-%COMP%] {\\n  width: 2.75rem;\\n  height: 1.5rem;\\n  background-color: #e5e7eb;\\n  border-radius: 9999px;\\n  position: relative;\\n  transition: all 0.2s ease;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .toggle-slider[_ngcontent-%COMP%] {\\n  background-color: #374151;\\n}\\n\\n.toggle-slider[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  top: 2px;\\n  left: 2px;\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  background-color: white;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.toggle-input[_ngcontent-%COMP%]:checked    + .toggle-slider[_ngcontent-%COMP%] {\\n  background-color: #4f5fad;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .toggle-input[_ngcontent-%COMP%]:checked    + .toggle-slider[_ngcontent-%COMP%] {\\n  background-color: #6d78c9;\\n}\\n\\n.toggle-input[_ngcontent-%COMP%]:checked    + .toggle-slider[_ngcontent-%COMP%]::after {\\n  transform: translateX(1.25rem);\\n}\\n\\n\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3d4a85 0%, #4f5fad 100%);\\n  color: white;\\n  padding: 0.625rem 1.5rem;\\n  border-radius: 0.5rem;\\n  font-weight: 500;\\n  font-size: 0.875rem;\\n  transition: all 0.2s ease;\\n  border: none;\\n  cursor: pointer;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4f5fad 0%, #6d78c9 100%);\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 12px rgba(79, 95, 173, 0.3);\\n  transform: translateY(-1px);\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: rgba(79, 95, 173, 0.1);\\n  color: #4f5fad;\\n  padding: 0.5rem 0.75rem;\\n  border-radius: 0.5rem;\\n  font-weight: 500;\\n  font-size: 0.875rem;\\n  transition: all 0.2s ease;\\n  border: none;\\n  cursor: pointer;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n  background-color: rgba(109, 120, 201, 0.1);\\n  color: #6d78c9;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(79, 95, 173, 0.2);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(109, 120, 201, 0.2);\\n}\\n\\n\\n\\n.settings-card[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 0.75rem;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\\n  border: 1px solid rgba(237, 241, 244, 0.5);\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .settings-card[_ngcontent-%COMP%] {\\n  background-color: #1e1e1e;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);\\n  border-color: #2a2a2a;\\n}\\n\\n.settings-card[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 2px;\\n  background: linear-gradient(90deg, #3d4a85 0%, #4f5fad 100%);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .settings-card[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(90deg, #6d78c9 0%, #4f5fad 100%);\\n}\\n\\n\\n\\n.fade-in[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n.slide-in[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    opacity: 0;\\n    transform: translateX(-20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n\\n\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  width: 1rem;\\n  height: 1rem;\\n  border: 2px solid transparent;\\n  border-top: 2px solid currentColor;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% { transform: rotate(0deg); }\\n  100% { transform: rotate(360deg); }\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .settings-container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  \\n  .tab-nav[_ngcontent-%COMP%] {\\n    overflow-x: auto;\\n    white-space: nowrap;\\n  }\\n  \\n  .tab-button[_ngcontent-%COMP%] {\\n    flex-shrink: 0;\\n    margin-right: 2rem;\\n  }\\n}\\n\\n\\n\\n.form-input[_ngcontent-%COMP%]:focus, .btn-primary[_ngcontent-%COMP%]:focus, .btn-secondary[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #4f5fad;\\n  outline-offset: 2px;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .form-input[_ngcontent-%COMP%]:focus, .dark[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:focus, .dark[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:focus {\\n  outline-color: #6d78c9;\\n}\\n\\n\\n\\n.input-success[_ngcontent-%COMP%] {\\n  border-color: #10b981;\\n  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);\\n}\\n\\n.input-error[_ngcontent-%COMP%] {\\n  border-color: #ef4444;\\n  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);\\n}\\n\\n\\n\\n.tooltip[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.tooltip[_ngcontent-%COMP%]::after {\\n  content: attr(data-tooltip);\\n  position: absolute;\\n  bottom: 100%;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background-color: #1f2937;\\n  color: white;\\n  padding: 0.5rem;\\n  border-radius: 0.25rem;\\n  font-size: 0.75rem;\\n  white-space: nowrap;\\n  opacity: 0;\\n  pointer-events: none;\\n  transition: opacity 0.2s ease;\\n  z-index: 10;\\n}\\n\\n.tooltip[_ngcontent-%COMP%]:hover::after {\\n  opacity: 1;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵlistener", "AdminSettingsComponent_button_23_Template_button_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r7", "tab_r5", "$implicit", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "setActiveTab", "id", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "ctx_r0", "activeTab", "ɵɵadvance", "ɵɵclassMap", "icon", "ɵɵtextInterpolate1", "label", "AdminSettingsComponent_div_25_Template_input_ngModelChange_8_listener", "$event", "_r11", "ctx_r10", "systemSettings", "siteName", "AdminSettingsComponent_div_25_Template_textarea_ngModelChange_12_listener", "ctx_r12", "siteDescription", "AdminSettingsComponent_div_25_Template_select_ngModelChange_16_listener", "ctx_r13", "defaultUserRole", "AdminSettingsComponent_div_25_Template_input_ngModelChange_29_listener", "ctx_r14", "maintenanceMode", "AdminSettingsComponent_div_25_Template_input_ngModelChange_35_listener", "ctx_r15", "allowRegistration", "AdminSettingsComponent_div_25_Template_input_ngModelChange_41_listener", "ctx_r16", "emailVerificationRequired", "AdminSettingsComponent_div_25_Template_button_click_44_listener", "ctx_r17", "clearCache", "AdminSettingsComponent_div_25_Template_button_click_47_listener", "ctx_r18", "restartSystem", "AdminSettingsComponent_div_25_Template_button_click_51_listener", "ctx_r19", "saveSettings", "ɵɵtemplate", "AdminSettingsComponent_div_25_i_52_Template", "AdminSettingsComponent_div_25_i_53_Template", "ɵɵproperty", "ctx_r1", "saving", "AdminSettingsComponent_div_26_Template_input_ngModelChange_8_listener", "_r23", "ctx_r22", "emailSettings", "smtpHost", "AdminSettingsComponent_div_26_Template_input_ngModelChange_12_listener", "ctx_r24", "smtpPort", "AdminSettingsComponent_div_26_Template_input_ngModelChange_16_listener", "ctx_r25", "smtpUsername", "AdminSettingsComponent_div_26_Template_input_ngModelChange_20_listener", "ctx_r26", "smtpPassword", "AdminSettingsComponent_div_26_Template_input_ngModelChange_27_listener", "ctx_r27", "fromEmail", "AdminSettingsComponent_div_26_Template_input_ngModelChange_31_listener", "ctx_r28", "fromName", "AdminSettingsComponent_div_26_Template_input_ngModelChange_36_listener", "ctx_r29", "smtpSecure", "AdminSettingsComponent_div_26_Template_button_click_39_listener", "ctx_r30", "testEmailConnection", "AdminSettingsComponent_div_26_Template_button_click_42_listener", "ctx_r31", "sendTestEmail", "AdminSettingsComponent_div_26_Template_button_click_46_listener", "ctx_r32", "AdminSettingsComponent_div_26_i_47_Template", "AdminSettingsComponent_div_26_i_48_Template", "ctx_r2", "AdminSettingsComponent", "constructor", "toastService", "maxFileUploadSize", "sessionTimeout", "<PERSON><PERSON>in<PERSON><PERSON><PERSON>", "passwordRequireSpecialChars", "securitySettings", "enableTwoFactor", "maxLogin<PERSON><PERSON><PERSON>s", "lockoutDuration", "passwordExpiry", "enableAuditLog", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "allowedIps", "backupSettings", "autoBackup", "backupFrequency", "backup<PERSON><PERSON><PERSON><PERSON>", "backupLocation", "lastBackup", "Date", "loading", "ngOnInit", "loadSettings", "setTimeout", "settingsType", "showSuccess", "tab", "toggleMaintenanceMode", "status", "showInfo", "confirm", "createBackup", "restoreBackup", "generateApiKey", "chars", "result", "i", "char<PERSON>t", "Math", "floor", "random", "length", "addIpTo<PERSON><PERSON><PERSON><PERSON>", "ip", "includes", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "index", "indexOf", "splice", "exportSettings", "settings", "system", "email", "security", "backup", "dataStr", "JSON", "stringify", "dataBlob", "Blob", "type", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "click", "revokeObjectURL", "importSettings", "event", "file", "target", "files", "reader", "FileReader", "onload", "e", "parse", "error", "showError", "readAsText", "ɵɵdirectiveInject", "i1", "ToastService", "selectors", "decls", "vars", "consts", "template", "AdminSettingsComponent_Template", "rf", "ctx", "AdminSettingsComponent_Template_button_click_12_listener", "AdminSettingsComponent_Template_input_change_18_listener", "AdminSettingsComponent_button_23_Template", "AdminSettingsComponent_div_25_Template", "AdminSettingsComponent_div_26_Template", "AdminSettingsComponent_div_27_Template", "AdminSettingsComponent_div_28_Template", "ɵɵpureFunction4", "_c4", "ɵɵpureFunction0", "_c0", "_c1", "_c2", "_c3"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\settings\\settings.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\settings\\settings.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ToastService } from 'src/app/services/toast.service';\n\n@Component({\n  selector: 'app-admin-settings',\n  templateUrl: './settings.component.html',\n  styleUrls: ['./settings.component.css']\n})\nexport class AdminSettingsComponent implements OnInit {\n  \n  // System Settings\n  systemSettings = {\n    siteName: 'DevBridge Admin',\n    siteDescription: 'Project Management System',\n    maintenanceMode: false,\n    allowRegistration: true,\n    emailVerificationRequired: true,\n    maxFileUploadSize: 10, // MB\n    sessionTimeout: 30, // minutes\n    defaultUserRole: 'student',\n    passwordMinLength: 8,\n    passwordRequireSpecialChars: true\n  };\n\n  // Email Settings\n  emailSettings = {\n    smtpHost: '',\n    smtpPort: 587,\n    smtpUsername: '',\n    smtpPassword: '',\n    smtpSecure: true,\n    fromEmail: '',\n    fromName: 'DevBridge Team'\n  };\n\n  // Security Settings\n  securitySettings = {\n    enableTwoFactor: false,\n    maxLoginAttempts: 5,\n    lockoutDuration: 15, // minutes\n    passwordExpiry: 90, // days\n    enableAuditLog: true,\n    enableIpWhitelist: false,\n    allowedIps: []\n  };\n\n  // Backup Settings\n  backupSettings = {\n    autoBackup: true,\n    backupFrequency: 'daily',\n    backupRetention: 30, // days\n    backupLocation: 'cloud',\n    lastBackup: new Date()\n  };\n\n  // UI State\n  activeTab = 'system';\n  loading = false;\n  saving = false;\n\n  constructor(\n    private toastService: ToastService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadSettings();\n  }\n\n  loadSettings(): void {\n    this.loading = true;\n    // In a real app, load settings from API\n    setTimeout(() => {\n      this.loading = false;\n    }, 1000);\n  }\n\n  saveSettings(settingsType: string): void {\n    this.saving = true;\n    \n    // Simulate API call\n    setTimeout(() => {\n      this.saving = false;\n      this.toastService.showSuccess(`${settingsType} settings saved successfully!`);\n    }, 1500);\n  }\n\n  setActiveTab(tab: string): void {\n    this.activeTab = tab;\n  }\n\n  // System Actions\n  toggleMaintenanceMode(): void {\n    this.systemSettings.maintenanceMode = !this.systemSettings.maintenanceMode;\n    const status = this.systemSettings.maintenanceMode ? 'enabled' : 'disabled';\n    this.toastService.showInfo(`Maintenance mode ${status}`);\n  }\n\n  clearCache(): void {\n    this.toastService.showSuccess('System cache cleared successfully!');\n  }\n\n  restartSystem(): void {\n    if (confirm('Are you sure you want to restart the system? This will temporarily interrupt service.')) {\n      this.toastService.showInfo('System restart initiated...');\n    }\n  }\n\n  // Backup Actions\n  createBackup(): void {\n    this.toastService.showInfo('Creating backup... This may take a few minutes.');\n    setTimeout(() => {\n      this.backupSettings.lastBackup = new Date();\n      this.toastService.showSuccess('Backup created successfully!');\n    }, 3000);\n  }\n\n  restoreBackup(): void {\n    if (confirm('Are you sure you want to restore from backup? This will overwrite current data.')) {\n      this.toastService.showInfo('Restoring from backup...');\n    }\n  }\n\n  // Security Actions\n  generateApiKey(): string {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n    let result = '';\n    for (let i = 0; i < 32; i++) {\n      result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n  }\n\n  addIpToWhitelist(ip: string): void {\n    if (ip && !this.securitySettings.allowedIps.includes(ip)) {\n      this.securitySettings.allowedIps.push(ip);\n      this.toastService.showSuccess(`IP ${ip} added to whitelist`);\n    }\n  }\n\n  removeIpFromWhitelist(ip: string): void {\n    const index = this.securitySettings.allowedIps.indexOf(ip);\n    if (index > -1) {\n      this.securitySettings.allowedIps.splice(index, 1);\n      this.toastService.showSuccess(`IP ${ip} removed from whitelist`);\n    }\n  }\n\n  // Email Actions\n  testEmailConnection(): void {\n    this.toastService.showInfo('Testing email connection...');\n    setTimeout(() => {\n      this.toastService.showSuccess('Email connection test successful!');\n    }, 2000);\n  }\n\n  sendTestEmail(): void {\n    this.toastService.showInfo('Sending test email...');\n    setTimeout(() => {\n      this.toastService.showSuccess('Test email sent successfully!');\n    }, 2000);\n  }\n\n  // Export/Import Settings\n  exportSettings(): void {\n    const settings = {\n      system: this.systemSettings,\n      email: this.emailSettings,\n      security: this.securitySettings,\n      backup: this.backupSettings\n    };\n    \n    const dataStr = JSON.stringify(settings, null, 2);\n    const dataBlob = new Blob([dataStr], { type: 'application/json' });\n    const url = URL.createObjectURL(dataBlob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = 'admin-settings.json';\n    link.click();\n    URL.revokeObjectURL(url);\n    \n    this.toastService.showSuccess('Settings exported successfully!');\n  }\n\n  importSettings(event: any): void {\n    const file = event.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        try {\n          const settings = JSON.parse(e.target?.result as string);\n          if (settings.system) this.systemSettings = { ...this.systemSettings, ...settings.system };\n          if (settings.email) this.emailSettings = { ...this.emailSettings, ...settings.email };\n          if (settings.security) this.securitySettings = { ...this.securitySettings, ...settings.security };\n          if (settings.backup) this.backupSettings = { ...this.backupSettings, ...settings.backup };\n          \n          this.toastService.showSuccess('Settings imported successfully!');\n        } catch (error) {\n          this.toastService.showError('Invalid settings file format');\n        }\n      };\n      reader.readAsText(file);\n    }\n  }\n}\n", "<!-- Admin Settings Page -->\n<div class=\"container-fluid p-4 md:p-6 bg-[#edf1f4] dark:bg-[#121212] min-h-screen relative\">\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"></div>\n    <div class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"></div>\n  </div>\n\n  <!-- Page Content -->\n  <div class=\"relative z-10\">\n    <!-- Page Header -->\n    <div class=\"flex flex-col md:flex-row md:items-center md:justify-between mb-8\">\n      <div>\n        <h1 class=\"text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-2\">\n          System Settings\n        </h1>\n        <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\n          Configure system-wide settings and preferences\n        </p>\n      </div>\n      \n      <!-- Quick Actions -->\n      <div class=\"flex items-center gap-3 mt-4 md:mt-0\">\n        <button\n          (click)=\"exportSettings()\"\n          class=\"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] hover:bg-[#f8fafc] dark:hover:bg-[#1a1a1a] transition-colors\"\n        >\n          <i class=\"fas fa-download mr-2\"></i>Export Settings\n        </button>\n        \n        <label class=\"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] hover:bg-[#f8fafc] dark:hover:bg-[#1a1a1a] transition-colors cursor-pointer\">\n          <i class=\"fas fa-upload mr-2\"></i>Import Settings\n          <input type=\"file\" accept=\".json\" (change)=\"importSettings($event)\" class=\"hidden\" />\n        </label>\n      </div>\n    </div>\n\n    <!-- Settings Tabs -->\n    <div class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative\">\n      <!-- Decorative gradient top border -->\n      <div class=\"absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"></div>\n\n      <!-- Tab Navigation -->\n      <div class=\"border-b border-[#edf1f4] dark:border-[#2a2a2a]\">\n        <nav class=\"flex space-x-8 px-6\" aria-label=\"Tabs\">\n          <button\n            *ngFor=\"let tab of [\n              { id: 'system', label: 'System', icon: 'fas fa-cog' },\n              { id: 'email', label: 'Email', icon: 'fas fa-envelope' },\n              { id: 'security', label: 'Security', icon: 'fas fa-shield-alt' },\n              { id: 'backup', label: 'Backup', icon: 'fas fa-database' }\n            ]\"\n            (click)=\"setActiveTab(tab.id)\"\n            [class.border-[#4f5fad]]=\"activeTab === tab.id\"\n            [class.text-[#4f5fad]]=\"activeTab === tab.id\"\n            [class.border-transparent]=\"activeTab !== tab.id\"\n            [class.text-[#6d6870]]=\"activeTab !== tab.id\"\n            class=\"whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors hover:text-[#4f5fad] dark:hover:text-[#6d78c9] flex items-center\"\n          >\n            <i [class]=\"tab.icon + ' mr-2'\"></i>\n            {{ tab.label }}\n          </button>\n        </nav>\n      </div>\n\n      <!-- Tab Content -->\n      <div class=\"p-6\">\n        <!-- System Settings Tab -->\n        <div *ngIf=\"activeTab === 'system'\" class=\"space-y-6\">\n          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <!-- Basic Settings -->\n            <div class=\"space-y-4\">\n              <h3 class=\"text-lg font-semibold text-[#4f5fad] dark:text-[#6d78c9]\">Basic Settings</h3>\n              \n              <div>\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-2\">Site Name</label>\n                <input\n                  type=\"text\"\n                  [(ngModel)]=\"systemSettings.siteName\"\n                  class=\"w-full px-3 py-2 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1a1a1a] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] transition-all\"\n                />\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-2\">Site Description</label>\n                <textarea\n                  [(ngModel)]=\"systemSettings.siteDescription\"\n                  rows=\"3\"\n                  class=\"w-full px-3 py-2 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1a1a1a] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] transition-all\"\n                ></textarea>\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-2\">Default User Role</label>\n                <select\n                  [(ngModel)]=\"systemSettings.defaultUserRole\"\n                  class=\"w-full px-3 py-2 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1a1a1a] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] transition-all\"\n                >\n                  <option value=\"student\">Student</option>\n                  <option value=\"teacher\">Teacher</option>\n                </select>\n              </div>\n            </div>\n\n            <!-- System Controls -->\n            <div class=\"space-y-4\">\n              <h3 class=\"text-lg font-semibold text-[#4f5fad] dark:text-[#6d78c9]\">System Controls</h3>\n              \n              <!-- Toggle Settings -->\n              <div class=\"space-y-3\">\n                <div class=\"flex items-center justify-between\">\n                  <span class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]\">Maintenance Mode</span>\n                  <label class=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      [(ngModel)]=\"systemSettings.maintenanceMode\"\n                      class=\"sr-only peer\"\n                    />\n                    <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#4f5fad]/20 dark:peer-focus:ring-[#6d78c9]/20 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-[#4f5fad] dark:peer-checked:bg-[#6d78c9]\"></div>\n                  </label>\n                </div>\n\n                <div class=\"flex items-center justify-between\">\n                  <span class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]\">Allow Registration</span>\n                  <label class=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      [(ngModel)]=\"systemSettings.allowRegistration\"\n                      class=\"sr-only peer\"\n                    />\n                    <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#4f5fad]/20 dark:peer-focus:ring-[#6d78c9]/20 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-[#4f5fad] dark:peer-checked:bg-[#6d78c9]\"></div>\n                  </label>\n                </div>\n\n                <div class=\"flex items-center justify-between\">\n                  <span class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]\">Email Verification Required</span>\n                  <label class=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      [(ngModel)]=\"systemSettings.emailVerificationRequired\"\n                      class=\"sr-only peer\"\n                    />\n                    <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#4f5fad]/20 dark:peer-focus:ring-[#6d78c9]/20 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-[#4f5fad] dark:peer-checked:bg-[#6d78c9]\"></div>\n                  </label>\n                </div>\n              </div>\n\n              <!-- Quick Actions -->\n              <div class=\"pt-4 space-y-2\">\n                <button\n                  (click)=\"clearCache()\"\n                  class=\"w-full px-3 py-2 text-sm font-medium rounded-lg bg-[#4f5fad]/10 text-[#4f5fad] hover:bg-[#4f5fad]/20 transition-colors\"\n                >\n                  <i class=\"fas fa-broom mr-2\"></i>Clear System Cache\n                </button>\n                \n                <button\n                  (click)=\"restartSystem()\"\n                  class=\"w-full px-3 py-2 text-sm font-medium rounded-lg bg-[#ff6b69]/10 text-[#ff6b69] hover:bg-[#ff6b69]/20 transition-colors\"\n                >\n                  <i class=\"fas fa-power-off mr-2\"></i>Restart System\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <!-- Save Button -->\n          <div class=\"flex justify-end pt-6 border-t border-[#edf1f4] dark:border-[#2a2a2a]\">\n            <button\n              (click)=\"saveSettings('System')\"\n              [disabled]=\"saving\"\n              class=\"px-6 py-2.5 text-sm font-medium text-white rounded-lg bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#4f5fad] dark:to-[#6d78c9] hover:shadow-lg transition-all disabled:opacity-50\"\n            >\n              <i *ngIf=\"!saving\" class=\"fas fa-save mr-2\"></i>\n              <i *ngIf=\"saving\" class=\"fas fa-spinner fa-spin mr-2\"></i>\n              {{ saving ? 'Saving...' : 'Save System Settings' }}\n            </button>\n          </div>\n        </div>\n\n        <!-- Email Settings Tab -->\n        <div *ngIf=\"activeTab === 'email'\" class=\"space-y-6\">\n          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <!-- SMTP Configuration -->\n            <div class=\"space-y-4\">\n              <h3 class=\"text-lg font-semibold text-[#4f5fad] dark:text-[#6d78c9]\">SMTP Configuration</h3>\n              \n              <div>\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-2\">SMTP Host</label>\n                <input\n                  type=\"text\"\n                  [(ngModel)]=\"emailSettings.smtpHost\"\n                  placeholder=\"smtp.gmail.com\"\n                  class=\"w-full px-3 py-2 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1a1a1a] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] transition-all\"\n                />\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-2\">SMTP Port</label>\n                <input\n                  type=\"number\"\n                  [(ngModel)]=\"emailSettings.smtpPort\"\n                  class=\"w-full px-3 py-2 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1a1a1a] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] transition-all\"\n                />\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-2\">Username</label>\n                <input\n                  type=\"text\"\n                  [(ngModel)]=\"emailSettings.smtpUsername\"\n                  class=\"w-full px-3 py-2 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1a1a1a] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] transition-all\"\n                />\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-2\">Password</label>\n                <input\n                  type=\"password\"\n                  [(ngModel)]=\"emailSettings.smtpPassword\"\n                  class=\"w-full px-3 py-2 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1a1a1a] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] transition-all\"\n                />\n              </div>\n            </div>\n\n            <!-- Email Settings -->\n            <div class=\"space-y-4\">\n              <h3 class=\"text-lg font-semibold text-[#4f5fad] dark:text-[#6d78c9]\">Email Settings</h3>\n              \n              <div>\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-2\">From Email</label>\n                <input\n                  type=\"email\"\n                  [(ngModel)]=\"emailSettings.fromEmail\"\n                  placeholder=\"<EMAIL>\"\n                  class=\"w-full px-3 py-2 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1a1a1a] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] transition-all\"\n                />\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-2\">From Name</label>\n                <input\n                  type=\"text\"\n                  [(ngModel)]=\"emailSettings.fromName\"\n                  class=\"w-full px-3 py-2 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1a1a1a] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] transition-all\"\n                />\n              </div>\n\n              <div class=\"flex items-center justify-between\">\n                <span class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]\">Use Secure Connection</span>\n                <label class=\"relative inline-flex items-center cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    [(ngModel)]=\"emailSettings.smtpSecure\"\n                    class=\"sr-only peer\"\n                  />\n                  <div class=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#4f5fad]/20 dark:peer-focus:ring-[#6d78c9]/20 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-[#4f5fad] dark:peer-checked:bg-[#6d78c9]\"></div>\n                </label>\n              </div>\n\n              <!-- Test Email Actions -->\n              <div class=\"pt-4 space-y-2\">\n                <button\n                  (click)=\"testEmailConnection()\"\n                  class=\"w-full px-3 py-2 text-sm font-medium rounded-lg bg-[#4f5fad]/10 text-[#4f5fad] hover:bg-[#4f5fad]/20 transition-colors\"\n                >\n                  <i class=\"fas fa-plug mr-2\"></i>Test Connection\n                </button>\n                \n                <button\n                  (click)=\"sendTestEmail()\"\n                  class=\"w-full px-3 py-2 text-sm font-medium rounded-lg bg-[#00d4aa]/10 text-[#00d4aa] hover:bg-[#00d4aa]/20 transition-colors\"\n                >\n                  <i class=\"fas fa-paper-plane mr-2\"></i>Send Test Email\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <!-- Save Button -->\n          <div class=\"flex justify-end pt-6 border-t border-[#edf1f4] dark:border-[#2a2a2a]\">\n            <button\n              (click)=\"saveSettings('Email')\"\n              [disabled]=\"saving\"\n              class=\"px-6 py-2.5 text-sm font-medium text-white rounded-lg bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#4f5fad] dark:to-[#6d78c9] hover:shadow-lg transition-all disabled:opacity-50\"\n            >\n              <i *ngIf=\"!saving\" class=\"fas fa-save mr-2\"></i>\n              <i *ngIf=\"saving\" class=\"fas fa-spinner fa-spin mr-2\"></i>\n              {{ saving ? 'Saving...' : 'Save Email Settings' }}\n            </button>\n          </div>\n        </div>\n\n        <!-- Security Settings Tab -->\n        <div *ngIf=\"activeTab === 'security'\" class=\"space-y-6\">\n          <!-- Security content will be added here -->\n          <div class=\"text-center py-12\">\n            <i class=\"fas fa-shield-alt text-4xl text-[#4f5fad] dark:text-[#6d78c9] mb-4\"></i>\n            <h3 class=\"text-lg font-semibold text-[#4f5fad] dark:text-[#6d78c9] mb-2\">Security Settings</h3>\n            <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">Configure security policies and access controls</p>\n          </div>\n        </div>\n\n        <!-- Backup Settings Tab -->\n        <div *ngIf=\"activeTab === 'backup'\" class=\"space-y-6\">\n          <!-- Backup content will be added here -->\n          <div class=\"text-center py-12\">\n            <i class=\"fas fa-database text-4xl text-[#4f5fad] dark:text-[#6d78c9] mb-4\"></i>\n            <h3 class=\"text-lg font-semibold text-[#4f5fad] dark:text-[#6d78c9] mb-2\">Backup & Restore</h3>\n            <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">Manage system backups and data recovery</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;IC6CUA,EAAA,CAAAC,cAAA,iBAaC;IANCD,EAAA,CAAAE,UAAA,mBAAAC,kEAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,YAAA,CAAAL,MAAA,CAAAM,EAAA,CAAoB;IAAA,EAAC;IAO9Bb,EAAA,CAAAc,SAAA,QAAoC;IACpCd,EAAA,CAAAe,MAAA,GACF;IAAAf,EAAA,CAAAgB,YAAA,EAAS;;;;;IARPhB,EAAA,CAAAiB,WAAA,qBAAAC,MAAA,CAAAC,SAAA,KAAAZ,MAAA,CAAAM,EAAA,CAA+C,mBAAAK,MAAA,CAAAC,SAAA,KAAAZ,MAAA,CAAAM,EAAA,wBAAAK,MAAA,CAAAC,SAAA,KAAAZ,MAAA,CAAAM,EAAA,oBAAAK,MAAA,CAAAC,SAAA,KAAAZ,MAAA,CAAAM,EAAA;IAM5Cb,EAAA,CAAAoB,SAAA,GAA4B;IAA5BpB,EAAA,CAAAqB,UAAA,CAAAd,MAAA,CAAAe,IAAA,WAA4B;IAC/BtB,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAhB,MAAA,CAAAiB,KAAA,MACF;;;;;IAgHIxB,EAAA,CAAAc,SAAA,YAAgD;;;;;IAChDd,EAAA,CAAAc,SAAA,YAA0D;;;;;;IA1GhEd,EAAA,CAAAC,cAAA,cAAsD;IAIqBD,EAAA,CAAAe,MAAA,qBAAc;IAAAf,EAAA,CAAAgB,YAAA,EAAK;IAExFhB,EAAA,CAAAC,cAAA,UAAK;IAC8ED,EAAA,CAAAe,MAAA,gBAAS;IAAAf,EAAA,CAAAgB,YAAA,EAAQ;IAClGhB,EAAA,CAAAC,cAAA,gBAIE;IAFAD,EAAA,CAAAE,UAAA,2BAAAuB,sEAAAC,MAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAsB,IAAA;MAAA,MAAAC,OAAA,GAAA5B,EAAA,CAAAU,aAAA;MAAA,OAAaV,EAAA,CAAAW,WAAA,CAAAiB,OAAA,CAAAC,cAAA,CAAAC,QAAA,GAAAJ,MAAA,CACzB;IAAA,EADiD;IAFvC1B,EAAA,CAAAgB,YAAA,EAIE;IAGJhB,EAAA,CAAAC,cAAA,UAAK;IAC8ED,EAAA,CAAAe,MAAA,wBAAgB;IAAAf,EAAA,CAAAgB,YAAA,EAAQ;IACzGhB,EAAA,CAAAC,cAAA,oBAIC;IAHCD,EAAA,CAAAE,UAAA,2BAAA6B,0EAAAL,MAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAsB,IAAA;MAAA,MAAAK,OAAA,GAAAhC,EAAA,CAAAU,aAAA;MAAA,OAAaV,EAAA,CAAAW,WAAA,CAAAqB,OAAA,CAAAH,cAAA,CAAAI,eAAA,GAAAP,MAAA,CACzB;IAAA,EADwD;IAG7C1B,EAAA,CAAAgB,YAAA,EAAW;IAGdhB,EAAA,CAAAC,cAAA,WAAK;IAC8ED,EAAA,CAAAe,MAAA,yBAAiB;IAAAf,EAAA,CAAAgB,YAAA,EAAQ;IAC1GhB,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAE,UAAA,2BAAAgC,wEAAAR,MAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAsB,IAAA;MAAA,MAAAQ,OAAA,GAAAnC,EAAA,CAAAU,aAAA;MAAA,OAAaV,EAAA,CAAAW,WAAA,CAAAwB,OAAA,CAAAN,cAAA,CAAAO,eAAA,GAAAV,MAAA,CACzB;IAAA,EADwD;IAG5C1B,EAAA,CAAAC,cAAA,kBAAwB;IAAAD,EAAA,CAAAe,MAAA,eAAO;IAAAf,EAAA,CAAAgB,YAAA,EAAS;IACxChB,EAAA,CAAAC,cAAA,kBAAwB;IAAAD,EAAA,CAAAe,MAAA,eAAO;IAAAf,EAAA,CAAAgB,YAAA,EAAS;IAM9ChB,EAAA,CAAAC,cAAA,eAAuB;IACgDD,EAAA,CAAAe,MAAA,uBAAe;IAAAf,EAAA,CAAAgB,YAAA,EAAK;IAGzFhB,EAAA,CAAAC,cAAA,eAAuB;IAEkDD,EAAA,CAAAe,MAAA,wBAAgB;IAAAf,EAAA,CAAAgB,YAAA,EAAO;IAC5FhB,EAAA,CAAAC,cAAA,iBAAgE;IAG5DD,EAAA,CAAAE,UAAA,2BAAAmC,uEAAAX,MAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAsB,IAAA;MAAA,MAAAW,OAAA,GAAAtC,EAAA,CAAAU,aAAA;MAAA,OAAaV,EAAA,CAAAW,WAAA,CAAA2B,OAAA,CAAAT,cAAA,CAAAU,eAAA,GAAAb,MAAA,CAC7B;IAAA,EAD4D;IAF9C1B,EAAA,CAAAgB,YAAA,EAIE;IACFhB,EAAA,CAAAc,SAAA,eAAuf;IACzfd,EAAA,CAAAgB,YAAA,EAAQ;IAGVhB,EAAA,CAAAC,cAAA,eAA+C;IACwBD,EAAA,CAAAe,MAAA,0BAAkB;IAAAf,EAAA,CAAAgB,YAAA,EAAO;IAC9FhB,EAAA,CAAAC,cAAA,iBAAgE;IAG5DD,EAAA,CAAAE,UAAA,2BAAAsC,uEAAAd,MAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAsB,IAAA;MAAA,MAAAc,OAAA,GAAAzC,EAAA,CAAAU,aAAA;MAAA,OAAaV,EAAA,CAAAW,WAAA,CAAA8B,OAAA,CAAAZ,cAAA,CAAAa,iBAAA,GAAAhB,MAAA,CAC7B;IAAA,EAD8D;IAFhD1B,EAAA,CAAAgB,YAAA,EAIE;IACFhB,EAAA,CAAAc,SAAA,eAAuf;IACzfd,EAAA,CAAAgB,YAAA,EAAQ;IAGVhB,EAAA,CAAAC,cAAA,eAA+C;IACwBD,EAAA,CAAAe,MAAA,mCAA2B;IAAAf,EAAA,CAAAgB,YAAA,EAAO;IACvGhB,EAAA,CAAAC,cAAA,iBAAgE;IAG5DD,EAAA,CAAAE,UAAA,2BAAAyC,uEAAAjB,MAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAsB,IAAA;MAAA,MAAAiB,OAAA,GAAA5C,EAAA,CAAAU,aAAA;MAAA,OAAaV,EAAA,CAAAW,WAAA,CAAAiC,OAAA,CAAAf,cAAA,CAAAgB,yBAAA,GAAAnB,MAAA,CAC7B;IAAA,EADsE;IAFxD1B,EAAA,CAAAgB,YAAA,EAIE;IACFhB,EAAA,CAAAc,SAAA,eAAuf;IACzfd,EAAA,CAAAgB,YAAA,EAAQ;IAKZhB,EAAA,CAAAC,cAAA,eAA4B;IAExBD,EAAA,CAAAE,UAAA,mBAAA4C,gEAAA;MAAA9C,EAAA,CAAAK,aAAA,CAAAsB,IAAA;MAAA,MAAAoB,OAAA,GAAA/C,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAoC,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAGtBhD,EAAA,CAAAc,SAAA,aAAiC;IAAAd,EAAA,CAAAe,MAAA,2BACnC;IAAAf,EAAA,CAAAgB,YAAA,EAAS;IAEThB,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAE,UAAA,mBAAA+C,gEAAA;MAAAjD,EAAA,CAAAK,aAAA,CAAAsB,IAAA;MAAA,MAAAuB,OAAA,GAAAlD,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAuC,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAGzBnD,EAAA,CAAAc,SAAA,aAAqC;IAAAd,EAAA,CAAAe,MAAA,uBACvC;IAAAf,EAAA,CAAAgB,YAAA,EAAS;IAMfhB,EAAA,CAAAC,cAAA,eAAmF;IAE/ED,EAAA,CAAAE,UAAA,mBAAAkD,gEAAA;MAAApD,EAAA,CAAAK,aAAA,CAAAsB,IAAA;MAAA,MAAA0B,OAAA,GAAArD,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA0C,OAAA,CAAAC,YAAA,CAAa,QAAQ,CAAC;IAAA,EAAC;IAIhCtD,EAAA,CAAAuD,UAAA,KAAAC,2CAAA,gBAAgD;IAChDxD,EAAA,CAAAuD,UAAA,KAAAE,2CAAA,gBAA0D;IAC1DzD,EAAA,CAAAe,MAAA,IACF;IAAAf,EAAA,CAAAgB,YAAA,EAAS;;;;IAlGHhB,EAAA,CAAAoB,SAAA,GAAqC;IAArCpB,EAAA,CAAA0D,UAAA,YAAAC,MAAA,CAAA9B,cAAA,CAAAC,QAAA,CAAqC;IAQrC9B,EAAA,CAAAoB,SAAA,GAA4C;IAA5CpB,EAAA,CAAA0D,UAAA,YAAAC,MAAA,CAAA9B,cAAA,CAAAI,eAAA,CAA4C;IAS5CjC,EAAA,CAAAoB,SAAA,GAA4C;IAA5CpB,EAAA,CAAA0D,UAAA,YAAAC,MAAA,CAAA9B,cAAA,CAAAO,eAAA,CAA4C;IAoBxCpC,EAAA,CAAAoB,SAAA,IAA4C;IAA5CpB,EAAA,CAAA0D,UAAA,YAAAC,MAAA,CAAA9B,cAAA,CAAAU,eAAA,CAA4C;IAY5CvC,EAAA,CAAAoB,SAAA,GAA8C;IAA9CpB,EAAA,CAAA0D,UAAA,YAAAC,MAAA,CAAA9B,cAAA,CAAAa,iBAAA,CAA8C;IAY9C1C,EAAA,CAAAoB,SAAA,GAAsD;IAAtDpB,EAAA,CAAA0D,UAAA,YAAAC,MAAA,CAAA9B,cAAA,CAAAgB,yBAAA,CAAsD;IA+B9D7C,EAAA,CAAAoB,SAAA,IAAmB;IAAnBpB,EAAA,CAAA0D,UAAA,aAAAC,MAAA,CAAAC,MAAA,CAAmB;IAGf5D,EAAA,CAAAoB,SAAA,GAAa;IAAbpB,EAAA,CAAA0D,UAAA,UAAAC,MAAA,CAAAC,MAAA,CAAa;IACb5D,EAAA,CAAAoB,SAAA,GAAY;IAAZpB,EAAA,CAAA0D,UAAA,SAAAC,MAAA,CAAAC,MAAA,CAAY;IAChB5D,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAoC,MAAA,CAAAC,MAAA,6CACF;;;;;IA8GE5D,EAAA,CAAAc,SAAA,YAAgD;;;;;IAChDd,EAAA,CAAAc,SAAA,YAA0D;;;;;;IA1GhEd,EAAA,CAAAC,cAAA,cAAqD;IAIsBD,EAAA,CAAAe,MAAA,yBAAkB;IAAAf,EAAA,CAAAgB,YAAA,EAAK;IAE5FhB,EAAA,CAAAC,cAAA,UAAK;IAC8ED,EAAA,CAAAe,MAAA,gBAAS;IAAAf,EAAA,CAAAgB,YAAA,EAAQ;IAClGhB,EAAA,CAAAC,cAAA,gBAKE;IAHAD,EAAA,CAAAE,UAAA,2BAAA2D,sEAAAnC,MAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAyD,IAAA;MAAA,MAAAC,OAAA,GAAA/D,EAAA,CAAAU,aAAA;MAAA,OAAaV,EAAA,CAAAW,WAAA,CAAAoD,OAAA,CAAAC,aAAA,CAAAC,QAAA,GAAAvC,MAAA,CACzB;IAAA,EADgD;IAFtC1B,EAAA,CAAAgB,YAAA,EAKE;IAGJhB,EAAA,CAAAC,cAAA,UAAK;IAC8ED,EAAA,CAAAe,MAAA,iBAAS;IAAAf,EAAA,CAAAgB,YAAA,EAAQ;IAClGhB,EAAA,CAAAC,cAAA,iBAIE;IAFAD,EAAA,CAAAE,UAAA,2BAAAgE,uEAAAxC,MAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAyD,IAAA;MAAA,MAAAK,OAAA,GAAAnE,EAAA,CAAAU,aAAA;MAAA,OAAaV,EAAA,CAAAW,WAAA,CAAAwD,OAAA,CAAAH,aAAA,CAAAI,QAAA,GAAA1C,MAAA,CACzB;IAAA,EADgD;IAFtC1B,EAAA,CAAAgB,YAAA,EAIE;IAGJhB,EAAA,CAAAC,cAAA,WAAK;IAC8ED,EAAA,CAAAe,MAAA,gBAAQ;IAAAf,EAAA,CAAAgB,YAAA,EAAQ;IACjGhB,EAAA,CAAAC,cAAA,iBAIE;IAFAD,EAAA,CAAAE,UAAA,2BAAAmE,uEAAA3C,MAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAyD,IAAA;MAAA,MAAAQ,OAAA,GAAAtE,EAAA,CAAAU,aAAA;MAAA,OAAaV,EAAA,CAAAW,WAAA,CAAA2D,OAAA,CAAAN,aAAA,CAAAO,YAAA,GAAA7C,MAAA,CACzB;IAAA,EADoD;IAF1C1B,EAAA,CAAAgB,YAAA,EAIE;IAGJhB,EAAA,CAAAC,cAAA,WAAK;IAC8ED,EAAA,CAAAe,MAAA,gBAAQ;IAAAf,EAAA,CAAAgB,YAAA,EAAQ;IACjGhB,EAAA,CAAAC,cAAA,iBAIE;IAFAD,EAAA,CAAAE,UAAA,2BAAAsE,uEAAA9C,MAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAyD,IAAA;MAAA,MAAAW,OAAA,GAAAzE,EAAA,CAAAU,aAAA;MAAA,OAAaV,EAAA,CAAAW,WAAA,CAAA8D,OAAA,CAAAT,aAAA,CAAAU,YAAA,GAAAhD,MAAA,CACzB;IAAA,EADoD;IAF1C1B,EAAA,CAAAgB,YAAA,EAIE;IAKNhB,EAAA,CAAAC,cAAA,eAAuB;IACgDD,EAAA,CAAAe,MAAA,sBAAc;IAAAf,EAAA,CAAAgB,YAAA,EAAK;IAExFhB,EAAA,CAAAC,cAAA,WAAK;IAC8ED,EAAA,CAAAe,MAAA,kBAAU;IAAAf,EAAA,CAAAgB,YAAA,EAAQ;IACnGhB,EAAA,CAAAC,cAAA,iBAKE;IAHAD,EAAA,CAAAE,UAAA,2BAAAyE,uEAAAjD,MAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAyD,IAAA;MAAA,MAAAc,OAAA,GAAA5E,EAAA,CAAAU,aAAA;MAAA,OAAaV,EAAA,CAAAW,WAAA,CAAAiE,OAAA,CAAAZ,aAAA,CAAAa,SAAA,GAAAnD,MAAA,CACzB;IAAA,EADiD;IAFvC1B,EAAA,CAAAgB,YAAA,EAKE;IAGJhB,EAAA,CAAAC,cAAA,WAAK;IAC8ED,EAAA,CAAAe,MAAA,iBAAS;IAAAf,EAAA,CAAAgB,YAAA,EAAQ;IAClGhB,EAAA,CAAAC,cAAA,iBAIE;IAFAD,EAAA,CAAAE,UAAA,2BAAA4E,uEAAApD,MAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAyD,IAAA;MAAA,MAAAiB,OAAA,GAAA/E,EAAA,CAAAU,aAAA;MAAA,OAAaV,EAAA,CAAAW,WAAA,CAAAoE,OAAA,CAAAf,aAAA,CAAAgB,QAAA,GAAAtD,MAAA,CACzB;IAAA,EADgD;IAFtC1B,EAAA,CAAAgB,YAAA,EAIE;IAGJhB,EAAA,CAAAC,cAAA,eAA+C;IACwBD,EAAA,CAAAe,MAAA,6BAAqB;IAAAf,EAAA,CAAAgB,YAAA,EAAO;IACjGhB,EAAA,CAAAC,cAAA,iBAAgE;IAG5DD,EAAA,CAAAE,UAAA,2BAAA+E,uEAAAvD,MAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAyD,IAAA;MAAA,MAAAoB,OAAA,GAAAlF,EAAA,CAAAU,aAAA;MAAA,OAAaV,EAAA,CAAAW,WAAA,CAAAuE,OAAA,CAAAlB,aAAA,CAAAmB,UAAA,GAAAzD,MAAA,CAC3B;IAAA,EADoD;IAFxC1B,EAAA,CAAAgB,YAAA,EAIE;IACFhB,EAAA,CAAAc,SAAA,eAAuf;IACzfd,EAAA,CAAAgB,YAAA,EAAQ;IAIVhB,EAAA,CAAAC,cAAA,eAA4B;IAExBD,EAAA,CAAAE,UAAA,mBAAAkF,gEAAA;MAAApF,EAAA,CAAAK,aAAA,CAAAyD,IAAA;MAAA,MAAAuB,OAAA,GAAArF,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA0E,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IAG/BtF,EAAA,CAAAc,SAAA,aAAgC;IAAAd,EAAA,CAAAe,MAAA,wBAClC;IAAAf,EAAA,CAAAgB,YAAA,EAAS;IAEThB,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAE,UAAA,mBAAAqF,gEAAA;MAAAvF,EAAA,CAAAK,aAAA,CAAAyD,IAAA;MAAA,MAAA0B,OAAA,GAAAxF,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA6E,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAGzBzF,EAAA,CAAAc,SAAA,aAAuC;IAAAd,EAAA,CAAAe,MAAA,wBACzC;IAAAf,EAAA,CAAAgB,YAAA,EAAS;IAMfhB,EAAA,CAAAC,cAAA,eAAmF;IAE/ED,EAAA,CAAAE,UAAA,mBAAAwF,gEAAA;MAAA1F,EAAA,CAAAK,aAAA,CAAAyD,IAAA;MAAA,MAAA6B,OAAA,GAAA3F,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAgF,OAAA,CAAArC,YAAA,CAAa,OAAO,CAAC;IAAA,EAAC;IAI/BtD,EAAA,CAAAuD,UAAA,KAAAqC,2CAAA,gBAAgD;IAChD5F,EAAA,CAAAuD,UAAA,KAAAsC,2CAAA,gBAA0D;IAC1D7F,EAAA,CAAAe,MAAA,IACF;IAAAf,EAAA,CAAAgB,YAAA,EAAS;;;;IAlGHhB,EAAA,CAAAoB,SAAA,GAAoC;IAApCpB,EAAA,CAAA0D,UAAA,YAAAoC,MAAA,CAAA9B,aAAA,CAAAC,QAAA,CAAoC;IAUpCjE,EAAA,CAAAoB,SAAA,GAAoC;IAApCpB,EAAA,CAAA0D,UAAA,YAAAoC,MAAA,CAAA9B,aAAA,CAAAI,QAAA,CAAoC;IASpCpE,EAAA,CAAAoB,SAAA,GAAwC;IAAxCpB,EAAA,CAAA0D,UAAA,YAAAoC,MAAA,CAAA9B,aAAA,CAAAO,YAAA,CAAwC;IASxCvE,EAAA,CAAAoB,SAAA,GAAwC;IAAxCpB,EAAA,CAAA0D,UAAA,YAAAoC,MAAA,CAAA9B,aAAA,CAAAU,YAAA,CAAwC;IAcxC1E,EAAA,CAAAoB,SAAA,GAAqC;IAArCpB,EAAA,CAAA0D,UAAA,YAAAoC,MAAA,CAAA9B,aAAA,CAAAa,SAAA,CAAqC;IAUrC7E,EAAA,CAAAoB,SAAA,GAAoC;IAApCpB,EAAA,CAAA0D,UAAA,YAAAoC,MAAA,CAAA9B,aAAA,CAAAgB,QAAA,CAAoC;IAUlChF,EAAA,CAAAoB,SAAA,GAAsC;IAAtCpB,EAAA,CAAA0D,UAAA,YAAAoC,MAAA,CAAA9B,aAAA,CAAAmB,UAAA,CAAsC;IA8B5CnF,EAAA,CAAAoB,SAAA,IAAmB;IAAnBpB,EAAA,CAAA0D,UAAA,aAAAoC,MAAA,CAAAlC,MAAA,CAAmB;IAGf5D,EAAA,CAAAoB,SAAA,GAAa;IAAbpB,EAAA,CAAA0D,UAAA,UAAAoC,MAAA,CAAAlC,MAAA,CAAa;IACb5D,EAAA,CAAAoB,SAAA,GAAY;IAAZpB,EAAA,CAAA0D,UAAA,SAAAoC,MAAA,CAAAlC,MAAA,CAAY;IAChB5D,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAuE,MAAA,CAAAlC,MAAA,4CACF;;;;;IAKJ5D,EAAA,CAAAC,cAAA,cAAwD;IAGpDD,EAAA,CAAAc,SAAA,YAAkF;IAClFd,EAAA,CAAAC,cAAA,aAA0E;IAAAD,EAAA,CAAAe,MAAA,wBAAiB;IAAAf,EAAA,CAAAgB,YAAA,EAAK;IAChGhB,EAAA,CAAAC,cAAA,WAAsD;IAAAD,EAAA,CAAAe,MAAA,sDAA+C;IAAAf,EAAA,CAAAgB,YAAA,EAAI;;;;;IAK7GhB,EAAA,CAAAC,cAAA,cAAsD;IAGlDD,EAAA,CAAAc,SAAA,YAAgF;IAChFd,EAAA,CAAAC,cAAA,aAA0E;IAAAD,EAAA,CAAAe,MAAA,uBAAgB;IAAAf,EAAA,CAAAgB,YAAA,EAAK;IAC/FhB,EAAA,CAAAC,cAAA,WAAsD;IAAAD,EAAA,CAAAe,MAAA,8CAAuC;IAAAf,EAAA,CAAAgB,YAAA,EAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AD7S7G,OAAM,MAAO+E,sBAAsB;EAoDjCC,YACUC,YAA0B;IAA1B,KAAAA,YAAY,GAAZA,YAAY;IAnDtB;IACA,KAAApE,cAAc,GAAG;MACfC,QAAQ,EAAE,iBAAiB;MAC3BG,eAAe,EAAE,2BAA2B;MAC5CM,eAAe,EAAE,KAAK;MACtBG,iBAAiB,EAAE,IAAI;MACvBG,yBAAyB,EAAE,IAAI;MAC/BqD,iBAAiB,EAAE,EAAE;MACrBC,cAAc,EAAE,EAAE;MAClB/D,eAAe,EAAE,SAAS;MAC1BgE,iBAAiB,EAAE,CAAC;MACpBC,2BAA2B,EAAE;KAC9B;IAED;IACA,KAAArC,aAAa,GAAG;MACdC,QAAQ,EAAE,EAAE;MACZG,QAAQ,EAAE,GAAG;MACbG,YAAY,EAAE,EAAE;MAChBG,YAAY,EAAE,EAAE;MAChBS,UAAU,EAAE,IAAI;MAChBN,SAAS,EAAE,EAAE;MACbG,QAAQ,EAAE;KACX;IAED;IACA,KAAAsB,gBAAgB,GAAG;MACjBC,eAAe,EAAE,KAAK;MACtBC,gBAAgB,EAAE,CAAC;MACnBC,eAAe,EAAE,EAAE;MACnBC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE,IAAI;MACpBC,iBAAiB,EAAE,KAAK;MACxBC,UAAU,EAAE;KACb;IAED;IACA,KAAAC,cAAc,GAAG;MACfC,UAAU,EAAE,IAAI;MAChBC,eAAe,EAAE,OAAO;MACxBC,eAAe,EAAE,EAAE;MACnBC,cAAc,EAAE,OAAO;MACvBC,UAAU,EAAE,IAAIC,IAAI;KACrB;IAED;IACA,KAAAjG,SAAS,GAAG,QAAQ;IACpB,KAAAkG,OAAO,GAAG,KAAK;IACf,KAAAzD,MAAM,GAAG,KAAK;EAIX;EAEH0D,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,IAAI,CAACF,OAAO,GAAG,IAAI;IACnB;IACAG,UAAU,CAAC,MAAK;MACd,IAAI,CAACH,OAAO,GAAG,KAAK;IACtB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA/D,YAAYA,CAACmE,YAAoB;IAC/B,IAAI,CAAC7D,MAAM,GAAG,IAAI;IAElB;IACA4D,UAAU,CAAC,MAAK;MACd,IAAI,CAAC5D,MAAM,GAAG,KAAK;MACnB,IAAI,CAACqC,YAAY,CAACyB,WAAW,CAAC,GAAGD,YAAY,+BAA+B,CAAC;IAC/E,CAAC,EAAE,IAAI,CAAC;EACV;EAEA7G,YAAYA,CAAC+G,GAAW;IACtB,IAAI,CAACxG,SAAS,GAAGwG,GAAG;EACtB;EAEA;EACAC,qBAAqBA,CAAA;IACnB,IAAI,CAAC/F,cAAc,CAACU,eAAe,GAAG,CAAC,IAAI,CAACV,cAAc,CAACU,eAAe;IAC1E,MAAMsF,MAAM,GAAG,IAAI,CAAChG,cAAc,CAACU,eAAe,GAAG,SAAS,GAAG,UAAU;IAC3E,IAAI,CAAC0D,YAAY,CAAC6B,QAAQ,CAAC,oBAAoBD,MAAM,EAAE,CAAC;EAC1D;EAEA7E,UAAUA,CAAA;IACR,IAAI,CAACiD,YAAY,CAACyB,WAAW,CAAC,oCAAoC,CAAC;EACrE;EAEAvE,aAAaA,CAAA;IACX,IAAI4E,OAAO,CAAC,uFAAuF,CAAC,EAAE;MACpG,IAAI,CAAC9B,YAAY,CAAC6B,QAAQ,CAAC,6BAA6B,CAAC;;EAE7D;EAEA;EACAE,YAAYA,CAAA;IACV,IAAI,CAAC/B,YAAY,CAAC6B,QAAQ,CAAC,iDAAiD,CAAC;IAC7EN,UAAU,CAAC,MAAK;MACd,IAAI,CAACV,cAAc,CAACK,UAAU,GAAG,IAAIC,IAAI,EAAE;MAC3C,IAAI,CAACnB,YAAY,CAACyB,WAAW,CAAC,8BAA8B,CAAC;IAC/D,CAAC,EAAE,IAAI,CAAC;EACV;EAEAO,aAAaA,CAAA;IACX,IAAIF,OAAO,CAAC,iFAAiF,CAAC,EAAE;MAC9F,IAAI,CAAC9B,YAAY,CAAC6B,QAAQ,CAAC,0BAA0B,CAAC;;EAE1D;EAEA;EACAI,cAAcA,CAAA;IACZ,MAAMC,KAAK,GAAG,gEAAgE;IAC9E,IAAIC,MAAM,GAAG,EAAE;IACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3BD,MAAM,IAAID,KAAK,CAACG,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAGN,KAAK,CAACO,MAAM,CAAC,CAAC;;IAElE,OAAON,MAAM;EACf;EAEAO,gBAAgBA,CAACC,EAAU;IACzB,IAAIA,EAAE,IAAI,CAAC,IAAI,CAACtC,gBAAgB,CAACO,UAAU,CAACgC,QAAQ,CAACD,EAAE,CAAC,EAAE;MACxD,IAAI,CAACtC,gBAAgB,CAACO,UAAU,CAACiC,IAAI,CAACF,EAAE,CAAC;MACzC,IAAI,CAAC3C,YAAY,CAACyB,WAAW,CAAC,MAAMkB,EAAE,qBAAqB,CAAC;;EAEhE;EAEAG,qBAAqBA,CAACH,EAAU;IAC9B,MAAMI,KAAK,GAAG,IAAI,CAAC1C,gBAAgB,CAACO,UAAU,CAACoC,OAAO,CAACL,EAAE,CAAC;IAC1D,IAAII,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAC1C,gBAAgB,CAACO,UAAU,CAACqC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACjD,IAAI,CAAC/C,YAAY,CAACyB,WAAW,CAAC,MAAMkB,EAAE,yBAAyB,CAAC;;EAEpE;EAEA;EACAtD,mBAAmBA,CAAA;IACjB,IAAI,CAACW,YAAY,CAAC6B,QAAQ,CAAC,6BAA6B,CAAC;IACzDN,UAAU,CAAC,MAAK;MACd,IAAI,CAACvB,YAAY,CAACyB,WAAW,CAAC,mCAAmC,CAAC;IACpE,CAAC,EAAE,IAAI,CAAC;EACV;EAEAjC,aAAaA,CAAA;IACX,IAAI,CAACQ,YAAY,CAAC6B,QAAQ,CAAC,uBAAuB,CAAC;IACnDN,UAAU,CAAC,MAAK;MACd,IAAI,CAACvB,YAAY,CAACyB,WAAW,CAAC,+BAA+B,CAAC;IAChE,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACAyB,cAAcA,CAAA;IACZ,MAAMC,QAAQ,GAAG;MACfC,MAAM,EAAE,IAAI,CAACxH,cAAc;MAC3ByH,KAAK,EAAE,IAAI,CAACtF,aAAa;MACzBuF,QAAQ,EAAE,IAAI,CAACjD,gBAAgB;MAC/BkD,MAAM,EAAE,IAAI,CAAC1C;KACd;IAED,MAAM2C,OAAO,GAAGC,IAAI,CAACC,SAAS,CAACP,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;IACjD,MAAMQ,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAACJ,OAAO,CAAC,EAAE;MAAEK,IAAI,EAAE;IAAkB,CAAE,CAAC;IAClE,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,QAAQ,CAAC;IACzC,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;IACfG,IAAI,CAACI,QAAQ,GAAG,qBAAqB;IACrCJ,IAAI,CAACK,KAAK,EAAE;IACZP,GAAG,CAACQ,eAAe,CAACT,GAAG,CAAC;IAExB,IAAI,CAAC9D,YAAY,CAACyB,WAAW,CAAC,iCAAiC,CAAC;EAClE;EAEA+C,cAAcA,CAACC,KAAU;IACvB,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,MAAMG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAI;QACpB,IAAI;UACF,MAAM7B,QAAQ,GAAGM,IAAI,CAACwB,KAAK,CAACD,CAAC,CAACL,MAAM,EAAExC,MAAgB,CAAC;UACvD,IAAIgB,QAAQ,CAACC,MAAM,EAAE,IAAI,CAACxH,cAAc,GAAG;YAAE,GAAG,IAAI,CAACA,cAAc;YAAE,GAAGuH,QAAQ,CAACC;UAAM,CAAE;UACzF,IAAID,QAAQ,CAACE,KAAK,EAAE,IAAI,CAACtF,aAAa,GAAG;YAAE,GAAG,IAAI,CAACA,aAAa;YAAE,GAAGoF,QAAQ,CAACE;UAAK,CAAE;UACrF,IAAIF,QAAQ,CAACG,QAAQ,EAAE,IAAI,CAACjD,gBAAgB,GAAG;YAAE,GAAG,IAAI,CAACA,gBAAgB;YAAE,GAAG8C,QAAQ,CAACG;UAAQ,CAAE;UACjG,IAAIH,QAAQ,CAACI,MAAM,EAAE,IAAI,CAAC1C,cAAc,GAAG;YAAE,GAAG,IAAI,CAACA,cAAc;YAAE,GAAGsC,QAAQ,CAACI;UAAM,CAAE;UAEzF,IAAI,CAACvD,YAAY,CAACyB,WAAW,CAAC,iCAAiC,CAAC;SACjE,CAAC,OAAOyD,KAAK,EAAE;UACd,IAAI,CAAClF,YAAY,CAACmF,SAAS,CAAC,8BAA8B,CAAC;;MAE/D,CAAC;MACDN,MAAM,CAACO,UAAU,CAACV,IAAI,CAAC;;EAE3B;;;uBAlMW5E,sBAAsB,EAAA/F,EAAA,CAAAsL,iBAAA,CAAAC,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAtBzF,sBAAsB;MAAA0F,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPnC/L,EAAA,CAAAC,cAAA,aAA6F;UAGzFD,EAAA,CAAAc,SAAA,aAA6K;UAE/Kd,EAAA,CAAAgB,YAAA,EAAM;UAGNhB,EAAA,CAAAC,cAAA,aAA2B;UAKnBD,EAAA,CAAAe,MAAA,wBACF;UAAAf,EAAA,CAAAgB,YAAA,EAAK;UACLhB,EAAA,CAAAC,cAAA,WAAsD;UACpDD,EAAA,CAAAe,MAAA,wDACF;UAAAf,EAAA,CAAAgB,YAAA,EAAI;UAINhB,EAAA,CAAAC,cAAA,cAAkD;UAE9CD,EAAA,CAAAE,UAAA,mBAAA+L,yDAAA;YAAA,OAASD,GAAA,CAAA7C,cAAA,EAAgB;UAAA,EAAC;UAG1BnJ,EAAA,CAAAc,SAAA,aAAoC;UAAAd,EAAA,CAAAe,MAAA,wBACtC;UAAAf,EAAA,CAAAgB,YAAA,EAAS;UAEThB,EAAA,CAAAC,cAAA,iBAAyQ;UACvQD,EAAA,CAAAc,SAAA,aAAkC;UAAAd,EAAA,CAAAe,MAAA,wBAClC;UAAAf,EAAA,CAAAC,cAAA,iBAAqF;UAAnDD,EAAA,CAAAE,UAAA,oBAAAgM,yDAAAxK,MAAA;YAAA,OAAUsK,GAAA,CAAAvB,cAAA,CAAA/I,MAAA,CAAsB;UAAA,EAAC;UAAnE1B,EAAA,CAAAgB,YAAA,EAAqF;UAM3FhB,EAAA,CAAAC,cAAA,eAAiM;UAE/LD,EAAA,CAAAc,SAAA,eAA0I;UAG1Id,EAAA,CAAAC,cAAA,eAA6D;UAEzDD,EAAA,CAAAuD,UAAA,KAAA4I,yCAAA,sBAgBS;UACXnM,EAAA,CAAAgB,YAAA,EAAM;UAIRhB,EAAA,CAAAC,cAAA,eAAiB;UAEfD,EAAA,CAAAuD,UAAA,KAAA6I,sCAAA,oBA8GM;UAGNpM,EAAA,CAAAuD,UAAA,KAAA8I,sCAAA,oBA8GM;UAGNrM,EAAA,CAAAuD,UAAA,KAAA+I,sCAAA,kBAOM;UAGNtM,EAAA,CAAAuD,UAAA,KAAAgJ,sCAAA,kBAOM;UACRvM,EAAA,CAAAgB,YAAA,EAAM;;;UA1QgBhB,EAAA,CAAAoB,SAAA,IAKf;UALepB,EAAA,CAAA0D,UAAA,YAAA1D,EAAA,CAAAwM,eAAA,IAAAC,GAAA,EAAAzM,EAAA,CAAA0M,eAAA,IAAAC,GAAA,GAAA3M,EAAA,CAAA0M,eAAA,IAAAE,GAAA,GAAA5M,EAAA,CAAA0M,eAAA,IAAAG,GAAA,GAAA7M,EAAA,CAAA0M,eAAA,IAAAI,GAAA,GAKf;UAiBC9M,EAAA,CAAAoB,SAAA,GAA4B;UAA5BpB,EAAA,CAAA0D,UAAA,SAAAsI,GAAA,CAAA7K,SAAA,cAA4B;UAiH5BnB,EAAA,CAAAoB,SAAA,GAA2B;UAA3BpB,EAAA,CAAA0D,UAAA,SAAAsI,GAAA,CAAA7K,SAAA,aAA2B;UAiH3BnB,EAAA,CAAAoB,SAAA,GAA8B;UAA9BpB,EAAA,CAAA0D,UAAA,SAAAsI,GAAA,CAAA7K,SAAA,gBAA8B;UAU9BnB,EAAA,CAAAoB,SAAA,GAA4B;UAA5BpB,EAAA,CAAA0D,UAAA,SAAAsI,GAAA,CAAA7K,SAAA,cAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}